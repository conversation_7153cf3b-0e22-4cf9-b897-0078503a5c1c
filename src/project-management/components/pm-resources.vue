<script setup>
import { storeToRefs } from 'pinia';
import { useModal, useModalSlot } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import TableWrapper from '~/common/components/organisms/hawk-table/table.wrapper.vue';
import PmBulkResourcesModal from '~/project-management/components/pm-bulk-resources-modal.vue';
import PmDeleteResourceContent from '~/project-management/components/pm-delete-resource-content.vue';
import PmUserMappingModal from '~/project-management/components/pm-user-mapping-modal.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const $t = inject('$t');
const $toast = inject('$toast');

const project_management_store = useProjectManagementStore();
const { save_resources } = project_management_store;
const { active_schedule, active_schedule_data } = storeToRefs(project_management_store);

const new_resource_modal = useModal({
  component: PmBulkResourcesModal,
  attrs: {
    onClose: () => {
      new_resource_modal.close();
    },
  },
});

const user_mapping_modal = useModal({
  component: PmUserMappingModal,
  attrs: {
    onClose: () => {
      user_mapping_modal.close();
    },
  },
});

const state = reactive({
  active_item: 'members',
  resource_members: [],
  selected_rows: [],
  freeze_table: -1,
  is_saving: false,
  clear_tracking: false,
  reload_count: 0,
});

const table_columns = computed(() => {
  return [
    {
      accessorKey: 'select',
      header: '',
      id: 'select',
      size: '5',
      enableSorting: false,
      enableResizing: false,
      show_on_hover: 'true',
      static: true,
    },
    {
      header: $t('Resource'),
      accessorKey: 'resource',
      id: 'resource',
      cell: info => info.getValue(),
    },
    {
      header: $t('Type'),
      accessorKey: 'type',
      id: 'type',
      cell: info => info.getValue(),
    },
    {
      header: $t('Access level'),
      accessorKey: 'access_level',
      id: 'access_level',
      cell: info => info.getValue(),
    },
    ...(active_schedule.value.track_costs
      ? [
          {
            header: $t('Cost'),
            accessorKey: 'cost',
            id: 'cost',
            cell: info => info.getValue(),
          },
          {
            header: $t('Unit'),
            accessorKey: 'unit',
            id: 'unit',
            cell: info => info.getValue(),
          },
        ]
      : []),
    ...(active_schedule.value.actions.modify_resources
      ? [
          {
            accessorKey: 'context_menu',
            header: '',
            id: 'context_menu',
            size: '5',
            show_on_hover: 'true',
          },
        ]
      : []),
  ];
});

const existing_members = computed(() => {
  return active_schedule.value.resources.filter(member => member.type === 'member').map(item => item.external_id);
});

const existing_custom_resources = computed(() => {
  return active_schedule.value.resources.filter(resource => resource.type === 'custom').map(item => item.name);
});

const delete_popup = useModal({
  component: HawkDeletePopup,
});

function onAddResources() {
  new_resource_modal.patchOptions({
    attrs: {
      mode: 'create',
      prefillData: {},
      existingMembers: existing_members.value,
      existingCustomResources: existing_custom_resources.value,
      save: async (payload) => {
        const added_resource_payload = {
          ...(payload.resource_name?.name === payload.resource_name?.uid ? { name: payload.resource_name.name } : {}),
          ...(payload.resource_name?.name !== payload.resource_name?.uid ? { external_id: payload.resource_name.uid } : {}),
          type: payload.resource_name?.name === payload.resource_name?.uid ? 'custom' : 'member',
          cost: Number.parseFloat(payload.cost),
          cost_type: payload.cost_type,
          permission: payload.access_level,
        };
        await save_resources([added_resource_payload], [], []);
        if (added_resource_payload.type === 'member') {
          active_schedule.value.members[added_resource_payload.external_id] = added_resource_payload.permission;
        }
      },
    },
  });
  new_resource_modal.open();
}

function onEditResource(data) {
  const permission_map = {
    read: `${$t('Viewer')}`,
    manage: `${$t('Manager')}`,
    write: `${$t('Admin')}`,
  };
  const cost_type_map = {
    per_hour: `${$t('Per hour')}`,
    per_item: `${$t('Per item')}`,
    fixed: `${$t('Fixed')}`,
  };
  const prefill_data = data.map((item) => {
    return {
      uid: item.uid,
      name: item.name ?? item.external_id,
      type: item.type === 'custom' ? 'Custom' : 'Member',
      permission: permission_map?.[active_schedule.value.members?.[item?.external_id]],
      cost_type: cost_type_map?.[item.cost_type],
      cost: item.cost,
    };
  });
  new_resource_modal.patchOptions({
    attrs: {
      mode: 'edit',
      prefillData: prefill_data,
      existingMembers: [],
      existingCustomResources: existing_custom_resources.value,
      onSave: () => {
        state.reload_count++;
        state.selected_rows = [];
        new_resource_modal.close();
      },
    },
  });
  new_resource_modal.open();
}

function onConvertToMember(data) {
  user_mapping_modal.patchOptions({
    attrs: {
      prefillData: data,
      existingMembers: existing_members.value,
    },
  });
  user_mapping_modal.open();
}

function handleSelectRow(e) {
  state.selected_rows = e;
}

async function handleDelete(data) {
  state.clear_tracking = false;
  const data_uids = data.map(item => item.uid);
  const has_trackings = active_schedule.value.trackings.filter(tracking => data_uids.includes(tracking.resource));
  delete_popup.patchOptions(
    {
      ...(active_schedule_data.value?.data?.[0]?.actions?.clear_trackings && has_trackings.length
        ? {
            slots: {
              content: useModalSlot({
                component: PmDeleteResourceContent,
                attrs: {
                  onChange(event) {
                    state.clear_tracking = event;
                  },
                },
              }),
            },
          }
        : {
            slots: {
              content: `
                <div class="text-sm font-normal text-gray-600 mb-6">
                  ${$t('Are you sure you want to delete? This action cannot be undone.')}
                </div>
              `,
            },
          }
      ),
      attrs: {
        header: data.length > 1 ? $t('Delete resources') : $t('Delete resource'),
        button_text: $t('Delete'),
        onClose() {
          delete_popup.close();
        },
        confirm: async () => {
          try {
            const delete_payload = [];
            data.forEach((item) => {
              delete_payload.push({
                uid: item.uid,
                clear_tracking: state.clear_tracking,
              });
            });
            await save_resources([], [], delete_payload);
            active_schedule.value.resources = active_schedule.value.resources.filter(resource => resource.uid !== data.uid);
            if (state.clear_tracking)
              active_schedule.value.trackings = active_schedule.value.trackings.filter(tracking => tracking.resource !== data.uid);
          }
          catch (err) {
            logger.error(err);
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again'),
              type: 'error',
            });
          }
          finally {
            state.selected_rows = [];
            delete_popup.close();
          }
        },
      },
    },
  );
  delete_popup.open();
}

watch(() => active_schedule.value.track_costs, () => {
  state.reload_count++;
});
</script>

<template>
  <div class="px-4 pb-6">
    <div class="flex items-center justify-between mt-2 mb-4">
      <div class="w-2/3 text-sm font-normal text-gray-600">
        {{ $t('pm-resources-tab-description') }}
      </div>
      <template v-if="active_schedule.actions.modify_resources">
        <HawkMenu
          v-if="state.selected_rows.length"
          :items="[
            {
              label: $t('Update resources'),
              value: 'update',
              on_click: () => {
                onEditResource(state.selected_rows.map(row => row.original));
              },
            },
            {
              label: $t('Delete resources'),
              value: 'delete',
              on_click: () => {
                handleDelete(state.selected_rows.map(row => row.original));
              },
            },
          ]"
        >
          <template #trigger="{ open }">
            <HawkButton type="outlined">
              {{ $t('Actions') }}
              <IconHawkChevronUp v-if="open" />
              <IconHawkChevronDown v-else />
            </HawkButton>
          </template>
        </HawkMenu>
        <HawkButton v-else type="text" @click="onAddResources">
          <IconHawkPlus />
          {{ $t('Add resources') }}
        </HawkButton>
      </template>
    </div>
    <TableWrapper container_class="!mt-0 border-0 !h-[calc(100vh-270px)]">
      <HawkTable
        :key="active_schedule.resources.length + state.reload_count"
        :non_sortable_columns="['resource', 'access_level', 'unit']"
        :freeze_table="state.freeze_table"
        :data="active_schedule.resources"
        :columns="table_columns"
        :is_gapless="true"
        :show_menu_header="false"
        :disable_resize="true"
        :header_grid_lines="{
          horizontal: true,
          vertical: true,
        }"
        :data_grid_lines="{
          horizontal: true,
          vertical: true,
        }"
        cell_height="32px"
        additional_table_classes="shadow-sm"
        @select-row="handleSelectRow"
      >
        <template #resource="data">
          <template v-if="data.data.row.original.type === 'custom'">
            <div class="w-4 h-4 flex items-center justify-center bg-gray-100 text-xs font-medium text-gray-600 rounded-full mr-1.5">
              {{ data.data.row.original.name.charAt(0) }}
            </div>
            <span class="text-xs font-medium text-gray-700">
              {{ data.data.row.original.name }}
            </span>
          </template>
          <template v-else-if="data.data.row.original.type === 'member'">
            <HawkMembers :members="data.data.row.original.external_id" type="label" size="tiny" />
          </template>
        </template>
        <template #type="data">
          <div class="text-xs font-normal text-gray-700">
            <template v-if="data.data.row.original.type === 'member'">
              {{ $t('Member') }}
            </template>
            <template v-else>
              {{ $t('Custom') }}
            </template>
          </div>
        </template>
        <template #access_level="data">
          <div class="text-xs font-normal text-gray-700">
            <template v-if="data.data.row.original.type === 'member'">
              <template v-if="active_schedule.members[data.data.row.original.external_id] === 'read'">
                {{ $t('Viewer') }}
              </template>
              <template v-else-if="active_schedule.members[data.data.row.original.external_id] === 'manage'">
                {{ $t('Manager') }}
              </template>
              <template v-else-if="active_schedule.members[data.data.row.original.external_id] === 'write'">
                {{ $t('Admin') }}
              </template>
            </template>
            <template v-else>
              &ndash;
            </template>
          </div>
        </template>
        <template #cost="data">
          <div v-if="data.data.row.original.cost" class="text-xs font-normal text-gray-700">
            {{ active_schedule?.currency?.symbol }}
            {{ new Intl.NumberFormat('en-US').format(data.data.row.original.cost) }}
          </div>
          <div v-else>
            &ndash;
          </div>
        </template>
        <template #unit="data">
          <div class="text-xs font-normal text-gray-600">
            <template v-if="data.data.row.original.cost_type === 'per_hour'">
              {{ $t('Per hour') }}
            </template>
            <template v-else-if="data.data.row.original.cost_type === 'fixed'">
              {{ $t('Fixed') }}
            </template>
            <template v-else-if="data.data.row.original.cost_type === 'per_item'">
              {{ $t('Per item') }}
            </template>
          </div>
        </template>
        <template #context_menu="data">
          <HawkMenu
            :items="[
              {
                label: $t('Update'),
                value: 'update',
                on_click: () => {
                  onEditResource([data.data.row.original]);
                },
              },
              ...(data.data.row.original.type === 'custom' && data.data.row.original.cost_type !== 'per_item' ? [
                {
                  label: $t('Convert to member'),
                  value: 'convert_to_member',
                  on_click: () => {
                    onConvertToMember(data.data.row.original);
                  },
                },
              ] : []),
              {
                label: $t('Delete'),
                value: 'delete',
                on_click: () => {
                  handleDelete([data.data.row.original]);
                },
              },
            ]"
            position="fixed"
            additional_trigger_classes="!ring-0 !flex !items-center"
            @click.stop=""
            @open="state.freeze_table = data.data?.row?.id"
            @close="state.freeze_table = '-1'"
          >
            <template #trigger>
              <IconHawkDotsVertical class="flex items-center text-gray-600" />
            </template>
          </HawkMenu>
        </template>
      </HawkTable>
    </TableWrapper>
  </div>
</template>
