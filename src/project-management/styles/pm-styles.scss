.pm-gantt {
  $icon_chevron_right: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 18L15 12L9 6" stroke="%23475467" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E');
  $icon_chevron_down: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M4 6L8 10L12 6" stroke="%23475467" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E');
  $icon_plus: url('data:image/svg+xml,%3Csvg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M6.99984 1.1665V12.8332M1.1665 6.99984H12.8332" stroke="%23475467" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E');
  $icon_drag: url('data:image/svg+xml,<svg width="24" height="17" viewBox="0 0 24 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g filter="url(%23filter0_d_16994_18623)"><path d="M12 10H4.53229C4.34762 10 4.26161 9.77109 4.40059 9.64949L11.8683 3.11524C11.9437 3.04926 12.0563 3.04926 12.1317 3.11524L19.5994 9.64949C19.7384 9.77109 19.6524 10 19.4677 10H12Z" fill="white"/><path d="M12 10H4.53229C4.34762 10 4.26161 9.77109 4.40059 9.64949L11.8683 3.11524C11.9437 3.04926 12.0563 3.04926 12.1317 3.11524L19.5994 9.64949C19.7384 9.77109 19.6524 10 19.4677 10H12Z" stroke="%23D0D5DD" stroke-width="0.5"/></g><defs><filter id="filter0_d_16994_18623" x="0.0814209" y="0.81543" width="23.8372" height="15.4346" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2"/><feGaussianBlur stdDeviation="2"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_16994_18623"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_16994_18623" result="shape"/></filter></defs></svg>');
  $icon_loading_children: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 12C20 13.0506 19.7931 14.0909 19.391 15.0615C18.989 16.0321 18.3997 16.914 17.6569 17.6569C16.914 18.3997 16.0321 18.989 15.0615 19.391C14.0909 19.7931 13.0506 20 12 20C10.9494 20 9.90914 19.7931 8.93853 19.391C7.96793 18.989 7.08601 18.3997 6.34314 17.6569C5.60028 16.914 5.011 16.0321 4.60896 15.0615C4.20693 14.0909 4 13.0506 4 12C4 10.9494 4.20693 9.90913 4.60896 8.93853C5.011 7.96793 5.60028 7.08601 6.34315 6.34314C7.08602 5.60027 7.96793 5.011 8.93854 4.60896C9.90914 4.20693 10.9494 4 12 4C13.0506 4 14.0909 4.20693 15.0615 4.60897C16.0321 5.011 16.914 5.60028 17.6569 6.34315C18.3997 7.08602 18.989 7.96793 19.391 8.93854C19.7931 9.90914 20 10.9494 20 12L20 12Z' stroke='white' stroke-width='2.28571' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M6.34315 6.34314C7.23586 5.45043 8.32717 4.78168 9.52787 4.39155C10.7286 4.00142 12.0045 3.901 13.2515 4.09849C14.4984 4.29599 15.6809 4.7858 16.7023 5.52787C17.7237 6.26993 18.5549 7.2432 19.1281 8.36808C19.7012 9.49296 20 10.7375 20 12C20 13.2625 19.7012 14.507 19.1281 15.6319C18.5549 16.7568 17.7237 17.7301 16.7023 18.4721C15.6809 19.2142 14.4984 19.704 13.2515 19.9015' stroke='%231570EF' stroke-width='2.28571' stroke-linecap='round' stroke-linejoin='round'%3E%3CanimateTransform attributeName='transform' attributeType='XML' type='rotate' from='0 12 12' to='360 12 12' dur='1s' repeatCount='indefinite' /%3E%3C/path%3E%3C/svg%3E%0A");
  // $icon_constraint_arrow: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M11.59 7.41L15.17 11H1v2h14.17l-3.59 3.59L13 18l6-6-6-6-1.41 1.41zM20 6v12h2V6h-2z'/%3E%3C/svg%3E");

  *:focus {
    outline: none;
  }

  *:focus-visible {
    outline: none;
  }

  .gantt_container {
    font-family: "Inter", sans-serif;
  }

  .gantt_task .gantt_task_scale .gantt_scale_cell,
  .gantt_grid .gantt_grid_scale .gantt_grid_head_cell {
    text-transform: none;
  }

  .gantt_task_content,
  .gantt_side_content,
  .gantt_grid_scale,
  .gantt_task_scale,
  .gantt_cell {
    font-size: 12px;
  }

  .gantt_side_content.gantt_link_crossing {
    @apply top-0;
  }

  .gantt_side_content.gantt_right {
    @apply pl-5;
  }

  .gantt_bar_WBS .gantt_side_content.gantt_right,
  .gantt_bar_PROJECT .gantt_side_content.gantt_right {
    margin-top: -12px;
    left: -21px;
  }

  .gantt_bar_WBS,
  .gantt_bar_PROJECT {
    margin-top: 6px;
  }

  .gantt_row_WBS .gantt_cell,
  .gantt_row_PROJECT .gantt_cell, {
    @apply font-semibold;
  }

  .gantt_bar_WBS .gantt_link_control,
  .gantt_bar_PROJECT .gantt_link_control {
    margin-top: -7px;
  }

  .gantt_link_control.task_left {
    margin-left: -3.5px;
  }

  .gantt_link_control.task_right {
    margin-right: -3.5px;
  }

  .gantt_tree_content {
    width: 100%;
  }

  [data-column-name="weight"] .gantt_tree_content {
    @apply text-left px-1;
  }

  .gantt_tree_icon.gantt_open,
  .gantt_tree_icon.gantt_close {
    @apply -ml-1 mr-0.75 cursor-pointer w-4;
  }

  .gantt_tree_icon.gantt_file {
    @apply hidden;
    background-image: $icon_chevron_right;
  }
  .gantt_tree_icon.gantt_folder_closed {
    @apply hidden;
    background-image: $icon_chevron_right;
  }
  .gantt_tree_icon.gantt_folder_open {
    @apply hidden;
    background-image: $icon_chevron_down;
  }

  .gantt_tree_icon.gantt_open {
    background-image: $icon_chevron_right;
  }

  .gantt_tree_icon.gantt_close {
    background-image: $icon_chevron_down;
  }

  .gantt_tree_icon.gantt_blank {
    @apply w-0;
  }

  .gantt_tree_icon.gantt_loading_children {
    background-image: $icon_loading_children;
    background-size: 16px 16px;
    width: 12px;
    cursor: pointer;
    margin-right: 3px;
  }

  .gantt_grid_data .gantt_row.odd:hover,
  .gantt_grid_data .gantt_row:hover {
    @apply bg-gray-50;
  }

  .gantt_grid_data .gantt_row.gantt_selected,
  .gantt_grid_data .gantt_row.odd.gantt_selected,
  .gantt_task_row.gantt_selected {
    @apply bg-primary-50;
  }

  .show-activity-name-icon-on-hover {
    @apply hidden;
  }

  .gantt_grid_data .gantt_row.odd:hover .show-activity-name-icon-on-hover,
  .gantt_grid_data .gantt_row:hover .show-activity-name-icon-on-hover {
    @apply block;
  }

  .show-on-row-hover {
    @apply opacity-0;
  }

  .gantt_grid_data .gantt_row.odd:hover .show-on-row-hover,
  .gantt_grid_data .gantt_row:hover .show-on-row-hover {
    @apply opacity-100;
  }

  .visible-on-row-hover {
    @apply hidden;
  }

  .gantt_grid_data .gantt_row.odd:hover .visible-on-row-hover,
  .gantt_grid_data .gantt_row:hover .visible-on-row-hover {
    @apply block;
  }

  .hide-on-row-hover {
    @apply block;
  }

  .gantt_grid_data .gantt_row.odd:hover .hide-on-row-hover,
  .gantt_grid_data .gantt_row:hover .hide-on-row-hover {
    @apply hidden;
  }

  .gantt_add,
  .gantt_grid_head_add {
    background-image: $icon_plus;
  }

  .gantt_grid_head_name, .gantt_grid_head_tasks, .gantt_grid_head_progress {
    text-align: left;
    padding-left: 16px;
  }

  .gantt_grid_column_resize_wrap {
    @apply opacity-100;
  }

  .gantt_task_line .gantt_side_content,
  .gantt_task_line.gantt_MILESTONE .gantt_side_content {
    @apply text-gray-600;
    overflow: visible;
  }

  .gantt_bar_PROJECT .gantt_side_content,
  .gantt_bar_WBS .gantt_side_content {
    @apply font-semibold;
  }

  .gantt_task_line.gantt_critical_task.gantt_MILESTONE,
  .gantt_task_line.gantt_critical_task.gantt_PROJECT {
    @apply bg-red-400;
  }

  .gantt_task_line.pm-completed.gantt_milestone,
  .gantt_task_line.pm-completed.gantt_project {
    @apply border-success-400
  }

  .gantt_grid_scale,
  .gantt_task_scale,
  .gantt_task_vscroll {
    @apply border-gray-200;
  }

  div.gantt_grid_scale:after,
  div.gantt_scale_line:last-child:after {
    @apply shadow-none;
  }

  .gantt_link_line_down,
  .gantt_link_line_left,
  .gantt_link_line_right,
  .gantt_link_line_up,
  .gantt_grid_data .gantt_row,
  .gantt_grid_data .gantt_row.odd {
    @apply transition-none;
  }

  input {
    @apply text-xs;
  }

  .gantt_grid_editor_placeholder {
    @apply bg-gray-300/20;
  }
  .gantt_grid_editor_placeholder select {
    @apply px-[15px] #{!important};
  }
  .gantt_grid_editor_placeholder select,
  .gantt_grid_editor_placeholder input:not([name="text"]) {
    @apply text-xs px-1.5;
    border: 1px solid dodgerblue;
  }

  .gantt_cell {
    border-bottom: 1px solid var(--vf-gray-200);
    border-right: 1px solid var(--vf-gray-200);
  }

  .gantt_row_SURROGATE .gantt_cell {
    @apply border-r-0;
  }

  .gantt_row_SURROGATE .gantt_cell:nth-last-child(2) {
    @apply border-r;
  }

  .gantt_grid_data .gantt_last_cell,
  .gantt_grid_scale .gantt_last_cell,
  .gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell,
  .gantt_task_bg .gantt_last_cell {
    @apply border-r-0;
  }

  .gantt_task_line {
    @apply bg-primary-400 border-primary-400 rounded;
  }

  .gantt_task_progress {
    @apply bg-primary-600 border-primary-600;
  }

  .gantt_bar_WBS_orange_color {
    @apply bg-orange-300 border-orange-300 rounded #{!important};
  }

  .gantt_bar_WBS_orange_color .gantt_task_progress {
    @apply bg-orange-400 border-orange-400 #{!important};
  }

  .gantt_bar_PROJECT {
    @apply bg-gray-400 border-gray-400 rounded;
  }

  .gantt_bar_PROJECT .gantt_task_progress {
    @apply bg-gray-600 border-gray-600;
  }

  .gantt_MILESTONE {
    @apply bg-fuchsia-400 border-fuchsia-400;
  }

  .gantt_critical_task {
    @apply bg-error-500 border-error-500;
  }

  .gantt_critical_task .gantt_task_line {
    @apply bg-error-400 border-error-400;
  }

  .gantt_critical_task .gantt_task_progress {
    @apply bg-error-600 border-error-600;
  }

  .pm-completed {
    @apply bg-success-400 border-success-400;
  }

  .pm-completed .gantt_task_line {
    @apply bg-success-400 border-success-400;
  }

  .pm-completed .gantt_task_progress {
    @apply bg-success-400 border-success-400;
  }

  .gantt_task_line.pm-completed.gantt_MILESTONE,
  .gantt_task_line.pm-completed.gantt_PROJECT {
    @apply bg-success-400 #{!important};
  }

  .pm-completed-link .gantt_line_wrapper>div {
    @apply bg-success-400;
  }

  .pm-completed-link .gantt_link_arrow {
    @apply border-success-400;
  }

  .gantt_task_link.pm-completed-link:hover .gantt_line_wrapper div {
    box-shadow: 0 2px 4px 0 rgba(50, 213, 131, .2)
  }

  .gantt_task_link.pm-completed-link:hover .gantt_link_arrow_left:before,
  .gantt_task_link.pm-completed-link:hover .gantt_link_arrow_right:before {
    border-color: transparent;
    box-shadow: 0 2px 3px 0 rgba(50, 213, 131, .4), 0 1px 1px 0 rgba(50, 213, 131, .25)
  }

  .pm-partial-critical-link .gantt_line_wrapper>div {
    @apply bg-[#87a4bc];
  }

  .pm-partial-critical-link .gantt_link_arrow {
    @apply border-[#87a4bc];
  }

  .gantt_task_link.pm-partial-critical-link:hover .gantt_line_wrapper div {
    box-shadow: 0 2px 4px 0 rgb(135, 164, 188, .2);
  }

  .gantt_task_link.pm-partial-critical-link:hover .gantt_link_arrow_left:before,
  .gantt_task_link.pm-partial-critical-link:hover .gantt_link_arrow_right:before {
    border-color: transparent;
    box-shadow: 0 2px 3px 0 rgba(135, 164, 188, .4), 0 1px 1px 0 rgba(135, 164, 188, .25)
  }

  .gantt_task_drag.task_left {
    @apply left-0;
  }

  .gantt_task_drag.task_right {
    @apply right-0;
  }

  .gantt_task_line.gantt_thin_task .gantt_task_drag.task_end_date {
    @apply left-[5px];
  }

  .gantt_task_line.gantt_thin_task .gantt_task_drag.task_start_date {
    @apply -left-[5px];
  }

  .gantt_task_drag {
    background: none;
    @apply bg-repeat-space;
    background-size: 2px;
  }

  .drag_date {
    @apply z-1 text-center text-gray-800;
  }

  .drag_date.drag_move_start {
    @apply -ml-7 text-xs;
  }

  .drag_date.drag_move_end {
    @apply ml-7 text-xs;
  }

  .drag_move_vertical, .drag_move_horizontal {
    @apply bg-slate-100 opacity-50 box-border;
  }

  .drag_move_vertical {
    @apply border-l border-r border-slate-400;
  }

  .drag_move_horizontal {
    @apply border-t border-b border-slate-400;
  }

  .off-time {
    @apply bg-red-50;
  }

  .today_scale_cell_class {
    @apply bg-primary-500;
    @apply text-white #{!important};
  }

  .gantt_grid_data {
    @apply border-r border-gray-200;
  }

  .gantt_task_line.gantt_MILESTONE .gantt_task_content {
    scale: 1.1;
  }

  .gantt_task_progress_drag,
  .gantt_task_progress_drag:hover {
    background-image: $icon_drag;
    transform: translateX(1.5px);
  }

  .gantt_cell.gantt_cell_tree {
    @apply pl-4;
  }

  .pm-highlighted-grey {
    // important is needed to overwrite the hover color
    @apply bg-gray-100 #{!important};
  }

  .pm-highlighted {
    // important is needed to overwrite the hover color
    @apply bg-warning-100 #{!important};
  }

  .pm-selected {
    // important is needed to overwrite the hover color
    @apply bg-primary-50 #{!important};
  }

  .gantt_row.gantt_selected .select-trigger {
    @apply opacity-100;
  }

  .resourceGrid_cell {
    @apply border-r-0;
  }

  .resourceGrid_cell .gantt_grid_head_cell:not(:last-child) {
    border-right: 1px solid var(--vf-gray-200);
  }

  .gantt_grid_head_cell:not(.gantt_last_cell) {
    border-right: 1px solid var(--vf-gray-200) !important;
  }

  .highlighted_resource {
    @apply bg-primary-100;
  }

  [data-column-name="select-columns"] {
    @apply border-none;
  }

  .resource_marker{
    @apply text-center;
  }
  .resource_marker div{
    @apply w-[calc(100%-2px)] h-[29px] inline-flex items-center justify-center text-xs;
    @apply relative -top-0.5;
  }
  .resource_marker.workday_ok div {
    @apply bg-success-50 text-success-600;
  }

  .resource_marker.workday_over div{
    @apply bg-error-50 text-error-600;
  }

  .resource_marker.workday_neutral div{
    @apply bg-transparent;
    color: rgba(0, 0, 0, .54);
  }

  .owner-label{
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    display: inline-block;
    border: 1px solid #cccccc;
    border-radius: 25px;
    background: #e6e6e6;
    color: #6f6f6f;
    margin: 0 3px;
    font-weight: bold;
  }

  .slack {
    position: absolute;
    opacity: 0.7;
    height: 24.5px !important;
    margin-top: -11px;
    border: none;
    border-radius: 4px;
    border-right: 1px solid rgb(228, 174, 58);
    background-image: repeating-linear-gradient(
      45deg,
      rgb(228, 174, 58) 0,
      rgb(228, 174, 58) 1px,
      transparent 0,
      transparent 50%
    );
    background-size: 5px 5px;
    background-color: transparent;
  }

  .baseline {
    position: absolute;
    border-radius: 4px;
    height: 8px;
    background: rgb(255, 236, 129);
    border: none;
  }

  .resource-controls {
    @apply pt-2.25 pl-3;

    label {
      @apply text-gray-500 font-medium;
    }

    input {
      @apply w-3 h-3 rounded-full bg-white border border-gray-500;

      &:checked {
        @apply bg-primary-200 border-primary-500;
      }
    }

    .gantt_layout_content > .flex > .flex.active > label {
      @apply underline underline-offset-4;
    }
  }

  select[name="constraint_type"] {
    text-align: center;
  }

  input[type="date"],
  input[name="duration"],
  input[name="progress"],
  input[name="predecessors"],
  input[name^="custom_field_"] {
    text-align: center;
    border: 1px solid dodgerblue;
  }

  [data-column-name$="_date"] div {
    width: 100% !important;
  }

  .gantt_task_progress_drag {
    @apply hidden #{!important};
  }

  .gantt_marker_content {
    @apply rounded-r;
  }

  .gantt_marker .gantt_marker_content {
    @apply opacity-70;
  }

  .gantt_marker:hover .gantt_marker_content {
    @apply opacity-100 z-[1000];
  }

  .today-marker {
    @apply bg-primary-500;
  }

  .data-date-marker {
    @apply bg-success-500;
  }

  .deadline-marker {
    @apply bg-error-500;
  }

  // .constraint-marker {
  //   position: absolute;

  //   -moz-box-sizing: border-box;
  //   box-sizing: border-box;

  //   width: 20px;
  //   height: 20px;
  //   margin-top: 5px;

  //   opacity: 0.4;
  //   z-index: 1;
  //   background-image: $icon_constraint_arrow;
  //   background-size: cover;
  //   background-repeat: no-repeat;
  //   background-position: center;
  // }

  // .constraint-marker.latest-end {
  //   transform: rotate(180deg);
  //   margin-left: -18px;
  // }
}

.pm-gantt--readonly {
  .gantt_link_control {
    @apply hidden;
  }
  .gantt_task_drag {
    @apply hidden #{!important};
  }
  .gantt_task_link:hover .gantt_line_wrapper div{
    @apply shadow-none #{!important};
  }
}

.pm-gantt--no-links {
  .gantt_link_control {
    @apply hidden;
  }
}

.gantt_link_tooltip {
  @apply shadow-md
         border-t
         border-t-gray-200
         border-l
         border-l-gray-200
         bg-white
         border-none
         text-sm
         text-gray-800
         p-3;
}

.gantt_tooltip {
  @apply shadow-md
         whitespace-nowrap
         z-50
         absolute
         p-3
         text-ellipsis
         max-w-96
         overflow-hidden
         text-sm
         rounded
         border-none
         text-white
         bg-gray-800;
}

// tippy theme for the assignee editor
.tippy-box[data-theme~='pm-resource-editor'] {
  @apply shadow-lg border rounded bg-white text-gray-800;
}
.tippy-box[data-theme~='pm-resource-editor'][data-placement^='top'] > .tippy-arrow::before {
  @apply border-t-white;
}
.tippy-box[data-theme~='pm-resource-editor'][data-placement^='bottom'] > .tippy-arrow::before {
  @apply border-b-white;
}
.tippy-box[data-theme~='pm-resource-editor'][data-placement^='left'] > .tippy-arrow::before {
  @apply border-l-white;
}
.tippy-box[data-theme~='pm-resource-editor'][data-placement^='right'] > .tippy-arrow::before {
  @apply border-r-white;
}
.tippy-box[data-theme~='pm-resource-editor'] .tippy-content {
  @apply p-1;
}
.tippy-box[data-theme~='pm-resource-editor']::-webkit-scrollbar {
  @apply w-2 h-2;
}
.tippy-box[data-theme~='pm-resource-editor']::-webkit-scrollbar-thumb {
  @apply bg-gray-300 bg-clip-padding rounded-lg;
}
.tippy-box[data-theme~='pm-resource-editor']:hover::-webkit-scrollbar-thumb {
  @apply bg-gray-300;
}
.tippy-box[data-theme~='pm-resource-editor']:hover::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

// tippy theme for the date editor
.tippy-box[data-theme~='pm-date-editor'] {
  @apply shadow-lg rounded-lg bg-transparent text-gray-800;
}
.tippy-box[data-theme~='pm-date-editor'][data-placement^='top'] > .tippy-arrow::before {
  @apply border-t-0;
}
.tippy-box[data-theme~='pm-date-editor'][data-placement^='bottom'] > .tippy-arrow::before {
  @apply border-b-0;
}
.tippy-box[data-theme~='pm-date-editor'][data-placement^='left'] > .tippy-arrow::before {
  @apply border-l-0;
}
.tippy-box[data-theme~='pm-date-editor'][data-placement^='right'] > .tippy-arrow::before {
  @apply border-r-0;
}
.tippy-box[data-theme~='pm-date-editor'] .tippy-content {
  @apply p-0;
}
.tippy-box[data-theme~='pm-date-editor']::-webkit-scrollbar {
  @apply w-2 h-2;
}
.tippy-box[data-theme~='pm-date-editor']::-webkit-scrollbar-thumb {
  @apply bg-gray-300 bg-clip-padding rounded-lg;
}
.tippy-box[data-theme~='pm-date-editor']:hover::-webkit-scrollbar-thumb {
  @apply bg-gray-300;
}
.tippy-box[data-theme~='pm-date-editor']:hover::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

// tippy theme for the weight editor
.tippy-box[data-theme~='small-text'] {
  @apply text-xs;
}
