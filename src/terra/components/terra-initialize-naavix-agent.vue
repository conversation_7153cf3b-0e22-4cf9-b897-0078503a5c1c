<script setup>
import { sortBy } from 'lodash-es';
import { onMounted, watch } from 'vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import useEmitter from '~/common/composables/useEmitter';
import TerraCharts from '~/terra/components/charts/terra-charts.vue';
import { useExportTerraReport } from '~/terra/utils/terra-report-export.composable.js';
import { useThermCustomReport } from '~/therm/composables/therm-custom-report.js';

const { $services, route, common_store, auth_store } = useCommonImports();
const { getFormattedData, getFieldValues, formattedDate } = useExportTerraReport();
const { uploadFiles } = useThermCustomReport();

const emitter = useEmitter();

const terra_charts_ref$ = ref(null);
const state = reactive({
  group_workflow_project: {},
  progress_data: [],
  upload_status: 'not_started',
  workflows_data: [],
});

const progress_data_columns = [
  { key: 'number', header: 'Number' },
  { key: 'id', header: 'ID' },
  { key: 'activity', header: 'Activity', width: 20 },
  { key: 'workflow', header: 'Activity category', width: 20 },
  { key: 'layer', header: 'Location category', width: 20 },
  { key: 'sublayer', header: 'Location', width: 20 },
  { key: 'uom', header: 'UOM' },
  { key: 'scope', header: 'Scope' },
  { key: 'completed', header: 'Completed', width: 20 },
  { key: 'actual_start', header: 'Actual Start', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
  { key: 'actual_finish', header: 'Actual Finish', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
  { key: 'remaining', header: 'Remaining' },
  { key: 'progress', header: '% Progress' },
  { key: 'status', width: 12, header: 'Status' },
  { key: 'planned_start', header: 'Planned Start', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
  { key: 'planned_finish', header: 'Planned Finish', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
  { key: 'expected_work_rate', header: 'Expected Work rate', width: 15 },
  { key: 'est_finish_date', header: 'Est. Finish date', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
];

const progress_history_columns = [
  { key: 'workflow', header: 'Activity category', width: 20 },
  { key: 'activity', header: 'Activity', width: 20 },
  { key: 'sublayer', header: 'Location', width: 20 },
  { key: 'layer', header: 'Location category', width: 20 },
  { key: 'date', header: 'Date', width: 15, style: { numFmt: 'DD/MM/YYYY' } },
  { key: 'value', header: 'Work done' },
];

function addProgressDataSheet(workbook) {
  const worksheet = workbook.addWorksheet('progress_data');
  worksheet.columns = progress_data_columns;

  state.workflows_data = [];
  Object.values(state.group_workflow_project).forEach((projects_map, workflow_index) => {
    Object.values(projects_map).forEach((fields, project_index) => {
      const fields_asc = sortBy(fields, [o => o.field.toLowerCase()]);
      fields_asc.forEach((field, field_index) => {
        const row = getFieldValues(field);
        row.number = `${workflow_index + 1}.${project_index + 1}.${field_index + 1}`;
        state.workflows_data.push(row);
        worksheet.addRow(row);
      });
    });
  });
}

function addProgressHistorySheet(workbook) {
  const worksheet = workbook.addWorksheet('progress_history_simplified');
  worksheet.columns = progress_history_columns;
  worksheet.addRows(state.progress_data.custom_agent_data);

  const progress_history_worksheet = workbook.addWorksheet('progress_history');
  progress_history_worksheet.columns = progress_history_columns;
  const dates = state.progress_data.custom_agent_data.map(item => item.actual_date);
  const unique_dates = [...new Set(dates)];
  unique_dates.forEach((date) => {
    const formatted_date = formattedDate(date);
    state.workflows_data.forEach((row) => {
      const row_data = {
        workflow: row.workflow,
        activity: row.activity,
        sublayer: row.sublayer,
        layer: row.layer,
        value: state.progress_data?.[row.workflow]?.[row.sublayer]?.[row.activity]?.[date] || 0,
        date: formatted_date,
      };
      progress_history_worksheet.addRow(row_data);
    });
  });
}

async function uploadChartsDataExcel() {
  const ExcelJS = await import('exceljs');
  const workbook = new ExcelJS.Workbook();

  addProgressDataSheet(workbook);
  addProgressHistorySheet(workbook);

  const buffer = await workbook.xlsx.writeBuffer();
  const file_name = `charts-summary.xlsx`;
  const file_blob = (new Blob([buffer]));
  const file = new File([file_blob], file_name);
  await uploadFiles([file], 'terra');
  await $services.terra.post({
    attribute: `naavix-upload/report-upload/container/${route.params.id}`,
    body: {
      service: file.service_object,
      asset: common_store.active_asset?.name,
      organization: auth_store.current_organization?.name,
    },
  });
  logger.log('File uploaded successfully', file);
}

watch(() => terra_charts_ref$.value?.getChartsData(), async (charts_data) => {
  if (charts_data?.is_loading === false) {
    try {
      const { group_workflow_project, progress_data } = await getFormattedData(charts_data.progress_data);
      state.group_workflow_project = group_workflow_project;
      state.progress_data = progress_data;
      await uploadChartsDataExcel();
      state.upload_status = 'completed';
      emitter.emit('agentSetupCompleted');
    }
    catch (error) {
      console.log(error);
      logger.log('Error generating charts data Excel:', error);
      state.upload_status = 'failed';
      emitter.emit('agentSetupFailed');
    }
  }
});

onMounted(() => {
  emitter.on('initializeAgent', () => {
    state.upload_status = 'started';
  });
});
onBeforeUnmount(() => {
  emitter.off('initializeAgent');
});
</script>

<template>
  <div v-if="state.upload_status === 'started'" style="position: absolute; top: -9999px; left: -9999px;">
    <TerraCharts ref="terra_charts_ref$" />
  </div>
</template>
