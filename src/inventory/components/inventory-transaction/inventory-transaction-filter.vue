<script setup>
import { orderBy } from 'lodash-es';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';

const props = defineProps({
  // eslint-disable-next-line vue/prop-name-casing
  warehouse_id: { type: String, default: '' },
});

const emit = defineEmits(['apply']);
const { $t } = useCommonImports();
const inventory_store = useInventoryStore();

const workflows = computed(() => orderBy(inventory_store.workflows, ['order_index'], ['asc']).map((workflow) => {
  return {
    name: workflow.name,
    uid: workflow.uid,
  };
}));

const warehouses = computed(() => inventory_store.warehouses.map((warehouse) => {
  return {
    name: warehouse.name,
    uid: warehouse.uid,
  };
}));

const display_filters = computed(() => {
  return [
    {
      name: $t('Type'),
      is_static: true,
      data_type: 'single_select',
      operators: ['isAnyOf'],
      option_type: 'single_select',
      options: [...workflows.value],
      uid: 'workflow',
      is_blank_option: false,
    },
    {
      name: $t('Status'),
      is_static: true,
      data_type: 'single_select',
      operators: ['isAnyOf'],
      option_type: 'single_select',
      options: [{ name: 'Draft', uid: 'draft' }, { name: 'Published', uid: 'published' }],
      uid: 'status',
      is_blank_option: false,
    },
    {
      uid: 'date',
      name: $t('Date'),
      data_type: 'date',
      is_static: true,
      option_type: null,
      operators: ['isEqualTo'],
      options: [],
      type: 'date',

    },
    ...(!props?.warehouse_id
      ? [{
          name: $t('Warehouse'),
          is_static: true,
          data_type: 'single_select',
          operators: ['isAnyOf'],
          option_type: 'single_select',
          options: [...warehouses.value],
          uid: 'warehouse',
          is_blank_option: false,
        }]
      : []),
  ];
});

const $additional_filters = ref(null);
function applyFilters(filter) {
  const filter_value = {};
  filter.forEach((element) => {
    filter_value[element.field] = element.value;
  });
  emit('apply', { filter: filter_value, signal: $additional_filters.value?.signal });
}
</script>

<template>
  <HawkDisplayFilters
    :display_filters="display_filters"
    @apply="applyFilters"
  />
</template>
