<script setup>
import { useModal } from 'vue-final-modal';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import TerraWorkflowForm from '~/forms/components/new-form/terra-workflow-form.vue';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';
import WorkflowTemplateDetails from '~/terra/components/workflow/workflow-form-details.vue';

const { $t, common_store, route, router, auth_store } = useCommonImports();

const inventory_store = useInventoryStore();

const terra_workflow_form = useModal({
  component: TerraWorkflowForm,
  attrs: {
    onClose() {
      terra_workflow_form.close();
    },
  },
});

const form$ = ref(null);
const form_data = ref(null);
const is_loading = ref(false);
const form_workflows = ref([]);

const members = ref([]);

const transaction_type = computed(() => inventory_store.workflows_map[route.params.transaction_type_uid]);

const from_to = computed(() => {
  const from = transaction_type.value.from_status.map(uid => inventory_store.statuses_map[uid]?.name);
  const to = inventory_store.statuses_map[transaction_type.value.to_status]?.name || 'NA';

  if (from.length === 0)
    return `NA -> ${to}`;
  else if (from.length === 1)
    return `${from[0]} -> ${to}`;
  return `(${from.join(', ')})-> ${to}`;
});

const goBack = () => router.back();

async function handleSave() {
  try {
    is_loading.value = true;
    const transaction_type_data = form_data.value;

    const payload = {
      name: transaction_type_data.name,
      description: transaction_type_data.description,
      color: transaction_type_data.color,
      quantities_label: transaction_type_data.quantities_label,
      plural_label: transaction_type_data.plural_label,
      members: members.value,
      notify_members: transaction_type_data.notify_members,
      form_workflows: form_workflows.value,
    };

    await inventory_store.update_workflow({
      uid: transaction_type.value.uid,
      item_workflow: payload,
    });

    is_loading.value = false;
    goBack();
  }
  catch (error) {
    logger.log(`🚀 ~ handleSave ~ error:`, error);
    is_loading.value = false;
  }
}

function updateMembers(e) {
  members.value = ([...e.users, ...e.teams]).map((member) => {
    return {
      ...member,
      asset: route.params.asset_id,
    };
  });
}

function formatString(str) {
  const words = str.split('_');
  words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
  return words.join(' ');
}

function getAssetMembers(members) {
  return members?.filter(member => member?.asset && member?.asset === route?.params?.asset_id) || [];
}

async function openTerraWorkflowForm(index = -1) {
  const form_data = index === -1 ? null : getFormattedFieldDetails(form_workflows.value[index]);
  terra_workflow_form.patchOptions({
    attrs: {
      form_data,
      is_inventory: true,
      disabled_templates: form_workflows.value.map(workflow => workflow.uid),
      onSave(form) {
        const field = {
          uid: form.template,
          name: form.name,
          assignees: form.assignees,
          duration: Number(form.duration),
          required: form.required,
          items: form.items || null,
          type: 'INTG_201',
          assignees_option: form.assignees_option,
        };
        if (index === -1)
          form_workflows.value.push(field);
        else
          form_workflows.value[index] = field;
        terra_workflow_form.close();
      },
    },
  });
  terra_workflow_form.open();
}

function getFormattedFieldDetails(form) {
  return {
    ...form,
    config: {
      template: form.uid,
      assignees: form.assignees,
      duration: form.duration,
      required: form.required || false,
      items: form.items,
      assignees_option: form.assignees_option,
    },
  };
}

onMounted(async () => {
  if (transaction_type.value?.uid) {
    form_data.value = transaction_type.value;
    members.value = getAssetMembers(transaction_type.value.members);
    form_workflows.value = [...(transaction_type.value?.form_workflows || [])];
  }
  await inventory_store.set_items({ query: { asset: route.params.asset_id } });
});
</script>

<template>
  <div class="block w-[calc(100vw_-_100px)]">
    <div class="p-4">
      <div class="flex items-center gap-3 mb-4">
        <HawkButton type="text" icon @click="goBack">
          <IconHawkChevronLeft />
        </HawkButton>
        <div class="text-lg font-semibold text-gray-900">
          {{ transaction_type.name }}
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <Vueform
          ref="form$"
          v-model="form_data"
          :float-placeholders="false"
          :display-errors="false"
          :columns="{
            default: { container: 12, label: 4, wrapper: 12 },
            sm: { container: 12, label: 4, wrapper: 12 },
            md: { container: 12, label: 4, wrapper: 12 },
          }"
          :add-classes="{ ElementLabel: { wrapper: 'text-gray-700 font-medium' } }"
          size="sm"
          sync
          :format-load="data => data"
        >
          <TextElement
            name="name"
            :label="$t('Name')"
            rules="required"
            class="mb-4"
            :placeholder="$t('Enter name')"
          />
          <TextareaElement
            name="description"
            :label="$t('Description')"
            class="mb-4"
            :placeholder="$t('Enter description')"
          />
          <HawkColorInput
            name="color"
            class="mb-4 col-span-12"
            :color="transaction_type?.color || null"
          />
          <StaticElement
            name="stock_operation"
            :label="$t('Operation type')"
            class="mb-4"
          >
            {{ $t(formatString(transaction_type.stock_operation)) }}
          </StaticElement>
          <StaticElement
            name="source_destination"
            class="mb-4"
          >
            <template #label>
              <div class="flex items-center gap-2">
                {{ $t('From') }} <IconHawkArrowNarrowRight class="w-4 h-4" /> {{ $t('To') }}
              </div>
            </template>
            <div class="flex items-center gap-2">
              {{ formatString(transaction_type.source) }} <IconHawkArrowNarrowRight class="w-4 h-4" /> {{ formatString(transaction_type.destination) }}
            </div>
          </StaticElement>
          <StaticElement
            name="statuses"
            :label="$t('Statuses')"
            :description="$t('The stock will be moved from the above statuses in the same order as they are displayed to the \'Issued\' status on performing the transaction')"
            class="mb-4"
          >
            {{ from_to }}
          </StaticElement>
          <TextElement
            name="quantities_label"
            :label="$t('Quantity label')"
            class="mb-4"
            :placeholder="$t('Enter quantity label')"
          />
          <TextElement
            name="plural_label"
            :label="$t('Plural label')"
            class="mb-4"
            :placeholder="$t('Enter plural label')"
          />
          <HawkAssigneeInput
            v-if="route.params?.asset_id"
            class="w-full mr-5"
            :multi="true"
            :options="{
              name: 'notify_members',
              has_teams: false,
              placeholder: $t('Select users or teams'),
              class: 'mb-4',
              label: $t('Notify'),
              description: $t('Choose users or teams to send notifications on publishing a transaction'),
            }"
          />
        </Vueform>
        <hr>

        <div v-if="auth_store.check_split('inventory_forms_integration')" class="py-4">
          <p class="text-md font-semibold text-gray-900">
            {{ $t('Forms') }}
          </p>
          <p class="text-xs text-gray-600 mb-4">
            {{ $t('Add a form to a transaction or an item to gather data.') }}
          </p>
          <div class="flex flex-col gap-2">
            <WorkflowTemplateDetails
              v-for="(form, index) in form_workflows"
              :key="index"
              class="mb-4"
              show_template_info
              :field_details="getFormattedFieldDetails(form)"
              @edit="openTerraWorkflowForm(index)"
              @delete="form_workflows.splice(index, 1)"
            >
              <template #item_details>
                <div v-if="form.items?.length > 0" class="flex items-center mt-2 flex-wrap gap-2">
                  <div class="text-xs text-gray-600">
                    {{ $t('Items') }}:
                  </div>
                  <HawkBadge v-for="item_uid in form.items.filter(item_uid => !!inventory_store.items_map[item_uid])" :key="item_uid" size="sm" class="!bg-white !border-solid !border !border-gray-300">
                    {{ inventory_store.items_map[item_uid]?.name }}
                  </HawkBadge>
                </div>
              </template>
            </WorkflowTemplateDetails>
          </div>
          <HawkButton type="link" @click="openTerraWorkflowForm()">
            <IconHawkPlus
              class="text-primary-700 w-4 h-4"
            />
            <div class="text-[14px] font-semibold group-hover:text-primary-800">
              {{ $t('Add form') }}
            </div>
          </HawkButton>
        </div>

        <template v-if="route.params?.asset_id">
          <hr class="mb-4">
          <div>
            <p class="text-md font-semibold text-gray-900">
              {{ $t('Permissions') }}
            </p>
            <p class="text-xs text-gray-600 mb-4">
              {{ $t('Display various types of transaction workflows with detailed information, along with a few configuration options and access controls.') }}
            </p>
            <HawkShare
              :members="common_store?.filter_users(getAssetMembers(transaction_type?.members))"
              :teams="common_store?.filter_teams(getAssetMembers(transaction_type?.members))"
              :access_levels="[
                ...(common_store.is_development || (auth_store.check_split('inventory_draft_permission') && common_store.is_ril) ? [{ name: 'draft', label: $t('Draft'), description: $t('View own transactions and create drafts only.') }] : []),
                { name: 'write', label: $t('Publish'), description: $t('View own transactions and create/publish transactions.') },
              ]"
              class="mb-5"
              hide_empty
              @input="updateMembers"
            />
          </div>
        </template>
      </div>
      <div class="sticky bottom-0 bg-white">
        <hr>
        <div class="flex justify-end items-center py-5">
          <hawk-button
            class="mr-5"
            type="outlined"
            @click="goBack"
          >
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button :loading="is_loading" @click="handleSave">
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>
