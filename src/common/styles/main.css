@import "./tippy.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  body,
  #app {
    height: 100%;
    margin: 0;
    padding: 0;
  }
}

*, *:before, *:after, :root {
  /* Vueform-related  */

  --vf-primary: #1570EF;
  --vf-primary-darker: #004EEB;
  --vf-primary-25: #F5FAFF;
  --vf-primary-50: #EFF8FF;
  --vf-primary-100: #D1E9FF;
  --vf-primary-200: #B2DDFF;
  --vf-primary-300: #84CAFF;
  --vf-primary-400: #53B1FD;
  --vf-primary-500: #2E90FA;
  --vf-primary-600: #1570EF;
  --vf-primary-700: #004EEB;
  --vf-primary-800: #1849A9;
  --vf-primary-900: #194185;
  --vf-color-on-primary: #ffffff;
  --vf-danger: #F04438;
  --vf-danger-lighter: #FEE4E2;
  --vf-danger-25: #FFFBFA;
  --vf-danger-50: #FEF3F2;
  --vf-danger-100: #FEE4E2;
  --vf-danger-200: #FECDCA;
  --vf-danger-300: #FDA29B;
  --vf-danger-400: #F97066;
  --vf-danger-500: #F04438;
  --vf-danger-600: #D92D20;
  --vf-danger-700: #B42318;
  --vf-danger-800: #912018;
  --vf-danger-900: #7A271A;
  --vf-success: #12B76A;
  --vf-success-lighter: #D1FADF;
  --vf-success-25: #F6FEF9;
  --vf-success-50: #ECFDF3;
  --vf-success-100: #D1FADF;
  --vf-success-200: #A6F4C5;
  --vf-success-300: #6CE9A6;
  --vf-success-400: #32D583;
  --vf-success-500: #12B76A;
  --vf-success-600: #039855;
  --vf-success-700: #027A48;
  --vf-success-800: #05603A;
  --vf-success-900: #054F31;
  --vf-ring-width: 4px;
  --vf-gray-25: #FCFCFD;
  --vf-gray-50: #F9FAFB;
  --vf-gray-100: #F2F4F7;
  --vf-gray-200: #EAECF0;
  --vf-gray-300: #D0D5DD;
  --vf-gray-400: #98A2B3;
  --vf-gray-500: #667085;
  --vf-gray-600: #475467;
  --vf-gray-700: #344054;
  --vf-gray-800: #1D2939;
  --vf-gray-900: #101828;
  --vf-font-size: 1rem;
  --vf-font-size-sm: 0.875rem;
  --vf-font-size-lg: 1rem;
  --vf-font-size-small: 0.875rem;
  --vf-font-size-small-sm: 0.8125rem;
  --vf-font-size-small-lg: 0.875rem;
  --vf-line-height: 1.5rem;
  --vf-line-height-sm: 1.25rem;
  --vf-line-height-lg: 1.5rem;
  --vf-line-height-small: 1.25rem;
  --vf-line-height-small-sm: 1.125rem;
  --vf-line-height-small-lg: 1.25rem;
  --vf-letter-spacing: 0px;
  --vf-letter-spacing-sm: 0px;
  --vf-letter-spacing-lg: 0px;
  --vf-letter-spacing-small: 0px;
  --vf-letter-spacing-small-sm: 0px;
  --vf-letter-spacing-small-lg: 0px;
  --vf-gutter: 1rem;
  --vf-gutter-sm: 0.5rem;
  --vf-gutter-lg: 1rem;
  --vf-min-height-input: 2.375rem;
  --vf-min-height-input-sm: 2.125rem;
  --vf-min-height-input-lg: 2.875rem;
  --vf-py-input: 0.375rem;
  --vf-py-input-sm: 0.375rem;
  --vf-py-input-lg: 0.625rem;
  --vf-px-input: 0.75rem;
  --vf-px-input-sm: 0.5rem;
  --vf-px-input-lg: 0.875rem;
  --vf-py-btn: 0.375rem;
  --vf-py-btn-sm: 0.375rem;
  --vf-py-btn-lg: 0.625rem;
  --vf-px-btn: 0.875rem;
  --vf-px-btn-sm: 0.75rem;
  --vf-px-btn-lg: 1.25rem;
  --vf-py-btn-small: 0.25rem;
  --vf-py-btn-small-sm: 0.25rem;
  --vf-py-btn-small-lg: 0.375rem;
  --vf-px-btn-small: 0.625rem;
  --vf-px-btn-small-sm: 0.625rem;
  --vf-px-btn-small-lg: 0.75rem;
  --vf-py-group-tabs: 0.375rem;
  --vf-py-group-tabs-sm: 0.375rem;
  --vf-py-group-tabs-lg: 0.625rem;
  --vf-px-group-tabs: 0.75rem;
  --vf-px-group-tabs-sm: 0.5rem;
  --vf-px-group-tabs-lg: 0.875rem;
  --vf-py-group-blocks: 0.75rem;
  --vf-py-group-blocks-sm: 0.625rem;
  --vf-py-group-blocks-lg: 0.875rem;
  --vf-px-group-blocks: 1rem;
  --vf-px-group-blocks-sm: 1rem;
  --vf-px-group-blocks-lg: 1rem;
  --vf-py-tag: 0px;
  --vf-py-tag-sm: 0px;
  --vf-py-tag-lg: 0px;
  --vf-px-tag: 0.4375rem;
  --vf-px-tag-sm: 0.4375rem;
  --vf-px-tag-lg: 0.4375rem;
  --vf-py-slider-tooltip: 0.125rem;
  --vf-py-slider-tooltip-sm: 0.0625rem;
  --vf-py-slider-tooltip-lg: 0.1875rem;
  --vf-px-slider-tooltip: 0.375rem;
  --vf-px-slider-tooltip-sm: 0.3125rem;
  --vf-px-slider-tooltip-lg: 0.5rem;
  --vf-space-addon: 0px;
  --vf-space-addon-sm: 0px;
  --vf-space-addon-lg: 0px;
  --vf-space-checkbox: 0.375rem;
  --vf-space-checkbox-sm: 0.375rem;
  --vf-space-checkbox-lg: 0.375rem;
  --vf-space-tags: 0.1875rem;
  --vf-space-tags-sm: 0.1875rem;
  --vf-space-tags-lg: 0.1875rem;
  --vf-floating-top: 0rem;
  --vf-floating-top-sm: 0rem;
  --vf-floating-top-lg: 0.6875rem;
  --vf-bg-input: #ffffff;
  --vf-bg-input-hover: #ffffff;
  --vf-bg-input-focus: #ffffff;
  --vf-bg-input-danger: white;
  --vf-bg-input-success: #ffffff;
  --vf-bg-disabled: var(--vf-gray-100);
  --vf-bg-selected: #1118270d;
  --vf-bg-passive: var(--vf-gray-300);
  --vf-bg-icon: var(--vf-gray-500);
  --vf-bg-danger: var(--vf-danger-lighter);
  --vf-bg-success: var(--vf-success-lighter);
  --vf-bg-tag: var(--vf-primary);
  --vf-bg-slider-handle: var(--vf-primary);
  --vf-bg-toggle-handle: #ffffff;
  --vf-bg-date-head: var(--vf-gray-100);
  --vf-bg-addon: #ffffff00;
  --vf-bg-btn: var(--vf-primary);
  --vf-bg-btn-danger: var(--vf-danger);
  --vf-bg-btn-secondary: var(--vf-gray-200);
  --vf-color-input: var(--vf-gray-800);
  --vf-color-input-hover: var(--vf-gray-800);
  --vf-color-input-focus: var(--vf-gray-800);
  --vf-color-input-danger: var(--vf-gray-800);
  --vf-color-input-success: var(--vf-gray-800);
  --vf-color-disabled: var(--vf-gray-400);
  --vf-color-placeholder: var(--vf-gray-500);
  --vf-color-passive: var(--vf-gray-700);
  --vf-color-muted: var(--vf-gray-500);
  --vf-color-floating: var(--vf-gray-500);
  --vf-color-floating-focus: var(--vf-gray-500);
  --vf-color-floating-success: var(--vf-gray-500);
  --vf-color-floating-danger: var(--vf-danger-lighter);
  --vf-color-danger: var(--vf-danger);
  --vf-color-success: var(--vf-success);
  --vf-color-tag: var(--vf-color-on-primary);
  --vf-color-addon: var(--vf-gray-800);
  --vf-color-date-head: var(--vf-gray-700);
  --vf-color-btn: var(--vf-color-on-primary);
  --vf-color-btn-danger: #ffffff;
  --vf-color-btn-secondary: var(--vf-gray-700);
  --vf-border-color-input: var(--vf-gray-300);
  --vf-border-color-input-hover: var(--vf-gray-300);
  --vf-border-color-input-focus: #1570EF;
  --vf-border-color-input-danger: var(--vf-danger);
  --vf-border-color-input-success: var(--vf-gray-300);
  --vf-border-color-checked: var(--vf-primary);
  --vf-border-color-passive: var(--vf-gray-300);
  --vf-border-color-slider-tooltip: var(--vf-primary);
  --vf-border-color-tag: var(--vf-primary);
  --vf-border-color-btn: var(--vf-primary);
  --vf-border-color-btn-danger: var(--vf-danger);
  --vf-border-color-btn-secondary: var(--vf-gray-200);
  --vf-border-width-input-t: 1px;
  --vf-border-width-input-r: 1px;
  --vf-border-width-input-b: 1px;
  --vf-border-width-input-l: 1px;
  --vf-border-width-radio-t: 1px;
  --vf-border-width-radio-r: 1px;
  --vf-border-width-radio-b: 1px;
  --vf-border-width-radio-l: 1px;
  --vf-border-width-checkbox-t: 1px;
  --vf-border-width-checkbox-r: 1px;
  --vf-border-width-checkbox-b: 1px;
  --vf-border-width-checkbox-l: 1px;
  --vf-border-width-dropdown: 1px;
  --vf-border-width-btn: 1px;
  --vf-border-width-toggle: 0.125rem;
  --vf-border-width-tag: 1px;
  --vf-shadow-input: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-input-hover: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-input-focus: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-handles: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-handles-hover: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-handles-focus: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-btn: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-shadow-dropdown: 0px 0px 0px 0px rgba(0,0,0,0);
  --vf-radius-input: 8px;
  --vf-radius-input-sm: 8px;
  --vf-radius-input-lg: 8px;
  --vf-radius-btn: 8px;
  --vf-radius-btn-sm: 8px;
  --vf-radius-btn-lg: 8px;
  --vf-radius-small: 8px;
  --vf-radius-small-sm: 8px;
  --vf-radius-small-lg: 8px;
  --vf-radius-large: 8px;
  --vf-radius-large-sm: 8px;
  --vf-radius-large-lg: 8px;
  --vf-radius-tag: 8px;
  --vf-radius-tag-sm: 8px;
  --vf-radius-tag-lg: 8px;
  --vf-radius-checkbox: 0.25rem;
  --vf-radius-checkbox-sm: 0.25rem;
  --vf-radius-checkbox-lg: 0.25rem;
  --vf-radius-slider: 0.25rem;
  --vf-radius-slider-sm: 0.25rem;
  --vf-radius-slider-lg: 0.25rem;
  --vf-radius-image: 0.25rem;
  --vf-radius-image-sm: 0.25rem;
  --vf-radius-image-lg: 0.25rem;
  --vf-radius-gallery: 0.25rem;
  --vf-radius-gallery-sm: 0.25rem;
  --vf-radius-gallery-lg: 0.25rem;
  --vf-checkbox-size: 1rem;
  --vf-checkbox-size-sm: 0.875rem;
  --vf-checkbox-size-lg: 1rem;
  --vf-gallery-size: 6rem;
  --vf-gallery-size-sm: 5rem;
  --vf-gallery-size-lg: 7rem;
  --vf-toggle-width: 3rem;
  --vf-toggle-width-sm: 2.75rem;
  --vf-toggle-width-lg: 3rem;
  --vf-toggle-height: 1.25rem;
  --vf-toggle-height-sm: 1rem;
  --vf-toggle-height-lg: 1.25rem;
  --vf-slider-height: 0.375rem;
  --vf-slider-height-sm: 0.3125rem;
  --vf-slider-height-lg: 0.5rem;
  --vf-slider-height-vertical: 20rem;
  --vf-slider-height-vertical-sm: 20rem;
  --vf-slider-height-vertical-lg: 20rem;
  --vf-slider-handle-size: 1rem;
  --vf-slider-handle-size-sm: 0.875rem;
  --vf-slider-handle-size-lg: 1.25rem;
  --vf-slider-tooltip-distance: 0.5rem;
  --vf-slider-tooltip-distance-sm: 0.375rem;
  --vf-slider-tooltip-distance-lg: 0.5rem;
  --vf-slider-tooltip-arrow-size: 0.3125rem;
  --vf-slider-tooltip-arrow-size-sm: 0.3125rem;
  --vf-slider-tooltip-arrow-size-lg: 0.3125rem;
  --vf-ring-color: #bde0ff84;
  --vf-bg-checkbox: #fff;


  /* @vuepic/vue-datepicker */
  /*General*/
  --dp-font-family: "Inter var";
  --dp-border-radius: 8px; /*Configurable border-radius*/
  --dp-cell-border-radius: 50%; /*Specific border radius for the calendar cell*/
  --dp-common-transition: all 0.1s ease-in; /*Generic transition applied on buttons and calendar cells*/

  /*Sizing*/
  --dp-button-height: 35px; /*Size for buttons in overlays*/
  --dp-month-year-row-height: 40px; /*Height of the month-year select row*/
  --dp-month-year-row-button-size: 32px; /*Specific height for the next/previous buttons*/
  --dp-button-icon-height: 20px; /*Icon sizing in buttons*/
  --dp-cell-size: 40px; /*Width and height of calendar cell*/
  --dp-cell-padding: 5px; /*Padding in the cell*/
  --dp-common-padding: 10px; /*Common padding used*/
  --dp-input-icon-padding: 35px; /*Padding on the left side of the input if icon is present*/
  --dp-input-padding: 6px 30px 6px 12px; /*Padding in the input*/
  --dp-menu-min-width: 350px; /*Adjust the min width of the menu*/
  --dp-action-buttons-padding: 2px 5px; /*Adjust padding for the action buttons in action row*/
  --dp-row-maring:  8px 0; /*Adjust the spacing between rows in the calendar*/
  --dp-calendar-header-cell-padding: 0.5rem; /*Adjust padding in calendar header cells*/
  --dp-two-calendars-spacing:  10px; /*Space between multiple calendars*/
  --dp-overlay-col-padding:  3px; /*Padding in the overlay column*/
  --dp-time-inc-dec-button-size:  32px; /*Sizing for arrow buttons in the time picker*/
  --dp-menu-padding: 8px 16px; /*Menu padding*/
  
  /*Font sizes*/
  --dp-font-size: 14px; /*Default font-size*/
  --dp-preview-font-size: 0.8rem; /*Font size of the date preview in the action row*/
  --dp-time-font-size: 0.8rem; /*Font size in the time picker*/
  
  /*Transitions*/
  --dp-animation-duration: 0.1s; /*Transition duration*/
  --dp-menu-appear-transition-timing: cubic-bezier(.4, 0, 1, 1); /*Timing on menu appear animation*/
  --dp-transition-timing: ease-out; /*Timing on slide animations*/
}

.dp__theme_light {
  --dp-background-color: #ffffff;
  --dp-text-color: var(--vf-gray-900);
  --dp-hover-color: var(--vf-gray-50);
  --dp-hover-text-color: #212121;
  --dp-hover-icon-color: #959595;
  --dp-primary-color: var(--vf-primary-600);
  --dp-primary-text-color: var(--vf-gray-100);
  --dp-secondary-color: var(--vf-gray-400);
  --dp-border-color: #ddd;
  --dp-menu-border-color: #ddd;
  --dp-border-color-hover: #aaaeb7;
  --dp-disabled-color: var(--vf-gray-400);
  --dp-scroll-bar-background: #f3f3f3;
  --dp-scroll-bar-color: #959595;
  --dp-success-color: var(--vf-success-600);
  --dp-success-color-disabled: #a3d9b1;
  --dp-icon-color: #959595;
  --dp-danger-color: var(--vf-error-600);
  --dp-highlight-color: rgba(25, 118, 210, 0.1);
}

.dp__calendar_header_item {
  @apply text-gray-500 font-medium;
}

.dp__time_display {
  @apply text-sm;
}

.dp__instance_calendar {
  @apply rounded-lg overflow-hidden;
}

.dp__outer_menu_wrap > [role="dialog"] > div > .dp__instance_calendar {
  @apply shadow-lg;
}

.dp__input {
  @apply border-gray-300 h-[34px];
}

.dp__input:hover {
  @apply border-gray-300;
}

.dp__input_focus {
  @apply !border-primary-600 ring-4 ring-primary-100 ring-opacity-70;
}

.dp__disabled {
  @apply bg-gray-100 text-gray-500;
}

.dp--tp-wrap {
  @apply block m-auto;
}

.dp--qr-btn-active {
  @apply text-primary-600;
}

.select-caret {
  mask-image: url('../../../assets/icons/chevron-down.svg');
  height: 20px;
  width: 20px;
}

.delete-list-icon {
  mask:url('../../../assets/icons/delete-popup.svg') no-repeat;
  mask-position: center;
  mask-size: 15px;
  background-color: #667085;
}

.drag-handle-icon {
  mask: url('../../../assets/icons/drag-icon.svg') no-repeat;
  mask-position: center;
  mask-size: 15px;
}

.appearance-auto {
  appearance: auto !important; 
}

.sticky-header {
  @apply sticky top-16 bg-white z-1;
}


.vti__flag.np {
  background-color: transparent;
}
.vue-tel-input {
  border-radius: 8px;
  display: flex;
  border: 1px solid var(--vf-gray-300);
  text-align: left;
  min-height: 36px;
}
.vue-tel-input.disabled .selection,
.vue-tel-input.disabled .dropdown,
.vue-tel-input.disabled input {
  cursor: no-drop;
}
.vue-tel-input:focus-within {
  outline: var(--vf-ring-width) solid var(--vf-ring-color);
  border-color: var(--vf-primary);
}
.vue-tel-input.has-error {
  @apply border-red-300;
  outline: var(--vf-ring-width) solid #fee2e2;
}
.vti__dropdown {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  position: relative;
  padding: 7px;
  cursor: pointer;
  border-radius: 8px 0 0 8px;
}
.vti__dropdown.show {
  max-height: 300px;
  overflow: scroll;
}
.vti__dropdown.open {
  background-color: #f3f3f3;
}
.vti__dropdown.disabled {
  cursor: no-drop;
  outline: none;
  background-color: #f3f3f3;
}
.vti__dropdown:hover {
  background-color: #F9FAFB;
}
.vti__selection {
  font-size: 0.8em;
  display: flex;
  align-items: center;
}
.vti__selection .vti__country-code {
  color: #666;
}
.vti__flag {
  margin-right: 8px;
  margin-left: 4px;
}
.vti__dropdown-list {
  z-index: 2;
  padding: 4px;
  margin: 0;
  text-align: left;
  list-style: none;
  max-height: 200px;
  overflow-y: scroll;
  position: absolute;
  left: -1px;
  background-color: #fff;
  border: 1px solid #E5E7EB;
  width: 350px;
  border-radius: 8px;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.vti__dropdown-list.below {
  top: 33px;
}
.vti__dropdown-list.above {
  top: auto;
  bottom: 100%;
  margin-bottom: 4px;
}
.vti__dropdown-arrow {
  display: inline-block;
  color: var(--vf-gray-600);
}
.vti__dropdown-item {
  cursor: pointer;
  padding: 10px 16px;
  border-radius: 8px;
}
.vti__dropdown-item.last-preferred {
  border-bottom: 1px solid #cacaca;
}
.vti__dropdown-item .vti__flag {
  display: inline-block;
}
.vti__dropdown-item strong{
  font-weight: 400;
}
.vti__dropdown-item:hover{
  background-color: #F9FAFB;
}
.vti__input {
  border: none;
  border-radius: 0 8px 8px 0;
  width: 90%;
  outline: none;
  padding: 0 8px;
  font-size: 14px!important;
}
.vti__search_box {
  border: 1px solid #ccc;
  width: 94%;
  padding: 7px;
  margin: 2px;
}

#fc_frame, #fc_frame.fc-widget-normal{
  z-index: 999;
}

.vueform-input-bottom-underline-focused {
  position: relative;
}

.vueform-input-bottom-underline-focused::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 1%;
  width: 98%;
  height: 1px;
  @apply bg-primary-600 !important;
}

.vueform-input-bottom-underline {
  position: relative;
}

.vueform-input-bottom-underline::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 1%;
  width: 98%;
  height: 1px;
  @apply bg-gray-300;
}

.date-time-underline .dp__input {
  border: 0;
}

.date-time-underline .dp__input_focus {
  @apply ring-0;
}

.date-time-underline .dp__main:has(.dp__input.dp__input_focus)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 1%;
  width: 98%;
  height: 1px;
  @apply bg-primary-600 !important;
}
