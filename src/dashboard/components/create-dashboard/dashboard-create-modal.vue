<script setup>
import { useRoute } from 'vue-router';
import { useAuthStore } from '~/auth/stores/auth.store';

import HawkDateRangeFilter from '~/common/components/organisms/hawk-filters/hawk-date-range-filter.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';

import DashboardCreateStandardMaps from '~/dashboard/components/create-dashboard/dashboard-create-standard-maps.vue';
import { getCustomPrintMeta } from '~/dashboard/print/custom-print.js';
import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';
import { useFamConstants } from '~/forms-as-module/composables/fam-constants.composable.js';

const props = defineProps({
  isEditing: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['close', 'save']);

const $toast = inject('$toast');

const route = useRoute();
const auth_store = useAuthStore();
const dashboard_store = useDashboardStore();
const { $t } = useCommonImports();

const { DASHBOARD_GLOBAL_DATE_FILTERS } = useFamConstants($t);

const form$ = ref(null);
const date_filter = ref(DASHBOARD_GLOBAL_DATE_FILTERS[0]);
const can_test_print = ref(!!dashboard_store.current_dashboard.docx_template);
const is_saving = ref(false);

const can_share_reports = computed(() => {
  if (['global', 'organization', 'forms', 'inventory'].includes(dashboard_store.scope))
    return auth_store.check_permission('share_reports', route.params.asset_id);
  else return false;
});
async function onCreate() {
  is_saving.value = true;
  const form_data = form$?.value?.data;
  if (!form_data) {
    $toast({
      title: $t('Something went wrong'),
      text: $t('Please try again'),
      type: 'error',
    });
    return emit('close');
  }
  let references;
  if (route.name === 'pm-dashboard') {
    references = [
      {
        resource_id: route.params.schedule_id,
        resource_type: 'project_management',
      },
    ];
  }
  else if (route.name === 'form-template-detail-report') {
    references = [
      {
        resource_id: route.params.form_id,
        resource_type: 'forms',
      },
    ];
  }
  else {
    references = [];
  }

  const sharing_data = {
    ...(form_data.assignees_input.assignees_type === 'Everyone'
      ? { public: true, members: {} }
      : {
          members: form_data.assignees_input.assignees.reduce((acc, member) => {
            acc[member.uid] = 'read';
            return acc;
          }, {}),
          public: false,
        }
    ),
  };

  let body;
  if (form_data.configuration_option === 'standard') {
    body = {
      name: form_data.dashboard_name,
      report_data: {
        module: form_data.module,
        data: {
          id: form_data.map,
        },
      },
      default: false,
      standard: true,
      organization: auth_store.current_organization?.uid,
      ...(dashboard_store.scope !== 'organization'
        ? {
            asset: dashboard_store.widget_asset,
          }
        : {}),
      references,
      ...sharing_data,
    };
  }

  else {
    const print_template = form$.value.data.print_template;
    body = {
      name: form_data.dashboard_name,
      report_data: props.isEditing ? dashboard_store.current_dashboard.report_data : {},
      default: false,
      organization: auth_store.current_organization?.uid,
      ...(dashboard_store.scope !== 'organization'
        ? {
            asset: dashboard_store.widget_asset,
          }
        : {}),
      references,
      ...sharing_data,
      selected_date_range: form_data.default_time_range.default_time_range_enabled ? date_filter.value : {},
      meta: {
        ...(print_template && { print_template: {
          file_name: print_template.name,
          type: print_template.type,
          name: print_template.name,
          size: print_template.size,
          service: print_template?.service_object || print_template?.service,
          upload_url: print_template.upload_url,
          uppy_id: print_template.uppy_id,
          created_at: new Date().toISOString(),
          owner: { uid: auth_store.logged_in_user_details?.user_id },
        },
        }),
      },
    };
  }

  try {
    await dashboard_store.update_dashboard({ ...body, ...(props.isEditing ? { uid: dashboard_store.current_dashboard.uid } : {}) }, $t);
    emit('close');
  }
  catch {
    emit('close');
  }
  finally {
    is_saving.value = false;
  }
}

async function testCustomDashboardPrint() {
  const meta_data = getCustomPrintMeta();
  const { data } = await dashboard_store.dashboard_custom_print(meta_data);
  const anchor = document.createElement('a');
  anchor.href = data.presigned_url;
  anchor.download = `${dashboard_store.current_dashboard.name}.pdf`;
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
}

function setPrintTestStatus(e) {
  if (!e)
    can_test_print.value = false;
}

function onFormMounted(el$) {
  if (props.isEditing) {
    date_filter.value = dashboard_store.current_dashboard.selected_date_range?.value ? dashboard_store.current_dashboard.selected_date_range : DASHBOARD_GLOBAL_DATE_FILTERS[0];
    el$.update({
      dashboard_name: dashboard_store.current_dashboard.name,
      assignees_input: {
        assignees_type: dashboard_store.current_dashboard.public ? 'Everyone' : 'Custom',
        assignees: Object.keys(dashboard_store.current_dashboard.members),
      },
      configuration_option: dashboard_store.current_dashboard.standard === true ? 'standard' : 'custom',
      default_time_range: {
        default_time_range_enabled: !!dashboard_store.current_dashboard.selected_date_range?.value,
      },
      // Will remove once split is removed
      print_template: dashboard_store.current_dashboard?.meta?.print_template?.service_object
        ? {
            ...dashboard_store.current_dashboard.meta.print_template,
            service: {
              ...dashboard_store.current_dashboard.meta.print_template.service_object,
              url: dashboard_store.current_dashboard.docx_template,
            },
          }
        : dashboard_store.current_dashboard?.meta?.print_template,
    });
  }
}
</script>

<template>
  <HawkModalTemplate content_class="rounded-lg" @close="emit('close')">
    <template #header_left>
      <div class="flex flex-col justify-start">
        {{ isEditing ? $t('Edit Dashboard') : $t("New Dashboard") }}
      </div>
    </template>
    <template #default>
      <Vueform
        ref="form$"
        size="sm"
        class="w-[600px]"
        :columns="{
          default: { container: 12, label: 4, wrapper: 12 },
          sm: { container: 12, label: 4, wrapper: 12 },
          md: { container: 12, label: 4, wrapper: 12 },
        }"
        @mounted="onFormMounted"
      >
        <TextElement
          name="dashboard_name"
          class="w-full"
          :label="$t('Name')"
          :placeholder="$t('Enter name')"
        />
        <RadiogroupElement
          v-if=" ['global'].includes(dashboard_store.scope)"
          class="mt-4"
          default="custom"
          :label="$t('Type')"
          name="configuration_option"
          :disabled="isEditing"
          :items="[
            {
              value: 'custom',
              label: $t('Custom'),
              description: $t('Custom'),
            },
            {
              value: 'standard',
              label: $t('Standard'),
              description:
                $t('Standard'),
            },
          ]"
        />
        <SelectElement
          :conditions="[['configuration_option', '==', 'standard']]"
          name="module"
          default="maps"
          disabled
          :can-deselect="false"
          :native="false"
          :search="true"
          :label="$t('Module')"
          :items="[{
            label: $t('maps'),
            value: 'maps',
          }]"
          class="my-4"
        />
        <ObjectElement
          v-if="can_share_reports"
          class="my-4"
          name="assignees_input"
          :label="$t('Share with')"
        >
          <RadiogroupElement
            name="assignees_type"
            :items="{
              Everyone: $t('Everyone'),
              Custom: $t('Custom'),
            }"
            default="Custom"
          />
          <hawk-assignee-input
            :options="{
              name: 'assignees',
              conditions: [
                ['assignees_input.assignees_type', 'Custom'],
              ],
              has_teams: true,
              placeholder: $t('Select members'),
              columns: {
                default: { container: 12, label: 12, wrapper: 12 },
                sm: { container: 12, label: 12, wrapper: 12 },
                md: { container: 12, label: 12, wrapper: 12 },
              },
            }"
            :multi="true"
          />
        </ObjectElement>
        <DashboardCreateStandardMaps :conditions="[['module', '==', 'maps']]" />
        <ObjectElement
          name="default_time_range"
          :label="$t('Default timerange')"
        >
          <ToggleElement
            class="mb-2"
            name="default_time_range_enabled"
          />
          <StaticElement
            name="time_range"
            :conditions="[['default_time_range.default_time_range_enabled', '==', true]]"
          >
            <template #default>
              <HawkDateRangeFilter
                class="w-full"
                width-class="w-full"
                position="top"
                :active-range="date_filter"
                :date-filters="DASHBOARD_GLOBAL_DATE_FILTERS"
                @filter-applied="e => date_filter = e"
              />
            </template>
          </StaticElement>
        </ObjectElement>
        <FileElement
          v-if="isEditing && auth_store.check_split('custom_dashboard_export')"
          name="print_template"
          :drop="true"
          :soft-remove="true"
          :label="$t('Document template')"
          class="mb-4"
          :auto="false"
          :use_uppy="true"
          :presets="['hawk_file_element']"
          accept=".docx"
          :options="{
            clickable_text: $t('Click to upload'),
            text: $t('or drag and drop'),
            description: $t('.docx files only'),
          }"
          @change="setPrintTestStatus"
        >
          <template #after>
            <div v-if="form$?.data?.print_template" class="flex items-center justify-between">
              <div class="flex items-center">
                <IconHawkCheckCircleGreen />
                <span class="font-medium mx-1.5 text-gray-900">
                  {{ $t('Uploaded') }}
                </span>
                <span> {{ $date(form$?.data?.print_template?.created_at, 'DD[th] MMM YYYY h:mm A') }}</span>
              </div>
              <HawkButton
                :disabled="!can_test_print"
                type="link"
                class="cursor-pointer"
                @click="testCustomDashboardPrint"
              >
                <span
                  v-tippy="can_test_print ? '' : $t('Please save the dashboard with the uploaded template to test the print')"
                >
                  {{ $t('Test') }}
                </span>
              </HawkButton>
            </div>
          </template>
        </FileElement>
      </Vueform>
    </template>
    <template #footer>
      <Vueform size="sm">
        <div class="flex justify-end w-full col-span-full">
          <ButtonElement
            name="cancel"
            class="mr-4"
            :secondary="true"
            @click="emit('close')"
          >
            {{ $t('Cancel') }}
          </ButtonElement>
          <ButtonElement
            name="save"
            :loading="is_saving || form$?.isLoading"
            :disabled="form$?.hasErrors || form$?.isLoading || form$?.uploading?.value || is_saving"
            @click="onCreate()"
          >
            {{ $t('Save') }}
          </ButtonElement>
        </div>
      </Vueform>
    </template>
  </HawkModalTemplate>
</template>
