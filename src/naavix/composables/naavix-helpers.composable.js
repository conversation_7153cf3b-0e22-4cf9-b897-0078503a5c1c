import { nextTick, ref } from 'vue';
import { useModal } from 'vue-final-modal';
import NaavixGraphModal from '~/naavix/components/resource-elements/naavix-graph-modal.vue';
import NaavixTableModal from '~/naavix/components/resource-elements/naavix-table-modal.vue';

export function useNaavixHelpers() {
  const table_modal = useModal({
    component: NaavixTableModal,
    attrs: {
      onClose() {
        table_modal.close();
      },
    },
  });

  const chart_modal = useModal({
    component: NaavixGraphModal,
    attrs: {
      onClose() {
        chart_modal.close();
      },
    },
  });

  // data has information of event.content.function_response.response. (Title and Description is not needed for tables because the artifact is fetched again and the title and description can easily be used from there)
  function openTableModal(data) {
    table_modal.patchOptions({
      attrs: {
        data,
      },
    });
    table_modal.open();
  }

  // data has information of event.content.function_response.response. (Title and Description is also needed for charts because the artifact is not fetched again)
  function openChartModal(data, title, description) {
    chart_modal.patchOptions({
      attrs: {
        title,
        description,
        data,
      },
    });
    chart_modal.open();
  }

  return {
    openTableModal,
    openChartModal,
  };
}

export function useChatScroll() {
  const chatContainer = ref(null);
  const isAtBottom = ref(true);

  function isAtBottomOfContainer() {
    const element = chatContainer.value;
    if (!element)
      return false;
    return Math.abs(element.scrollHeight - element.clientHeight - element.scrollTop) < 30;
  }

  function scrollToBottom(is_manual = false, is_streaming = false) {
    if (!chatContainer.value)
      return;

    if (is_manual || isAtBottom.value) {
      nextTick(() => {
        chatContainer.value.scrollTo({
          top: chatContainer.value.scrollHeight,
          behavior: is_manual && !is_streaming ? 'smooth' : 'instant',
        });
      });
    }
  }

  function handleScroll() {
    isAtBottom.value = isAtBottomOfContainer();
  }

  function setupScrollListeners() {
    chatContainer.value?.addEventListener('scroll', handleScroll);
  }

  function cleanupScrollListeners() {
    chatContainer.value?.removeEventListener('scroll', handleScroll);
  }

  return {
    chatContainer,
    scrollToBottom,
    setupScrollListeners,
    cleanupScrollListeners,
    isAtBottom,
  };
}
