<script setup>
import { storeToRefs } from 'pinia';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  title: {
    type: String,
    default: 'Construction Progress Intelligence',
  },
  description: {
    type: String,
    default: 'Monitor solar construction progress, analyze performance metrics, and stay ahead of project timelines.',
  },
  starterSuggestions: {
    type: Array,
    default: () => {
      return [
        `What activities are delayed and need immediate attention this week?`,
        `Compare daily module installation performance against planned work rate`,
        `Which tracker installation locations show critical progress delays?`,
        `Show progress status of electrical work across inverter blocks vs timeline`,
        `What's the current trenching progress and how much work remains by location?`,
      ];
    },
  },
});

const naavix_store = useNaavixStore();
const { is_explicit_function_call_active } = storeToRefs(naavix_store);

const state = reactive({
  is_bubble_visible: true,
  is_bubble_open: false,
  is_bubble_hovered: false,
  variant: 'mini',
});

watch(() => state.variant, () => {
  if (state.variant === 'full') {
    document.body.style.overflow = 'hidden';
  }
  else {
    document.body.style.overflow = '';
  }
});

function closeBubble() {
  state.is_bubble_open = false;
  state.variant = 'mini';
}
</script>

<template>
  <div
    v-if="state.is_bubble_visible"
    class="fixed right-4 bottom-4 h-12 w-12 flex items-center justify-center rounded-full bubble shadow-lg cursor-pointer z-[1001]"
    @click="state.is_bubble_open = !state.is_bubble_open"
    @mouseenter="state.is_bubble_hovered = true"
    @mouseleave="state.is_bubble_hovered = false"
  >
    <IconHawkXClose v-if="state.is_bubble_open" class="text-white" />
    <IconHawkStarSix v-else class="text-white" />
    <Transition name="fade">
      <div
        v-if="state.is_bubble_hovered && !state.is_bubble_open"
        class="absolute -top-2 -right-2 bg-gray-300 hover:bg-gray-400 rounded-full p-1 transform transition-all duration-200 hover:scale-110"
        @click.stop="state.is_bubble_visible = false"
      >
        <IconHawkXClose class="w-3 h-3" />
      </div>
    </Transition>
  </div>

  <Transition name="fade">
    <div v-if="is_explicit_function_call_active" class="circle-to-search fixed inset-0 z-40" />
  </Transition>

  <div v-if="state.is_bubble_visible && state.is_bubble_open && state.variant === 'full'" class="fixed inset-0 backdrop-blur-[2px] z-[41]" />

  <Transition name="slide-up">
    <NaavixChatDialog
      v-if="state.is_bubble_visible && state.is_bubble_open"
      :title="props.title"
      :description="props.description"
      :starter-suggestions="props.starterSuggestions"
      @close="closeBubble"
      @variant-change="state.variant = $event"
    />
  </Transition>
</template>

<style scoped>
@import "./styles/stylesheet.scss";

.circle-to-search {
  overflow: hidden;
}

.circle-to-search::before {
  content: "";
  position: absolute;
  top: -100%;
  left: -100%;
  width: 150%;
  height: 150%;
  opacity: 0.25;
  background: linear-gradient(
    135deg,
    transparent 35%,
    rgba(80, 85, 234, 0.8) 40%,
    rgba(69, 209, 224, 0.8) 50%,
    rgba(65, 230, 76, 0.8) 60%,
    transparent 65%
  );
  animation: continuousSweep 2s linear infinite;
  transform-origin: center;
}

@keyframes continuousSweep {
  0% {
    transform: translate(-100%, -100%);
  }
  100% {
    transform: translate(100%, 100%);
  }
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
  will-change: transform, opacity;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: scale(0.8);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
