<script setup>
import DOMPurify from 'dompurify';
import { cloneDeep } from 'lodash-es';
import { marked } from 'marked';
import { nanoid } from 'nanoid';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted, onUnmounted, watch } from 'vue';
import useAbortController from '~/common/composables/abort-controller.js';
import { sleep } from '~/common/utils/common.utils';
import { load_js_css_file } from '~/common/utils/load-script.util';
import { useExampleModuleActionHelpers } from '~/naavix/components/action-elements/example-module/example-module-actions.composable.js';
import { useTerraActionHelpers } from '~/naavix/components/action-elements/terra/terra-actions.composable';
import NaavixGraph from '~/naavix/components/resource-elements/naavix-graph.vue';
import NaavixPmActivities from '~/naavix/components/resource-elements/naavix-pm-activities.vue';
import { useChatScroll } from '~/naavix/composables/naavix-helpers.composable';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  variant: {
    type: String,
    default: 'mini',
    validator(value) {
      return ['mini', 'full'].includes(value);
    },
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  starterSuggestions: {
    type: Array,
    default: () => ([]),
  },
});

const emit = defineEmits('close');

const $services = inject('$services');
const $toast = inject('$toast');

const naavix_store = useNaavixStore();
const { set_chats } = naavix_store;
const { active_chat_id, are_messages_loading, messages, all_chats, is_explicit_function_call_active } = storeToRefs(naavix_store);

const { getExampleModuleActionsMap } = useExampleModuleActionHelpers();
const { getTerraActionsMap } = useTerraActionHelpers();
const controllers = useAbortController();

const {
  isAtBottom,
  chatContainer,
  scrollToBottom,
  setupScrollListeners,
  cleanupScrollListeners,
} = useChatScroll();

const agents_list = [
  {
    name: 'general',
    label: 'General',
  },
  {
    name: 'pm',
    label: 'Project Management',
  },
  {
    name: 'terra',
    label: 'Construction monitoring',
  },
];

const render_map = {
  chart: NaavixGraph,
  activities: NaavixPmActivities,
};

const actions_map = {
  ...getExampleModuleActionsMap(),
  ...getTerraActionsMap(),
};

// const follow_up_question_suggestions = [
//   'Do you want to monitor specific safety metrics for the upcoming week?',
//   'Need help drafting safety guidelines or preparing for a safety audit or something else?',
//   'Would you like to track the safety measures for future projects as well?',
// ];

const state = reactive({
  user_input: '',
  is_streaming: false,
  current_agent: cloneDeep(agents_list[2]),
  deep_dive: false,
  usage_quota_reached: '',
});

const inputContainer = ref(null);
const textarea = ref(null);

const function_calls_map = computed(() => {
  return messages.value.reduce((acc, message) => {
    message.function_calls?.forEach?.((function_call) => {
      acc[function_call.id] = function_call;
    });
    return acc;
  }, {});
});

function renderMarkdown(text) {
  const sanitizedHtml = DOMPurify.sanitize(marked.parse(text));
  return sanitizedHtml;
}

async function streamText(text) {
  const words = text.split(' ');

  for (let i = 0; i < words.length; i++) {
    const words_to_add = words[i] + (i < words.length - 1 ? ' ' : '');
    messages.value[messages.value.length - 1].message += words_to_add;
    scrollToBottom();
  }
}

async function handleFunctionCall(chunk) {
  if (!chunk.content.function_call.title)
    chunk.content.function_call.title = 'N/A';
  if (!chunk.content.function_call.description)
    chunk.content.function_call.description = 'N/A';
  messages.value[messages.value.length - 1].message = '';
  messages.value[messages.value.length - 1].function_calls.push(chunk.content.function_call);
  messages.value[messages.value.length - 1].current_function_call = chunk.content.function_call;
}

function handleFunctionResponse(chunk) {
  messages.value[messages.value.length - 1].function_responses.push(chunk.content.function_response);
}

async function sendMessage() {
  if (state.usage_quota_reached && !state.user_input.trim()) {
    state.user_input = state.usage_quota_reached;
    state.usage_quota_reached = '';
  }
  await nextTick();

  if (
    !state.user_input.trim()
    || are_messages_loading.value
    || state.is_streaming
  ) {
    return;
  }

  const messageText = state.user_input;
  state.user_input = '';
  textarea.value.style.height = '48px';

  // Add user message
  messages.value.push({
    id: nanoid(),
    sender: 'user',
    message: messageText,
  });

  scrollToBottom();

  // Add assistant message placeholder
  const naavix_message_id = nanoid();
  messages.value.push({
    id: naavix_message_id,
    sender: 'assistant',
    function_calls: [],
    current_function_call: null,
    message: '',
    function_responses: [],
    is_streaming: true,
  });

  const body = {
    message: messageText,
    resource_id: 'terra-container-id',
    resource_type: 'container',
  };
  await streamMessage(naavix_message_id, body);
}

async function streamMessage(naavix_message_id, body) {
  try {
    state.is_streaming = true;
    const signal = controllers.add('stream_naavix_response');

    const response = await $services.ai.stream({
      agent_name: 'terra',
      session_uuid: active_chat_id.value,
      query: {
        organization: 'org_id_1',
        ...(state.deep_dive && { mode: 'deep_dive' }),
      },
      body,
      signal,
    });

    if (response.status !== 200)
      throw new Error(`HTTP error! status: ${response.status}`);

    const reader = response.data.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { value, done } = await reader.read();
      if (done)
        break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        if (!line.startsWith('data: '))
          continue;

        const data = JSON.parse(line.slice(6));

        if (!messages.value.find(message => message.id === naavix_message_id)) {
          controllers.abort('stream_naavix_response');
          $toast({
            title: 'Conversation stopped',
            text: 'Previous response was interrupted. You can now start a new conversation.',
            type: 'warning',
          });
          break;
        }

        if (data.event === 'partial_message') {
          messages.value[messages.value.length - 1].current_function_call = null;
          await streamText(data.content);
        }
        else if (data.event === 'message') {
          messages.value[messages.value.length - 1].message = data.content;
          messages.value[messages.value.length - 1].current_function_call = null;
        }
        else if (data.event === 'function_call') {
          await handleFunctionCall(data);
        }
        else if (data.event === 'function_response') {
          handleFunctionResponse(data);
        }
        else if (data.event === 'error') {
          throw new Error(data);
        }
      }
    }

    if (!all_chats.value.find(chat => chat.id === active_chat_id.value))
      await set_chats();
  }
  catch (error) {
    if (error.name === 'AbortError') {
      return;
    }

    if (error.type === 'rate_limit') {
      state.usage_quota_reached = messageText;
    }
    else {
      logger.error('Error', error);
      $toast({
        title: 'Something went wrong',
        text: 'Please try again',
        type: 'error',
      });
    }
  }
  finally {
    state.is_streaming = false;
    messages.value[messages.value.length - 1].is_streaming = false;
    await nextTick();
    if (isAtBottom.value) {
      await sleep(100);
      scrollToBottom();
    }
  }
}

function stopStreaming() {
  state.is_streaming = false;
  messages.value[messages.value.length - 1].current_function_call = null;
  messages.value[messages.value.length - 1].is_streaming = false;
  controllers.abort('stream_naavix_response');
}

function handleEnterKeyPress(event) {
  if (event.shiftKey) {
    return;
  }
  event.preventDefault();
  sendMessage();
}

function adjustHeight(element) {
  element.style.height = '48px';
  const new_height = Math.min(element.scrollHeight, 160);
  element.style.height = `${new_height}px`;
}

async function handleSuggestionClick(suggestion) {
  state.user_input = suggestion;
  await nextTick();
  adjustHeight(textarea.value);
}

function onImplicitActionComplete(data) {
  onActionContinue(data);
}

function onImplicitAction(action) {
  if (action.action) {
    action.action(messages.value[messages.value.length - 1].current_function_call, onImplicitActionComplete);
  }
}

function onActionContinue(response) {
  const current_function_call = messages.value[messages.value.length - 1].current_function_call;
  const body = {
    type: 'function_response',
    id: current_function_call.id,
    name: current_function_call.name,
    response: JSON.stringify(response.response),
    resource_id: 'terra-container-id',
    resource_type: 'container',
    message: '',
  };
  handleFunctionResponse({
    content: {
      function_response: {
        id: current_function_call.id,
        name: current_function_call.name,
        response: {
          artifact_id: '',
          artifact_type: '',
          content: response.response,
          status: 'success',
        },
      },
    },
    event: 'function_response',
    is_final_response: false,
    role: 'model',
  });
  streamMessage(messages.value[messages.value.length - 1].id, body);
}

function onActionCancel() {
  messages.value[messages.value.length - 1].current_function_call = null;
}

// function exportMessageAsReport(_message) {
//   $toast({
//     title: 'A detailed report will be sent to your email shortly',
//     text: 'Coming soon™',
//     type: 'info',
//   });
// }

watch(() => messages.value[messages.value.length - 1]?.current_function_call, () => {
  if (actions_map[messages.value[messages.value.length - 1]?.current_function_call?.name]?.type === 'implicit') {
    onImplicitAction(actions_map[messages.value[messages.value.length - 1].current_function_call.name]);
  }
}, { deep: true });

watch(() => [messages.value], () => {
  scrollToBottom();
}, { deep: true });

watch(() => active_chat_id.value, async () => {
  state.user_input = '';
  cleanupScrollListeners();
  await nextTick();
  setupScrollListeners();
  scrollToBottom(true);
  isAtBottom.value = true;
});

watch(() => actions_map?.[messages.value?.[messages.value.length - 1]?.current_function_call?.name]
  && actions_map[messages.value[messages.value.length - 1].current_function_call.name].type === 'explicit'
  && !state.is_streaming, (value) => {
  is_explicit_function_call_active.value = !!value;
});

onMounted(() => {
  // scrollToBottom();
  setupScrollListeners();
  load_js_css_file('https://cdn.plot.ly/plotly-cartesian-3.0.1.js', 'plotly', 'js');
});

onUnmounted(() => {
  cleanupScrollListeners();
});
</script>

<template>
  <div :key="active_chat_id" class="flex flex-col h-full">
    <div ref="chatContainer" class="flex-1 scrollbar overscroll-contain py-3">
      <div v-if="are_messages_loading" class="w-full px-4 mt-[200px]">
        <HawkLoader container_class="m-0" />
        <div class="text-sm font-normal text-gray-500 text-center mt-2">
          Loading messages...
        </div>
      </div>
      <template v-else-if="messages.length === 0">
        <div class="min-h-full px-4 flex flex-col gap-3 justify-center items-center">
          <div class="min-h-10 w-10 flex items-center justify-center rounded-full bubble mb-1">
            <IconHawkStarSix class="text-white" />
          </div>
          <div class="text-lg font-semibold text-gray-900">
            {{ props.title }}
          </div>
          <div class="text-sm font-normal text-gray-500 text-center mb-3">
            {{ props.description }}
          </div>
          <div
            v-for="(starter_suggestion) in props.starterSuggestions"
            :key="starter_suggestion"
            class="border border-gray-200 text-gray-700 p-4 rounded-lg text-sm font-medium max-w-lg w-full cursor-pointer"
            @click="handleSuggestionClick(starter_suggestion)"
          >
            {{ starter_suggestion }}
          </div>
        </div>
      </template>
      <template v-else>
        <div
          class="mx-auto space-y-6"
          :class="props.variant === 'full' ? 'w-3/4' : 'w-[calc(100%-8px)]  px-4'"
        >
          <div
            v-for="(message) in messages"
            :key="message.id"
            class="flex flex-col justify-start"
            :class="[message.sender === 'user' ? 'items-end' : 'items-start']"
          >
            <NaavixThoughtsTimeline
              :function-calls="message.function_calls"
              :function-responses="message.function_responses"
            />
            <template v-for="function_response in message.function_responses" :key="function_response.id">
              <template
                v-if="
                  ['chart'].includes(function_response.response.artifact_type)
                    && render_map[function_response.response.artifact_type]
                "
              >
                <component
                  :is="render_map[function_response.response.artifact_type]"
                  :title="function_calls_map[function_response.id].title"
                  :description="function_calls_map[function_response.id].description"
                  :data="function_response.response"
                  @close="emit('close')"
                  @scroll-to-bottom="scrollToBottom"
                />
              </template>
            </template>
            <div
              v-if="message.is_streaming && !message.message && !message.current_function_call?.title"
              class="flex items-center gap-2 mb-2"
            >
              <!-- <IconHawkAiAgent /> -->
              <div class="text-sm font-normal shimmer">
                Thinking...
              </div>
            </div>
            <div
              v-else-if="message.is_streaming && !message.message && message.current_function_call?.title"
            >
              <!-- class="flex items-center gap-2 mt-[16px] ml-4 text-sm font-semibold text-gray-900" -->
              <!-- <IconHawkAiAgent />
              NaaviX -->
            </div>
            <div
              v-else
              class="rounded-lg"
              :class="[
                message.sender === 'user'
                  ? 'bg-gray-100 text-gray-900 rounded-tr-none px-4 py-3'
                  : 'text-gray-900 rounded-tl-none',
              ]"
            >
              <!-- <div
                v-if="message.sender === 'assistant'"
                class="flex items-center gap-2 text-sm font-semibold text-gray-900 mt-1 mb-2"
              >
                <IconHawkAiAgent />
                NaaviX
              </div> -->
              <div class="prose prose-sm" v-html="renderMarkdown(message.message)" />
            </div>
            <NaavixCurrentThought
              v-if="message.current_function_call"
              :current-thought="message.current_function_call"
            />
            <NaavixArtifactsList
              :function-calls-map="function_calls_map"
              :function-responses="message.function_responses"
              :is-this-message-streaming="message.is_streaming"
            />
            <!-- <div v-if="message.function_calls?.length && !message.is_streaming" class="mt-3">
              <HawkButton type="outlined" @click="exportMessageAsReport">
                <IconHawkDownloadOne />
                Export as report
              </HawkButton>
            </div> -->
          </div>
        </div>
        <div v-if="!state.is_streaming && messages.length !== 0 && !state.usage_quota_reached" class="flex flex-col gap-1 w-[calc(100%-8px)] mx-auto px-4 mt-3">
          <!-- <div class="flex items-center gap-1 text-xs font-normal text-gray-500 mb-1">
            <IconHawkStarSix class="w-3 h-3 text-gray-600" />
            Suggestions:
          </div>
          <div
            v-for="follow_up_question_suggestion in follow_up_question_suggestions"
            :key="follow_up_question_suggestion"
            class="border border-gray-300 rounded-lg text-xs font-medium text-gray-700 px-2 py-1 cursor-pointer w-fit hover:bg-gray-50"
            @click="handleSuggestionClick(follow_up_question_suggestion)"
          >
            {{ follow_up_question_suggestion }}
          </div> -->
        </div>
        <div v-if="state.usage_quota_reached" class="mx-5 my-6 px-2 py-1 bg-error-50 border border-error-300 rounded text-xs font-medium text-error-700 flex items-center justify-between">
          <span class="flex items-start gap-1 italic">
            <IconHawkAlertTriangle class="w-4 h-4 mt-0.5" />
            You have reached the usage quota. Please try again after sometime.
          </span>
          <span class="hover:text-error-800 cursor-pointer h-4 flex items-center gap-1" @click="sendMessage">
            <IconHawkRefreshCwOne class="w-4 h-4" />
            Retry
          </span>
        </div>
      </template>
    </div>

    <div class="border-t border-gray-200">
      <div class="relative">
        <div
          v-show="!isAtBottom && messages.length > 0"
          v-tippy="{ content: 'Scroll to the bottom', placement: 'top' }"
          class="absolute right-5 -top-14 bg-gray-900 text-white shadow-xl cursor-pointer p-2 rounded-full"
          @click="scrollToBottom(true, state.is_streaming)"
        >
          <IconHawkChevronDown class="w-5 h-5" />
        </div>
        <div
          v-if="
            actions_map?.[messages?.[messages.length - 1]?.current_function_call?.name]
              && actions_map[messages[messages.length - 1].current_function_call.name].type === 'explicit'
              && !state.is_streaming
          "
          class="rounded-xl border border-primary-600 m-3"
        >
          <component
            :is="actions_map[messages[messages.length - 1].current_function_call.name].component"
            :function-call="messages[messages.length - 1].current_function_call"
            @continue="onActionContinue"
            @cancel="onActionCancel"
          />
        </div>
        <div
          v-else
          class="border border-gray-200 rounded-lg shadow-sm"
          :class="props.variant === 'full' ? 'w-3/4 mx-auto mt-3' : 'mx-3 mt-3'"
        >
          <div ref="inputContainer" class="relative flex flex-col">
            <textarea
              ref="textarea"
              v-model="state.user_input"
              rows="1"
              class="w-full resize-none rounded-xl px-4 py-3 min-h-[48px] max-h-[160px] text-sm scrollbar"
              placeholder="Ask me anything about the project"
              @input="$event => adjustHeight($event.target)"
              @keydown.enter="handleEnterKeyPress"
            />
          </div>
          <div class="flex justify-between items-center gap-2 mx-4 mb-3">
            <div class="flex items-center gap-3">
              <HawkMenu
                :items="agents_list"
                position="fixed"
                additional_trigger_classes="!ring-0"
                class="-mt-1"
                :disabled="true"
                @select="$event => state.current_agent = $event"
              >
                <template #trigger="{ open }">
                  <div
                    class="cursor-pointer flex items-center text-xs font-medium text-gray-600"
                  >
                    {{ state.current_agent.label }}
                    <IconHawkChevronUp v-if="open" class="w-4 h-4 ml-1" />
                    <IconHawkChevronDown v-else class="w-4 h-4 ml-1" />
                  </div>
                </template>
              </HawkMenu>
              <div
                class="flex items-center gap-1 cursor-pointer border rounded px-2 py-1"
                :class="state.deep_dive ? 'border-primary-500 bg-primary-50' : 'hover:bg-gray-50'"
                @click="state.deep_dive = !state.deep_dive"
              >
                <IconHawkLightbulbTwo class="w-4 h-4 inline" />
                <span class="text-xs font-medium text-gray-700">Deep dive</span>
              </div>
            </div>
            <button
              v-if="state.is_streaming"
              v-tippy="'Stop'"
              class="p-2.5 bg-gray-900 text-white rounded-lg focus:outline-none"
              @click="stopStreaming"
            >
              <IconHawkStop class="w-3 h-3 bg-white rounded" />
            </button>
            <button
              v-else
              v-tippy="'Send'"
              class="p-2 bg-[#107CB2] text-white rounded-lg hover:bg-[#2d7191] focus:outline-none"
              :class="{ 'opacity-50 cursor-not-allowed': !state.user_input.trim() || are_messages_loading }"
              @click="sendMessage"
            >
              <IconHawkSendThree class="w-4 h-4" />
            </button>
          </div>
        </div>
        <div class="text-xs font-normal text-gray-500 flex justify-center my-1">
          NaaviX accelerates insights. Double-check results.
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "../styles/stylesheet.scss";

.small-placeholder::placeholder {
  @apply text-sm;
  @apply text-gray-500;
}
</style>
