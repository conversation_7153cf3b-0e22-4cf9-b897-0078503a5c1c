<script setup>
import { nanoid } from 'nanoid';
import { storeToRefs } from 'pinia';
import useEmitter from '~/common/composables/useEmitter';
import { sleep } from '~/common/utils/common.utils';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  variant: {
    type: String,
    default: 'mini',
    validator(value) {
      return ['mini', 'full'].includes(value);
    },
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  starterSuggestions: {
    type: Array,
    default: () => ([]),
  },
});

const emit = defineEmits(['close', 'variantChange']);

const emitter = useEmitter();
const $services = inject('$services');

const naavix_store = useNaavixStore();
const { active_chat_id, messages, is_agent_setup } = storeToRefs(naavix_store);

const state = reactive({
  variant: 'mini',
  view: 'chat',
  show_refresh: false,
  is_refreshing: false,
  is_agent_ready: false,
  is_agent_setting_up: false,
  is_agent_setup_failed: false,
});

function createNewChat() {
  messages.value = [];
  state.view = 'chat';
  active_chat_id.value = nanoid();
}

async function onRefreshData() {
  state.is_refreshing = true;
  await sleep(3000);
  state.is_refreshing = false;
  state.show_refresh = false;
}

function onViewChange(view) {
  state.view = view;
}

async function initializeAgent() {
  if (state.is_agent_ready) {
    is_agent_setup.value = true;
  }
  else {
    state.is_agent_setting_up = true;
    emitter.emit('initializeAgent');

    // await sleep(3000);
    // state.is_agent_setting_up = false;
    // // if (state.is_agent_setup_failed) {
    // state.is_agent_setup_failed = false;
    // state.is_agent_ready = true;
    // // }
    // // else {
    // //   state.is_agent_setup_failed = true;
    // // }
  }
}

watch(() => state.variant, () => {
  emit('variantChange', state.variant);
});

onMounted(async () => {
  state.variant = props.variant;
  if (is_agent_setup.value)
    return;
  try {
    await $services.ai.get({
      url: '/agents/terra/initialize',
      query: {
        resource_id: 'terra-container-id',
        resource_type: 'container',
      },
      toast: false,
    });
    is_agent_setup.value = true;
  }
  catch {
    is_agent_setup.value = false;
  }
  emitter.on('agentSetupCompleted', () => {
    state.is_agent_ready = true;
    state.is_agent_setting_up = false;
  });
  emitter.on('agentSetupFailed', () => {
    state.is_agent_setup_failed = true;
    state.is_agent_ready = false;
    state.is_agent_setting_up = false;
  });
});
onBeforeUnmount(() => {
  emitter.off('agentSetupCompleted');
  emitter.off('agentSetupFailed');
});
</script>

<template>
  <div
    class="flex flex-col"
    :class="[
      state.variant === 'full'
        ? 'rounded-none w-full h-full fixed inset-0 z-[1001]'
        : `rounded-lg p-[2px] gradient-background gradient-background-mini w-[488px] h-4/5 z-[1001] fixed right-4 bottom-20`,
    ]"
  >
    <div
      class="bg-white h-full z-10 flex"
      :class="state.variant === 'full' ? 'flex-row' : 'flex-col rounded-[6px]'"
    >
      <template v-if="is_agent_setup">
        <NaavixChatsList
          v-show="state.view === 'all-chats' || state.variant === 'full'"
          :variant="state.variant"
          @create-new-chat="createNewChat"
          @view-change="onViewChange"
        />
        <div v-if="state.variant === 'full'" class="h-full w-px bg-gray-200" />
        <div v-show="state.view === 'chat' || state.variant === 'full'" class="h-full flex-1 flex flex-col">
          <NaavixChatHeader
            :variant="state.variant"
            @close="emit('close')"
            @create-new-chat="createNewChat"
            @variant-change="state.variant = $event"
            @view-change="onViewChange"
          />
          <div v-if="state.show_refresh" class="mx-4 my-2 px-2 py-1 bg-warning-50 border border-warning-300 rounded text-xs font-medium text-warning-700 flex justify-between">
            <span>
              Data is last fetched on 24th April, 2025
            </span>
            <HawkLoader v-if="state.is_refreshing" container_class="" :height="4" :width="4" />
            <span v-else class="hover:text-warning-800 cursor-pointer h-4" @click="onRefreshData">
              <IconHawkRefreshCcwFive class="w-4 h-4 inline" />
              Refresh
            </span>
          </div>
          <div class="flex-1 overflow-hidden">
            <NaavixChatStream
              :variant="state.variant"
              :title="props.title"
              :description="props.description"
              :starter-suggestions="props.starterSuggestions"
              @close="emit('close')"
            />
          </div>
        </div>
      </template>
      <NaavixSetupAgent
        v-else
        :is-agent-setting-up="state.is_agent_setting_up"
        :is-agent-ready="state.is_agent_ready"
        :is-agent-setup-failed="state.is_agent_setup_failed"
        @initialize-agent="initializeAgent"
      />
    </div>
  </div>
</template>

<style scoped>
@import "../styles/stylesheet.scss";

@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

.gradient-background::before,
.gradient-background::after {
  content: "";
  position: absolute;
  inset: 0rem;
  z-index: -1;
  background: conic-gradient(
    from var(--gradient-angle),
    #A150D0 1.26%,
    #5055EA 18.63%,
    #45D1E0 38.35%,
    #41E64C 57.12%,
    #F2A13D 77.78%,
    #E43A9A 98.9%
  );
  border-radius: inherit;
  animation: rotation 10s linear infinite;
}

.gradient-background-full::before,
.gradient-background-full::after {
  inset: 1px;
}

.gradient-background::after {
  filter: blur(100px);
  opacity: 0.2;
}

@keyframes rotation {
  0% {
    --gradient-angle: 0deg;
  }
  100% {
    --gradient-angle: 360deg;
  }
}
</style>
