<script setup>
import dayjs from 'dayjs';
import { orderBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  variant: {
    type: String,
    default: 'mini',
    validator(value) {
      return ['mini', 'full'].includes(value);
    },
  },
});

const emit = defineEmits(['createNewChat', 'viewChange']);

const naavix_store = useNaavixStore();

const { set_chats, set_chat } = naavix_store;
const { active_chat_id, all_chats } = storeToRefs(naavix_store);

const state = reactive({
  is_loading: false,
});

const sorted_chats = computed(() => {
  return orderBy(all_chats.value, [chat => chat.lastUpdateTime], ['desc']);
});

async function onChatClick(chat) {
  await set_chat(chat.id);
  emit('viewChange', 'chat');
}

onMounted(async () => {
  state.is_loading = true;
  await set_chats();
  state.is_loading = false;
});
</script>

<template>
  <div class="h-full flex flex-col" :class="props.variant === 'full' ? 'w-[360px]' : ''">
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
      <div class="flex items-center gap-1">
        <HawkButton v-if="props.variant === 'mini'" icon type="text" @click="emit('viewChange', 'chat')">
          <IconHawkArrowLeft />
        </HawkButton>
        <div class="text-md font-bold text-gray-900">
          All chats
        </div>
      </div>
      <HawkButton type="text" @click="emit('createNewChat')">
        <IconHawkEditFive />
        New chat
      </HawkButton>
    </div>
    <div class="flex-1 scrollbar overscroll-contain">
      <HawkLoader v-if="state.is_loading" />
      <template v-else>
        <div
          v-for="chat in sorted_chats"
          :key="chat.id"
          class="px-4 py-3 cursor-pointer"
          :class="chat.id === active_chat_id ? 'bg-gray-100 hover:bg-gray-100' : 'hover:bg-gray-50'"
          @click="onChatClick(chat)"
        >
          <div class="text-sm font-semibold text-gray-900">
            {{ chat.state.title }}
          </div>
          <div class="text-sm font-normal text-gray-500">
            {{ $date(dayjs(chat.lastUpdateTime * 1000), 'DD MMMM YYYY, hh:mma') }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
