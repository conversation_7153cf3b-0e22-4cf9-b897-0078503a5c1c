<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close']);
</script>

<template>
  <HawkModalContainer content_class="!z-[10000] w-[80vw]">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg">
            {{ props.title }}
          </div>
          <div class="text-sm font-normal text-gray-500">
            {{ props.description }}
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent>
        <NaavixGraph
          :data="props.data"
          :title="props.title"
          :description="props.description"
          :is-popup="true"
        />
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
