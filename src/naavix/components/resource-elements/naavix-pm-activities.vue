<script setup>
import { storeToRefs } from 'pinia';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

// const props = defineProps({
//   data: {
//     type: Object,
//     required: true,
//   },
// });

const emit = defineEmits(['close']);

const project_management_store = useProjectManagementStore();
const { $g, active_schedule, active_schedule_data } = storeToRefs(project_management_store);
const { set_active_task_uid } = project_management_store;

const state = reactive({
  activities: [],
});

function getHierarchyBreadcrumbs(task_id) {
  const parents = [];
  $g.value.eachParent((task) => {
    parents.push({
      value: task.uid,
      label: task.text,
      uid: task.id,
      has_children: true,
      truncate_length: Math.min(task.text?.length, 20),
    });
  }, task_id);
  return parents.reverse();
}

// TODO: Remove dependency of PM store
onMounted(() => {
  if (!$g.value)
    return;
  // const activity_uids = JSON.parse(props.data.response.activity_uids);
  const activity_uids = [active_schedule_data.value.data[2].uid, active_schedule_data.value.data[3].uid];
  state.activities = activity_uids.map((activity_uid) => {
    return active_schedule.value.activities[activity_uid];
  });
});
</script>

<template>
  <div>
    <div class="text-sm font-medium text-gray-600 mb-3">
      <template v-if="state.activities.length > 1">
        Activities:
      </template>
      <template v-else>
        Activity:
      </template>
      (NOTE: Open the AI chat in a PM Schedule with at least 4 activities to see some activities here)
    </div>
    <div
      v-for="activity in state.activities"
      :key="activity.uid"
      class="flex justify-between items-center border border-gray-300 rounded-lg px-4 py-2 mb-2"
    >
      <!-- border-gray-200 -->
      <div class="flex flex-col gap-1 flex-grow overflow-hidden mr-2">
        <div class="text-sm font-medium text-gray-600 truncate">
          {{ activity.text }}
        </div>
        <div class="flex flex-wrap items-center gap-1 text-xs font-normal text-gray-500">
          <span v-for="(parent_activity, index) in getHierarchyBreadcrumbs(activity.id)" :key="parent_activity.id">
            <HawkText class="text-xs font-normal text-gray-500" :length="15" :content="parent_activity.label" placement="top" />
            <template v-if="index !== getHierarchyBreadcrumbs(activity.id).length - 1">
              &bull;
            </template>
          </span>
        </div>
      </div>
      <div class="flex-shrink-0 ml-2 w-6">
        <IconHawkShareFour
          class="text-primary-700 hover:text-primary-800 cursor-pointer"
          @click="set_active_task_uid(activity.uid);emit('close')"
        />
      </div>
    </div>
  </div>
</template>
