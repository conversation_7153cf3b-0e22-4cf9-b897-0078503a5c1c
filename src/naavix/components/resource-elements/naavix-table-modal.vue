<script setup>
import { storeToRefs } from 'pinia';
import HawkHandsontable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const $services = inject('$services');

const naavix_store = useNaavixStore();
const { artifacts_map } = storeToRefs(naavix_store);

const state = reactive({
  is_loading: false,
  hot_instance: null,
  title: '',
  description: '',
  hot_data: [],
  hot_columns: [],
});

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_data.length + 1) * 45 + 7;
  if (calculated_height > 500)
    calculated_height = 500;
  return `${calculated_height}px`;
});

function onHandsOnTableReady(event) {
  state.is_loading = false;
  state.hot_instance = event;
}

function onExportClicked() {
  const exportPlugin = state.hot_instance.getPlugin('exportFile');
  exportPlugin.downloadFile('csv', {
    bom: false,
    columnDelimiter: ',',
    columnHeaders: true,
    exportHiddenColumns: true,
    exportHiddenRows: true,
    fileExtension: 'csv',
    filename: state.title,
    mimeType: 'text/csv',
    rowDelimiter: '\r\n',
    rowHeaders: true,
  });
}

onMounted(async () => {
  state.is_loading = true;
  if (artifacts_map.value[props.data.artifact_id]) {
    state.hot_columns = artifacts_map.value[props.data.artifact_id].columns;
    state.hot_data = artifacts_map.value[props.data.artifact_id].data;
    state.title = artifacts_map.value[props.data.artifact_id].title;
    state.description = artifacts_map.value[props.data.artifact_id].description;
  }
  else {
    const response = await $services.ai.get_artifact({
      artifact_id: props.data.artifact_id,
    });

    state.title = response.data.title;
    state.description = response.data.description;

    // const rows = response.data.split('\n');
    // const headers = rows[0].split(',');

    // state.hot_columns = headers.map(header => ({
    //   data: header.trim(),
    //   header: header.trim(),
    // }));

    // state.hot_data = rows.slice(1)
    //   .filter(row => row.trim())
    //   .map((row) => {
    //     const values = row.split(',');
    //     return headers.reduce((obj, header, index) => {
    //       obj[header.trim()] = values[index]?.trim() || '';
    //       return obj;
    //     }, {});
    //   });

    if (response.data.table_data.length) {
      state.hot_data = response.data.table_data;
      state.hot_columns = Object.keys(response.data.table_data[0]).map((entry) => {
        return {
          data: entry,
          header: entry,
        };
      });

      artifacts_map.value[props.data.artifact_id] = {
        columns: state.hot_columns,
        data: state.hot_data,
        title: state.title,
        description: state.description,
      };
    }
    else {
      artifacts_map.value[props.data.artifact_id] = {
        columns: [],
        data: [],
        title: state.title,
        description: state.description,
      };
    }
  }
  state.is_loading = false;
});
</script>

<template>
  <HawkModalContainer content_class="!z-[10000] w-[80vw]">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg">
            {{ state.title }}
          </div>
          <div class="text-sm font-normal text-gray-500">
            {{ state.description }}
          </div>
        </template>
        <template v-if="state.hot_data.length" #right>
          <HawkButton v-tippy="'Export as CSV'" icon type="text" @click="onExportClicked">
            <IconHawkDownloadOne class="text-gray-600" />
          </HawkButton>
        </template>
      </HawkModalHeader>
      <HawkModalContent>
        <HawkLoader v-if="state.is_loading" />
        <HawkHandsontable
          v-else-if="state.hot_data.length"
          :key="props.data.artifact_id"
          :data="state.hot_data"
          :columns="state.hot_columns"
          :col-headers="state.hot_columns.map(column => column.header)"
          :height="hot_table_height"
          :read-only="true"
          @ready="onHandsOnTableReady"
        />
        <div v-else class="text-sm font-semiBold w-full h-[240px] grid place-items-center">
          No data present
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
