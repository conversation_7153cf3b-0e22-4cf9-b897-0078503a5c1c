<script setup>
import { useCommonImports } from '~/common/composables/common-imports.composable.js';

const emit = defineEmits(['continue', 'cancel']);

const { $t } = useCommonImports();
</script>

<template>
  <div class="w-full">
    <div v-if="$slots.header" class="px-6 py-4">
      <slot name="header" />
    </div>
    <hr v-if="$slots.header">
    <div v-if="$slots.content" class="px-6 py-4">
      <slot name="content" />
      <div class="text-xs italic font-normal text-gray-600 mt-3">
        <span class="font-semibold text-gray-800">
          {{ $t('Note') }}:
        </span>
        You won't be able to send messages in chat until this action is completed or cancelled.
      </div>
    </div>
    <hr v-if="$slots.content">
    <slot name="footer">
      <div class="flex gap-2 justify-end w-full pr-3 py-2">
        <HawkButton
          name="cancel"
          type="plain"
          :secondary="true"
          @click="emit('cancel')"
        >
          {{ $t('Cancel') }}
        </HawkButton>
        <HawkButton
          name="save"
          type="link"
          @click="emit('continue')"
        >
          {{ $t('Continue') }}
        </HawkButton>
      </div>
    </slot>
  </div>
</template>
