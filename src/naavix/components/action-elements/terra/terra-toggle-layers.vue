<script setup>
import { onMounted } from 'vue';
import HawkTreeSelect from '~/common/components/organisms/hawk-tree/hawk-treeselect.vue';
import { useTerraStore } from '~/terra/store/terra.store';
import { useTerraHelperComposable } from '~/terra/utils/helper-composable.js';

const props = defineProps({
  functionCall: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['continue', 'cancel']);

const terra_store = useTerraStore();

const { getProjectOptions } = useTerraHelperComposable();

const form = ref({
  projects: [],
});

const projects_list = computed(() => getProjectOptions());

const group_project_names_by_uid = computed(() => {
  return projects_list.value.reduce((acc, layer) => {
    if (!acc[layer.name]) {
      acc[layer.name] = {};
    }
    layer.projects.forEach((sub) => {
      acc[layer.name][sub.name] = sub.uid;
    });
    return acc;
  }, {});
});

async function onContinue() {
  logger.log(props.functionCall);
  const selected_sublayers = form.value.projects;
  const selected_layers_data = projects_list.value.reduce((acc, layer) => {
    const is_layer_visible = layer.projects.some(sub => selected_sublayers.includes(sub.uid));
    if (is_layer_visible) {
      acc.push({
        layer: layer.name,
        sublayers: layer.projects.filter(sub => selected_sublayers.includes(sub.uid)).map(sub => sub.name),
      });
    }
    layer.projects.forEach((sub) => {
      terra_store.active_projects_map[sub.uid] = {
        ortho_enabled: false,
        features_enabled: false,
      };
    });
    return acc;
  }, []);

  // Toggle projects
  const projects = [];
  selected_sublayers.forEach((project_uid) => {
    const project = terra_store.active_projects_data_map({ all_projects: true })[project_uid];
    terra_store.active_projects_map[project.uid] = {
      ortho_enabled: true,
      features_enabled: true,
    };
    projects.push(project);
  });
  await terra_store.set_projects_essentials({ projects });
  await terra_store.update_map_features_and_polygon();
  if (projects.length === 1) {
    terra_store.fly_to_project({ project: projects[0] });
  }

  const data = {
    response: selected_layers_data,
  };
  emit('continue', data);
}

function onCancel() {
  emit('cancel');
}

// Initialize form with empty projects
function getDefaultSelected() {
  props.functionCall.arguments.layers.forEach(({ layer_name, sublayers }) => {
    if (group_project_names_by_uid.value[layer_name]) {
      sublayers.forEach((sublayer) => {
        const project_uid = group_project_names_by_uid.value[layer_name][sublayer];
        const is_visible = props.functionCall.arguments.visibility !== 'hide';
        if (project_uid) {
          terra_store.active_projects_map[project_uid] = {
            ortho_enabled: is_visible,
            features_enabled: is_visible,
          };
        }
      });
    }
  });
  return Object.entries(terra_store.active_projects_map)
    .filter(([_, project]) => project.ortho_enabled || project.features_enabled)
    .map(([uid, _]) => uid);
}
</script>

<template>
  <NaavixActionBox @continue="onContinue" @cancel="onCancel">
    <template #header>
      <div class="flex items-center gap-3">
        <HawkFeaturedIcon theme="light-circle" color="warning">
          <IconHawkAlertTriangle />
        </HawkFeaturedIcon>
        <div class="text-md font-semibold text-gray-900">
          Toggle layers
        </div>
      </div>
    </template>
    <template #content>
      <div class="text-xs font-normal text-gray-600">
        Are you sure you want to toggle the layers?
      </div>
      <Vueform
        v-model="form"
        size="sm"
        :columns="{
          default: { container: 12, label: 4, wrapper: 12 },
          sm: { container: 12, label: 4, wrapper: 12 },
          md: { container: 12, label: 4, wrapper: 12 },
        }"
        sync
        :display-errors="false"
      >
        <div class="col-span-12">
          <HawkTreeSelect
            class="my-5"
            :options="{
              name: 'projects',
              label: $t('Layers'),
              openDirection: 'top',
            }"
            select_type="LEAF_PRIORITY"
            children_key="projects"
            label_key="name"
            value_key="uid"
            :data="projects_list"
            :initial_state="getDefaultSelected()"
            @update-form="form.projects = $event"
          />
        </div>
      </Vueform>
    </template>
  </NaavixActionBox>
</template>
