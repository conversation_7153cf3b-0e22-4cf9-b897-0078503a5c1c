<script setup>
import { reactive } from 'vue';
import TerraFilters from '~/terra/components/terra-filters.vue';
import { useTerraStore } from '~/terra/store/terra.store';

const props = defineProps({
  functionCall: {
    type: Object,
    required: true,
  },
});

const emits = defineEmits(['continue', 'cancel']);

const terra_store = useTerraStore();

const state = reactive({
  status: 'not_started', // Initial status
});

const filters_ref$ = ref(null);

watch(() => filters_ref$.value?.getFiltersData(), (data) => {
  if (state.status === 'not_started' && data && data?.filter_options !== null) {
    if (props.functionCall?.name === 'get_filters') {
      const filters_data = Object.entries(data.filter_options).reduce((acc, [key, values_object]) => {
        acc.push({
          filter: key,
          values: Object.entries(values_object).map(([value, properties]) => ({
            name: value,
            count: properties.count,
          })),
        });
        return acc;
      }, []);
      logger.log(`Filters data:`, filters_data);
      emits('continue', {
        response: filters_data,
      });
      state.status = 'completed'; // Update status to completed
    }
    if (props.functionCall?.name === 'set_filters') {
      data.resetFilters();
      // Apply filters from function call arguments
      const filters = props.functionCall?.arguments?.filters || [];
      for (let i = 0; i < filters.length; i++) {
        const { filter, values } = filters[i];
        if (values?.length) {
          values.forEach((value) => {
            if (terra_store.filters_state.key_values_data?.[filter]?.[value])
              terra_store.filters_state.key_values_data[filter][value].selected = true;
          });
        }
      }
      data.updateFeaturesCount();
      emits('continue', {
        response: [
          {
            message: 'Filters applied successfully',
          },
        ],
      });
      state.status = 'completed'; // Update status to completed
    }
  }
});
</script>

<template>
  <div v-if="state.status === 'not_started'" style="position: absolute; top: -9999px; left: -9999px;">
    <TerraFilters ref="filters_ref$" />
  </div>
</template>

<style scoped lang="scss">

</style>
