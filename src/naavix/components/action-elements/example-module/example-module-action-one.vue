<script setup>
const props = defineProps({
  functionCall: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['continue', 'cancel']);

function onContinue() {
  logger.log(props.functionCall);
  // Can do something custom before emitting the continue event like maybe a confirmation modal.
  const data = {
    response: 'Dummy action two triggered!',
  };
  emit('continue', data);
}

function onCancel() {
  emit('cancel');
}
</script>

<template>
  <NaavixActionBox @continue="onContinue" @cancel="onCancel">
    <template #header>
      <div class="flex items-center gap-3">
        <HawkFeaturedIcon theme="light-circle" color="warning">
          <IconHawkAlertTriangle />
        </HawkFeaturedIcon>
        <div class="text-md font-semibold text-gray-900">
          Continue the dummy action?
        </div>
      </div>
    </template>
    <template #content>
      <div class="text-xs font-normal text-gray-600">
        Are you sure you want to continue the dummy action?
      </div>
    </template>
  </NaavixActionBox>
</template>
