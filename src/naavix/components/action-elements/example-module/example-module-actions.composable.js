import ExampleModuleActionOne from '~/naavix/components/action-elements/example-module/example-module-action-one.vue';

export function useExampleModuleActionHelpers() {
  // Use example module store or whatever is needed.

  function getExampleModuleActionsMap() {
    return {
      example_module_action_one: {
        type: 'implicit',
        // action is needed only for implicit actions. For explicit actions, the action is defined in the component.
        action: (function_call, callback_fn) => {
          logger.log('function_call:', function_call);
          const data = {
            response: 'Dummy action one triggered!',
          };
          callback_fn(data);
        },
      },
      example_module_action_two: {
        type: 'explicit',
        // component is needed only for explicit actions.
        component: ExampleModuleActionOne,
      },
    };
  }

  return {
    getExampleModuleActionsMap,
  };
}
