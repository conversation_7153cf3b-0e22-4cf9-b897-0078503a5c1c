<script setup>
import { computed, defineComponent, h, ref } from 'vue';

const props = defineProps({
  functionCall: {
    type: Object,
    default: () => ({}),
  },
  functionResponse: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const { copy } = useClipboard();

const state = reactive({
  is_just_copied: '',
});

function copyObject(object, type) {
  state.is_just_copied = type;
  copy(JSON.stringify(object));
  setTimeout(() => {
    state.is_just_copied = '';
  }, 1000);
}

const ObjectNode = defineComponent({
  name: 'ObjectNode',
  props: {
    value: {
      type: [Object, Array, String, Number, Boolean, null],
      required: true,
    },
    keyName: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const expanded = ref(true);

    function toggle() {
      expanded.value = !expanded.value;
    }

    function isObject(val) {
      return val && typeof val === 'object' && !Array.isArray(val);
    }

    function isArray(val) {
      return Array.isArray(val);
    }

    const valueClass = computed(() => {
      const val = props.value;
      if (val === null)
        return 'text-gray-400';
      if (typeof val === 'string')
        return 'text-[#F9AB70]';
      if (typeof val === 'number')
        return 'text-[#F9AB70]';
      if (typeof val === 'boolean')
        return 'text-[#F9AB70]';
      return 'text-gray-200';
    });

    const preview = computed(() => {
      const val = props.value;
      if (val === null)
        return 'null';
      if (isArray(val))
        return `Array(${val.length})`;
      if (isObject(val))
        return `Object`;
      if (typeof val === 'string')
        return `"${val}"`;
      return String(val);
    });

    return {
      expanded,
      toggle,
      valueClass,
      preview,
      isObject,
      isArray,
    };
  },
  render() {
    const renderChildren = () => {
      if (!this.expanded || (!this.isObject(this.value) && !this.isArray(this.value))) {
        return null;
      }

      return h('div', { class: 'ml-4' }, Object.entries(this.value).map(([key, val]) => {
        return h('div', { class: 'naavix-debug-tree-item', key }, [
          h(ObjectNode, { value: val, keyName: key }),
        ]);
      }));
    };

    if (this.isObject(this.value) || this.isArray(this.value)) {
      return h('div', { class: 'naavix-debug-tree-node' }, [
        h('div', { class: 'flex items-start' }, [
          h('div', {
            class: 'flex items-center cursor-pointer hover:bg-[#2A2F45] rounded px-1',
            onClick: this.toggle,
          }, [
            h('span', { class: 'mr-1 select-none text-[#7FCFFF]' }, this.expanded ? '▼' : '▶'),
            this.keyName ? h('span', { class: 'naavix-debug-property-key' }, `${this.keyName}:`) : null,
            h('span', { class: 'naavix-debug-property-type' }, this.isArray(this.value) ? 'Array' : 'Object'),
            !this.expanded
              ? h('span', { class: 'text-gray-300 ml-2' }, this.isArray(this.value)
                ? `${this.value.length} items`
                : `${Object.keys(this.value).length} properties`)
              : null,
          ]),
        ]),
        renderChildren(),
      ]);
    }
    else {
      return h('div', { class: 'naavix-debug-tree-node' }, [
        h('div', { class: 'flex items-start' }, [
          this.keyName ? h('span', { class: 'naavix-debug-property-key' }, `${this.keyName}:`) : null,
          h('span', { class: this.valueClass }, this.preview),
        ]),
      ]);
    }
  },
});
</script>

<template>
  <HawkModalContainer content_class="w-[80vw]">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          [DEBUG] Function call and response
        </template>
      </HawkModalHeader>
      <HawkModalContent>
        <div class="text-md font-medium text-gray-700">
          Function call:
        </div>
        <div class="bg-[#1E2130] relative p-4 rounded-lg overflow-auto font-mono text-sm max-h-[calc(70vh-4rem)]">
          <div
            :key="state.is_just_copied"
            v-tippy="state.is_just_copied === 'functionCall' ? 'Copied' : 'Copy to clipboard'"
            class="absolute right-2 top-2 items-center p-2 rounded-lg cursor-pointer"
            @click="copyObject(props.functionCall, 'functionCall')"
          >
            <IconHawkCheckCircleGreen v-if="state.is_just_copied === 'functionCall'" class="text-white" />
            <IconHawkCopyTwo v-else class="text-white" />
          </div>
          <ObjectNode :value="props.functionCall" />
        </div>
        <div class="text-md font-medium text-gray-700 mt-4">
          Function response:
        </div>
        <div class="bg-[#1E2130] relative p-4 rounded-lg overflow-auto font-mono text-sm max-h-[calc(70vh-4rem)]">
          <div
            :key="state.is_just_copied"
            v-tippy="state.is_just_copied === 'functionResponse' ? 'Copied' : 'Copy to clipboard'"
            class="absolute right-2 top-2 items-center p-2 rounded-lg cursor-pointer"
            @click="copyObject(props.functionResponse, 'functionResponse')"
          >
            <IconHawkCheckCircleGreen v-if="state.is_just_copied === 'functionResponse'" class="text-white" />
            <IconHawkCopyTwo v-else class="text-white" />
          </div>
          <ObjectNode :value="props.functionResponse" />
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>

<style>
.naavix-debug-tree-node {
  padding: 1px 0;
}
.naavix-debug-tree-item {
  min-height: 1.2em;
}
.naavix-debug-property-key {
  color: #7FCFFF;
  margin-right: 0.5rem;
}
.naavix-debug-property-type {
  color: #7FCFFF;
}
</style>
