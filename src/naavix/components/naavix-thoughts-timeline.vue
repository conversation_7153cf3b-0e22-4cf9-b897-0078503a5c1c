<script setup>
import IconHawkTableTwo from '~icons/hawk/table-two';
import { keyBy } from 'lodash-es';
import { useModal } from 'vue-final-modal';
import { useAuthStore } from '~/auth/stores/auth.store';
import NaavixDebugModal from '~/naavix/components/naavix-debug-modal.vue';
import { useNaavixHelpers } from '~/naavix/composables/naavix-helpers.composable';

const props = defineProps({
  functionCalls: {
    type: Array,
    default: () => [],
  },
  functionResponses: {
    type: Array,
    default: () => [],
  },
});

const auth_store = useAuthStore();

const state = reactive({
  are_thoughts_expanded: false,
});

const { openTableModal, openChartModal } = useNaavixHelpers();

const naavix_debug_modal = useModal({
  component: NaavixDebugModal,
  attrs: {
    onClose() {
      naavix_debug_modal.close();
    },
  },
});

const function_responses_map = computed(() => {
  return keyBy(props.functionResponses, 'id');
});

const function_calls_map = computed(() => {
  return keyBy(props.functionCalls, 'id');
});

const timeline = computed(() => {
  return Object.keys(function_responses_map.value).map((id) => {
    return function_calls_map.value[id];
  });
});

function handleThoughtClick(thought) {
  if (function_responses_map.value[thought.id].response.artifact_type === 'table') {
    openTableModal(function_responses_map.value[thought.id].response);
  }
  else if (function_responses_map.value[thought.id].response.artifact_type === 'chart') {
    openChartModal(function_responses_map.value[thought.id].response, thought.title, thought.description);
  }
}

function handleDebugClick(function_call, function_response) {
  naavix_debug_modal.patchOptions({
    attrs: {
      functionCall: function_call,
      functionResponse: function_response,
    },
  });
  naavix_debug_modal.open();
}
</script>

<template>
  <div v-if="timeline.length">
    <div class="text-sm font-normal text-gray-600 cursor-pointer flex items-center gap-1 mb-2" @click="state.are_thoughts_expanded = !state.are_thoughts_expanded">
      <IconHawkChevronDown v-if="state.are_thoughts_expanded" class="w-4 h-4" />
      <IconHawkChevronRight v-else class="w-4 h-4" />
      Reasoning
    </div>
    <div v-if="state.are_thoughts_expanded" class="mb-2">
      <div
        v-for="thought in timeline"
        :key="thought.id"
        class="p-2 pl-5"
      >
        <div
          class="text-xs font-semibold text-gray-600 flex items-center gap-1"
          :class="['table', 'chart'].includes(function_responses_map[thought.id].response.artifact_type) ? 'hover:underline cursor-pointer hover:text-gray-900' : ''"
          @click="handleThoughtClick(thought)"
        >
          <IconHawkTableTwo v-if="function_responses_map[thought.id].response.artifact_type === 'table'" class="w-3 h-3" />
          <IconHawkBarChartTen v-else-if="function_responses_map[thought.id].response.artifact_type === 'chart'" class="w-3 h-3" />
          <IconHawkLightbulbTwo v-else class="w-3 h-3" />
          {{ thought.title }}
          <IconHawkCodeTwo
            v-if="auth_store.check_split('naavix_debug')"
            class="w-4 h-4 cursor-pointer"
            @click.prevent.stop="handleDebugClick(thought, function_responses_map[thought.id])"
          />
        </div>
        <div class="text-xs font-normal text-gray-600 border-l-2 pl-[9px] ml-[5px]">
          {{ thought.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "../styles/stylesheet.scss";
</style>
