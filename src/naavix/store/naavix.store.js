import { nanoid } from 'nanoid';
import { acceptHMRUpdate, defineS<PERSON> } from 'pinia';

export const useNaavixStore = defineStore('naavix', {
  state: () => ({
    is_agent_setup: true,
    are_messages_loading: false,
    active_chat_id: nanoid(),
    messages: [],
    all_chats: [],
    artifacts_map: {},
    is_explicit_function_call_active: false,
  }),
  getters: {
  },
  actions: {
    async set_chats() {
      const { data: sessions_list } = await this.$services.ai.get_sessions({
        agent_name: 'terra',
        query: {
          resource_id: 'terra-container-id',
          resource_type: 'container',
        },
      });
      this.all_chats = sessions_list.sessions;
    },
    async set_chat(chat_id) {
      this.active_chat_id = chat_id;
      this.are_messages_loading = true;
      this.messages = [];
      let last_role = null;
      const messages = [];

      const { data: session_details } = await this.$services.ai.get_session({
        agent_name: 'terra',
        session_uuid: chat_id,
        query: {
          resource_id: 'terra-container-id',
          resource_type: 'container',
        },
      });

      session_details.events.forEach((event) => {
        if (event.role !== last_role) {
          const current_message = {
            id: nanoid(),
            sender: event.role === 'user' ? 'user' : 'assistant',
            message: event.role === 'user' ? event.content : '',
            ...(event.role !== 'user' && {
              function_calls: [],
              function_responses: [],
            }),
          };
          messages.push(current_message);
          last_role = event.role;
        }

        if (event.role !== 'user') {
          if (event.event === 'message') {
            messages[messages.length - 1].message += event.content;
          }
          else if (event.event === 'function_call') {
            messages[messages.length - 1].message = '';
            messages[messages.length - 1].function_calls.push(event.content.function_call);
          }
          else if (event.event === 'function_response') {
            messages[messages.length - 1].function_responses.push(event.content.function_response);
          }
        }
      });
      if (this.active_chat_id === chat_id) {
        this.messages = messages;
        this.are_messages_loading = false;
      }
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useNaavixStore, import.meta.hot));
