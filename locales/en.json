{" Can view all dashboards.": " Can view all dashboards.", " Can view and share all dashboards.": " Can view and share all dashboards.", " draft saved": " draft saved", " has been published successfully.": " has been published successfully.", " has been saved as a draft successfully.": " has been saved as a draft successfully.", " published": " published", ".docx files only": ".docx files only", ".kml, .geojson formats supported": ".kml, .geojson formats supported", "“Shows daily count of modules scanned. Only scan additions are considered": "“Shows daily count of modules scanned. Only scan additions are considered", "*To approve": "*To approve", "*To reject": "*To reject", "# of tasks": "# of tasks", "% Actual": "% Actual", "% Actual progress": "% Actual progress", "% Complete": "% Complete", "% Delivered": "% Delivered", "% needed": "% needed", "% Planned progress": "% Planned progress", "% Remaining": "% Remaining", "% Remaining progress": "% Remaining progress", "1 January 2020": "1 January 2020", "1st": "1st", "2nd": "2nd", "3rd": "3rd", "4th": "4th", "A folder name can not contain any of the following characters": "A folder name can not contain any of the following characters", "A maximum of": "A maximum of", "A measurement with this symbol already exists. Please enter a different symbol to continue.": "A measurement with this symbol already exists. Please enter a different symbol to continue.", "A minimum of": "A minimum of", "A notification will be sent to the concerned person about the delegation.": "A notification will be sent to the concerned person about the delegation.", "A pending activity for this form is being processed on the server. Please check again in few minutes.": "A pending activity for this form is being processed on the server. Please check again in few minutes.", "A reminder can be configured only if the start/due date is set": "A reminder can be configured only if the start/due date is set", "A unified hub for managing and adding items across all organizational assets.": "A unified hub for managing and adding items across all organizational assets.", "A Unified Platform for Solar Projects": "A Unified Platform for Solar Projects", "A unique number for advance shipment": "A unique number for advance shipment", "AC Annual": "AC Annual", "Accept Invitation": "Accept Invitation", "access": "access", "Access": "Access", "Access all assets": "Access all assets", "Access all forms and create new forms.": "Access all forms and create new forms.", "Access all forms, create new forms and submit any form": "Access all forms, create new forms and submit any form", "Access all forms.": "Access all forms.", "Access all non-private drawings and their annotations/markups": "Access all non-private drawings and their annotations/markups", "Access all your previous invoices": " Access all your previous invoices", "Access and view items-stock and their quantities in warehouses to which they have access and view records of transactions created by them.": "Access and view items-stock and their quantities in warehouses to which they have access and view records of transactions created by them.", "Access controls": "Access controls", "Access level": "Access level", "Access only shared dashboards": "Access only shared dashboards", "Access only shared schedules": "Access only shared schedules", "Access permitted assets": "Access permitted assets", "Access provided directly or through teams will still be retained.": "Access provided directly or through teams will still be retained.", "Access user and team directory": "Access user and team directory", "Access users, teams and roles directory": "Access users, teams and roles directory", "Access view and download all documents": "Access view and download all documents", "Access, modify, submit and delete any form. Create new forms.": "Access, modify, submit and delete any form. Create new forms.", "Access, view and download only shared documents": "Access, view and download only shared documents", "Access, view, create and modify all items, transactions(at asset level), warehouses and vendors.": "Access, view, create and modify all items, transactions(at asset level), warehouses and vendors.", "Account": "Account", "Account not registered": "Account not registered", "Account Settings": "Account <PERSON><PERSON>", "accounts": "accounts", "Accounts": "Accounts", "Acquisition": "Acquisition", "Actions": "Actions", "Activate": "Activate", "Activate editor": "Activate editor", "Activate version": "Activate version", "Activated items will be shown in the items list and are allowed in transactions. Are you sure you want to activate the item?": "Activated items will be shown in the items list and are allowed in transactions. Are you sure you want to activate the item?", "Active": "Active", "Active Sessions": "Active Sessions", "Active Status": "Active Status", "Active Version changed": "Active Version changed", "activities": "activities", "Activities": "Activities", "Activities breakdown": "Activities breakdown", "Activities chart": "Activities chart", "Activities completed": "Activities completed", "Activities count": "Activities count", "Activities progress": "Activities progress", "activities started": "activities started", "Activities table": "Activities table", "activity": "activity", "Activity": "Activity", "Activity and reminders": "Activity and reminders", "Activity codes": "Activity codes", "Activity Codes": "Activity Codes", "Activity details": "Activity details", "Activity doesn't have parents": "Activity doesn't have parents", "Activity history": "Activity history", "Activity History": "Activity History", "Activity history table": "Activity history table", "Activity ID": "Activity ID", "Activity is ahead by": "Activity is ahead by", "Activity is behind by": "Activity is behind by", "Activity is on track": "Activity is on track", "Activity name": "Activity name", "Activity progress table": "Activity progress table", "Activity related to all tasks and forms you've been added to as users or watcher": "Activity related to all tasks and forms you've been added to as users or watcher", "Activity status": "Activity status", "Activity Status": "Activity Status", "Activity tracking": "Activity tracking", "Activity trackings of all the activities in the schedule associated to this/these resource(s) will be removed.": "Activity trackings of all the activities in the schedule associated to this/these resource(s) will be removed.", "Activity updates": "Activity updates", "Actor": "Actor", "Actual": "Actual", "Actual cannot be greater than planned": "Actual cannot be greater than planned", "Actual cost": "Actual cost", "Actual Cost": "Actual Cost", "Actual duration": "Actual duration", "Actual Duration": "Actual Duration", "Actual finish": "Actual finish", "Actual Finish": "Actual Finish", "Actual finish date": "Actual finish date", "Actual progress": "Actual progress", "Actual start": "Actual start", "Actual Start": "Actual Start", "Actual start date": "Actual start date", "Actual Value": "Actual Value", "Actual Work": "Actual Work", "Add": "Add", "Add a form to a transaction or an item to gather data.": "Add a form to a transaction or an item to gather data.", "Add a new item here": "Add a new item here", "Add a new workflow template to automate your daily tasks.": "Add a new workflow template to automate your daily tasks.", "Add a sibling milestone": "Add a sibling milestone", "Add a sibling task": "Add a sibling task", "Add a subtask": "Add a subtask", "Add account": "Add account", "Add activities": "Add activities", "Add activity tracking": "Add activity tracking", "Add additional reviewers": "Add additional reviewers", "Add Approvers / Reviewers": "Add Approvers / Reviewers", "Add Assets": "Add Assets", "Add atleast one document here": "Add atleast one document here", "Add atleast one status here": "Add atleast one status here", "Add attachment": "Add attachment", "Add attachments": "Add attachments", "Add Attachments": "Add Attachments", "Add block name": "Add block name", "Add card": "Add card", "Add Card details": "Add Card details", "Add category": "Add category", "Add child": "Add child", "Add Class": "Add Class", "Add column after": "Add column after", "Add column before": "Add column before", "Add comments here": "Add comments here", "Add component": "Add component", "Add condition": "Add condition", "Add Condition": "Add Condition", "Add custom resources": "Add custom resources", "Add Dependencies": "Add Dependencies", "Add dependencies, attachments, etc to this activity.": "Add dependencies, attachments, etc to this activity.", "Add dependency": "Add dependency", "Add description": "Add description", "Add Description": "Add Description", "Add document placeholders, upload files/folders": "Add document placeholders, upload files/folders", "Add documents": "Add documents", "Add domains to allow your team members to find your account with": "Add domains to allow your team members to find your account with", "Add Feature": "Add Feature", "Add feature type": "Add feature type", "Add Feature type": "Add Feature type", "Add field": "Add field", "Add Field": "Add Field", "Add fields": "Add fields", "Add fields to be tracked, assign weights and configure feature types to reach at each percentage steps": "Add fields to be tracked, assign weights and configure feature types to reach at each percentage steps", "Add file": "Add file", "Add files": "Add files", "Add filter": "Add filter", "Add form": "Add form", "Add forms": "Add forms", "Add Geofencing": "Add Geofencing", "Add group": "Add group", "Add Group": "Add Group", "Add Information": "Add Information", "Add information here": "Add information here", "Add Information/CC": "Add Information/CC", "Add Inspection Form": "Add Inspection Form", "Add item": "Add item", "Add Item": "Add Item", "Add item details": "Add item details", "Add item type": "Add item type", "Add items": "Add items", "Add layer": "Add layer", "Add linked task": "Add linked task", "Add manually": "Add manually", "Add marker": "Add marker", "Add markups": "Add markups", "Add measurement unit": "Add measurement unit", "Add members": "Add members", "Add members and teams": "Add members and teams", "Add Milestone": "Add Milestone", "Add milestone above": "Add milestone above", "Add milestone below": "Add milestone below", "Add new": "Add new", "Add new attachment": "Add new attachment", "Add new category": "Add new category", "Add New Group": "Add New Group", "Add new item": "Add new item", "Add new item type": "Add new item type", "Add New Layer": "Add New Layer", "Add new sub-layer": "Add new sub-layer", "Add New Template": "Add New Template", "Add new versions, share, update document properties for all files and folders": "Add new versions, share, update document properties for all files and folders", "Add new versions, share, update document properties only for items shared with edit access": "Add new versions, share, update document properties only for items shared with edit access", "Add notes": "Add notes", "Add notes & attachments for all defects": "Add notes & attachments for all defects", "Add notes & attachments for assigned defects": "Add notes & attachments for assigned defects", "Add notes here": "Add notes here", "Add Option": "Add Option", "Add or change construction progress workflows association for respective vectors (only for Sensehawk users)": "Add or change construction progress workflows association for respective vectors (only for Sensehawk users)", "Add or copy data into table": "Add or copy data into table", "Add Ordered quantity": "Add Ordered quantity", "Add pallet": "Add pallet", "Add Property": "Add Property", "Add reference": "Add reference", "Add references": "Add references", "Add references to tasks, files and forms that have been added in their respective modules.": "Add references to tasks, files and forms that have been added in their respective modules.", "Add reminder": "Add reminder", "Add Reminder": "<PERSON><PERSON>", "Add Report": "Add Report", "Add resource": "Add resource", "Add resources": "Add resources", "Add reviewers": "Add reviewers", "Add role": "Add role", "Add row": "Add row", "Add row after": "Add row after", "Add row before": "Add row before", "Add rule": "Add rule", "Add Scope": "<PERSON>d <PERSON>", "Add Section": "Add Section", "Add sections/fields to form for data collection": "Add sections/fields to form for data collection", "Add Serial numbers": "Add Serial numbers", "Add Shipment": "Add Shipment", "Add Signature": "Add Signature", "Add status": "Add status", "Add step": "Add step", "Add Stock": "Add Stock", "Add sub item": "Add sub item", "Add sub step": "Add sub step", "Add sub-category": "Add sub-category", "Add Subcategory": "Add Subcategory", "Add subtask": "Add subtask", "Add Subtasks": "Add Subtasks", "Add summary": "Add summary", "Add task": "Add task", "Add Task": "Add Task", "Add task above": "Add task above", "Add task below": "Add task below", "Add task that is blocked": "Add task that is blocked", "Add team": "Add team", "Add template": "Add template", "Add Template": "Add Template", "Add to all assets.": "Add to all assets.", "Add to filters": "Add to filters", "Add to Filters": "Add to Filters", "Add unassigned sheets": "Add unassigned sheets", "Add users to this team": "Add users to this team", "Add values dynamically": "Add values dynamically", "Add values manually": "Add values manually", "Add Vendor": "Add <PERSON>", "Add View": "Add View", "Add waiting on task": "Add waiting on task", "Add Warehouse": "Add Warehouse", "Add warehouse type": "Add warehouse type", "Add Widget": "Add Widget", "Add Workflow": "Add Workflow", "Add your comment": "Add your comment", "Add your message here": "Add your message here", "Add, modify and remove classes and groups": "Add, modify and remove classes and groups", "Add, update and delete annotations/markups": "Add, update and delete annotations/markups", "Add, update and delete construction progress workflows": "Add, update and delete construction progress workflows", "Add, update and delete construction progress workflows (only for Sensehawk users)": "Add, update and delete construction progress workflows (only for Sensehawk users)", "Add, update daily construction progress": "Add, update daily construction progress", "Add, update, remove dashboards and widgets": "Add, update, remove dashboards and widgets", "Add/Edit members": "Add/Edit members", "Add/manage subtasks": "Add/manage subtasks", "Add/modify feature type groups": "Add/modify feature type groups", "Add/modify terra progress workflows": "Add/modify terra progress workflows", "Add/Update feature properties, feature type. Enables scanning from scan app": "Add/Update feature properties, feature type. Enables scanning from scan app", "Add/Update feature properties, feature type. Enables scanning from scan app.": "Add/Update feature properties, feature type. Enables scanning from scan app.", "Add/update properties, change classes. Scan barcodes using mobile application": "Add/update properties, change classes. Scan barcodes using mobile application", "Add/update team details": "Add/update team details", "added": "added", "added a comment": "added a comment", "added a comment in": "added a comment in", "added a new file": "added a new file", "added a new folder": "added a new folder", "added a new version": "added a new version", "added a text": "added a text", "Added an annotation": "Added an annotation", "added assignees to the form": "added assignees to the form", "Added columns": "Added columns", "Added Columns": "Added Columns", "Added fields": "Added fields", "Added filters": "Added filters", "added in this project": "added in this project", "added members to the form": "added members to the form", "added members to the template": "added members to the template", "Added on annotation": "Added on annotation", "added subtask": "added subtask", "added the block": "added the block", "added the field": "added the field", "added the section": "added the section", "added the tags": "added the tags", "Additional details": "Additional details", "Additional reviewer's feedback": "Additional reviewer's feedback", "Additional reviewers' feedback": "Additional reviewers' feedback", "Additions": "Additions", "Address": "Address", "Adds all active users to the team": "Adds all active users to the team", "Adjusted Δ Temp": "Adjusted Δ Temp", "Admin": "Admin", "Admins": "Admins", "Advance options": "Advance options", "Advance Shipment": "Advance Shipment", "Advance Shipments": "Advance Shipments", "Advanced": "Advanced", "Advanced + Modify map data - name, description, add/update/delete projects, groups for self-serve terra,Add/modify feature type groups and types": "Advanced + Modify map data - name, description, add/update/delete projects, groups for self-serve terra,Add/modify feature type groups and types", "Advanced access:": "Advanced access:", "Advanced filters": "Advanced filters", "Advanced options": "Advanced options", "Advanced, Manager and admins can access this map": "Advanced, Manager and admins can access this map", "Affected": "Affected", "Affected Capacity": "Affected Capacity", "Affected modules": "Affected modules", "after": "after", "After": "After", "After submission": "After submission", "Aggregate": "Aggregate", "Aggregate values across columns using formula": "Aggregate values across columns using formula", "Aggregate values column-wise": "Aggregate values column-wise", "Aggregation": "Aggregation", "ahead": "ahead", "Ahead": "Ahead", "Alert": "<PERSON><PERSON>", "Alerts": "<PERSON><PERSON><PERSON>", "Aliases": "Aliases", "Alignment": "Alignment", "All": "All", "All activities": "All activities", "All activities and resources will use the below working days and hours and the activities are auto-scheduled using the same calendar": "All activities and resources will use the below working days and hours and the activities are auto-scheduled using the same calendar", "All activities in the schedule will use the selected timezone.": "All activities in the schedule will use the selected timezone.", "All activities, automations and notifications in the schedule will use the selected timezone.": "All activities, automations and notifications in the schedule will use the selected timezone.", "All assets": "All assets", "All attachments": "All attachments", "All comments": "All comments", "All conditions are met": "All conditions are met", "All conditions are met (AND)": "All conditions are met (AND)", "All dates are represented in": "All dates are represented in", "All documents are either closed or approved. Transmittal can be closed.": "All documents are either closed or approved. Transmittal can be closed.", "All existing values will be overwritten by the copied data. This operation is not reversible. Are you sure you want to continue?": "All existing values will be overwritten by the copied data. This operation is not reversible. Are you sure you want to continue?", "All files": "All files", "All forms": "All forms", "All images": "All images", "All Items": "All Items", "All layers": "All layers", "All levels": "All levels", "All of the following are true": "All of the following are true", "All required columns are mapped": "All required columns are mapped", "All results": "All results", "All sections/fields will be duplicated to the new block": "All sections/fields will be duplicated to the new block", "All submissions": "All submissions", "All the activities assigned to this resource have been completed.": "All the activities assigned to this resource have been completed.", "All time": "All time", "All weeks": "All weeks", "Allow access to": "Allow access to", "Allow access to all groups/layers": "Allow access to all groups/layers", "Allow access to only selected groups and layers": "Allow access to only selected groups and layers", "Allow adding/removing rows": "Allow adding/removing rows", "Allow all asset members": "Allow all asset members", "Allow file types": "Allow file types", "Allow form assignees to rollback to any of the completed steps": "Allow form assignees to rollback to any of the completed steps", "Allow members to view the feedback, status, annotations of other members": "Allow members to view the feedback, status, annotations of other members", "Allow modifying prefilled data": "Allow modifying prefilled data", "Allow multiple selection": "Allow multiple selection", "Allow updates and submission to the form(on mobile) only if the user is within the given proximity of the form location.": "Allow updates and submission to the form(on mobile) only if the user is within the given proximity of the form location.", "Allow updates to the task(on mobile) only if the user is within the given proximity of the task location": "Allow updates to the task(on mobile) only if the user is within the given proximity of the task location", "Allow users to unpublish, make changes to the schedule, and publish": "Allow users to unpublish, make changes to the schedule, and publish", "Allow using this workflow in all assets": "Allow using this workflow in all assets", "Allowed Members": "Allowed Members", "Allows you to copy data from this form and paste it into another form of the same template": "Allows you to copy data from this form and paste it into another form of the same template", "Already added": "Already added", "already exists": "already exists", "Already have an account": "Already have an account", "already submitted forms": "already submitted forms", "AM": "AM", "Amount": "Amount", "Amount spent": "Amount spent", "An email invitation has been sent to the users to join the asset": "An email invitation has been sent to the users to join the asset", "An email invitation has been sent to the users to join the organization": "An email invitation has been sent to the users to join the organization", "An SDP Inventory category that deals with safety/EHS items.": "An SDP Inventory category that deals with safety/EHS items.", "Analytics": "Analytics", "Analytics and Reporting.": "Analytics and Reporting.", "Analyze": "Analyze", "and": "and", "and are safety hazards. These need to be addressed immediately.": "and are safety hazards. These need to be addressed immediately.", "and not under severity level.": "and not under severity level.", "And the next form will be scheduled as follows": "And the next form will be scheduled as follows", "And the next task will be scheduled as follows": "And the next task will be scheduled as follows", "Annexure: Anomaly distribution - Thermal Orthomap": "Annexure: Anomaly distribution - The<PERSON> Orthomap", "Annexure: Anomaly distribution - Thermal Orthomap Legend": "Annexure: Anomaly distribution - Thermal Orthomap Legend", "Annotations": "Annotations", "Annotations downloaded": "Annotations downloaded", "Announcements": "Announcements", "Annually": "Annually", "Any": "Any", "Any condition is met": "Any condition is met", "Any edits done to the form blocks of this template may require you to upload a new document.": "Any edits done to the form blocks of this template may require you to upload a new document.", "Any numeric value": "Any numeric value", "Any of the conditions are met": "Any of the conditions are met", "Any of the conditions are met (OR)": "Any of the conditions are met (OR)", "Anyone": "Anyone", "Anyone added in this project": "Anyone added in this project", "Anyone can access": "Anyone can access", "Anyone in the asset": "Anyone in the asset", "Anyone in the Asset": "Anyone in the Asset", "Anyone in the organization": "Anyone in the organization", "Anyone in the Organization": "Anyone in the Organization", "App filters": "App filters", "Applications": "Applications", "Applied": "Applied", "Applies only for mobile": "Applies only for mobile", "Apply": "Apply", "Apply changes to preview": "Apply changes to preview", "Apply numbering for": "Apply numbering for", "Apply schema as global": "Apply schema as global", "Apply schema to existing files": "Apply schema to existing files", "Apply schema to sub folders": "Apply schema to sub folders", "Apply to": "Apply to", "Apply to all sheets in the drawing": "Apply to all sheets in the drawing", "Approval": "Approval", "Approval Block": "Approval Block", "Approval category": "Approval category", "Approval process": "Approval process", "Approvals": "Approvals", "approve": "Approve", "Approve": "Approve", "Approve with comments": "Approve with comments", "Approved": "Approved", "approved the document": "approved the document", "Approved with comments": "Approved with comments", "approver": "approver", "Approver": "Approver", "Approvers": "Approvers", "Apr": "Apr", "April": "April", "Archive": "Archive", "Archive item(s)": "Archive item(s)", "Archive Task": "Archive Task", "Archive Tasks": "Archive Tasks", "Archive Transmittal": "Archive Transmittal", "archived": "archived", "Archived": "Archived", "archived a file": "archived a file", "archived a folder": "archived a folder", "Archived forms": "Archived forms", "archived the file": "archived the file", "archived the folder": "archived the folder", "Archived the folder": "Archived the folder", "Are you sure to change classes?": "Are you sure to change classes?", "Are you sure you want end the flow?": "Are you sure you want end the flow?", "Are you sure you want to archive": "Are you sure you want to archive", "Are you sure you want to archive task": "Are you sure you want to archive task", "Are you sure you want to archive the item(s)? They will be moved to archived section and will be hidden.": "Are you sure you want to archive the item(s)? They will be moved to archived section and will be hidden.", "Are you sure you want to archive the Transmittal? It will be moved to archived section and will be hidden.": "Are you sure you want to archive the Transmittal? It will be moved to archived section and will be hidden.", "Are you sure you want to cancel this document request?": "Are you sure you want to cancel this document request?", "Are you sure you want to clear duplicates? This action cannot be undone.": "Are you sure you want to clear duplicates? This action cannot be undone.", "Are you sure you want to close this request? If yes, please add your reason for request cancellation.": "Are you sure you want to close this request? If yes, please add your reason for request cancellation.", "Are you sure you want to create": "Are you sure you want to create", "Are you sure you want to delete": "Are you sure you want to delete", "Are you sure you want to delete document status": "Are you sure you want to delete document status", "Are you sure you want to delete drawing": "Are you sure you want to delete drawing", "Are you sure you want to delete issue purpose": "Are you sure you want to delete issue purpose", "Are you sure you want to delete sheet": "Are you sure you want to delete sheet", "Are you sure you want to delete the activity": "Are you sure you want to delete the activity", "Are you sure you want to delete the container?": "Are you sure you want to delete the container?", "Are you sure you want to delete the feature type?": "Are you sure you want to delete the feature type?", "Are you sure you want to delete the feature?": "Are you sure you want to delete the feature?", "Are you sure you want to delete the features?": "Are you sure you want to delete the features?", "Are you sure you want to delete the group?": "Are you sure you want to delete the group?", "Are you sure you want to delete the item(s)? They will be moved to trash and will be permanently deleted after 30 days.": "Are you sure you want to delete the item(s)? They will be moved to trash and will be permanently deleted after 30 days.", "Are you sure you want to delete the layer?": "Are you sure you want to delete the layer?", "Are you sure you want to delete the link between": "Are you sure you want to delete the link between", "Are you sure you want to delete the values": "Are you sure you want to delete the values", "Are you sure you want to delete the view": "Are you sure you want to delete the view", "Are you sure you want to delete these items? This action cannot be undone.": "Are you sure you want to delete these items? This action cannot be undone.", "Are you sure you want to delete this": "Are you sure you want to delete this", "Are you sure you want to delete this component?": "Are you sure you want to delete this component?", "Are you sure you want to delete this connection?": "Are you sure you want to delete this connection?", "Are you sure you want to delete this instance? This action cannot be undone.": "Are you sure you want to delete this instance? This action cannot be undone.", "Are you sure you want to delete this schedule": "Are you sure you want to delete this schedule", "Are you sure you want to delete?": "Are you sure you want to delete?", "Are you sure you want to delete? This action cannot be undone.": "Are you sure you want to delete? This action cannot be undone.", "Are you sure you want to detach this component?": "Are you sure you want to detach this component?", "Are you sure you want to open this request again? If yes, please add your reason for opening the request.": "Are you sure you want to open this request again? If yes, please add your reason for opening the request.", "Are you sure you want to proceed?": "Are you sure you want to proceed?", "Are you sure you want to publish the report?": "Are you sure you want to publish the report?", "Are you sure you want to reinitiate the request for upload?": "Are you sure you want to reinitiate the request for upload?", "Are you sure you want to remove? This action cannot be undone.": "Are you sure you want to remove? This action cannot be undone.", "Are you sure you want to unarchive": "Are you sure you want to unarchive", "Are you sure you want to unarchive task": "Are you sure you want to unarchive task", "Are you sure you want to unarchive the Transmittal? It will be moved to the regular section and will be visible.": "Are you sure you want to unarchive the Transmittal? It will be moved to the regular section and will be visible.", "Are you sure you want to unlock and change the status of the document?": "Are you sure you want to unlock and change the status of the document?", "Are you sure you want to unlock this request?": "Are you sure you want to unlock this request?", "Are you sure you want to unpublish the form": "Are you sure you want to unpublish the form", "Are you sure you wish to proceed with this decision?": "Are you sure you wish to proceed with this decision?", "Area": "Area", "Area chart": "Area chart", "Area Chart": "Area Chart", "Arrow": "Arrow", "as a predecessor": "as a predecessor", "As Late As Possible": "As Late As Possible", "As of": "As of", "As Soon As Possible": "As Soon As Possible", "as the": "as the", "asset": "asset", "Asset": "<PERSON><PERSON>", "ASSET": "ASSET", "Asset admin": "Asset admin", "Asset admins": "Asset admins", "Asset breakdown": "Asset breakdown", "Asset Capacity (MW)": "Asset Capacity (MW)", "Asset code": "Asset code", "Asset deleted successfully": "Asset deleted successfully", "Asset details": "Asset details", "Asset fields": "Asset fields", "Asset item list": "Asset item list", "Asset Level": "Asset Level", "Asset meta data field": "Asset meta data field", "Asset metadata": "Asset metadata", "Asset Name": "Asset Name", "Asset properties": "Asset properties", "Asset property": "Asset property", "Asset settings": "Asset settings", "Asset Settings": "<PERSON><PERSON> Settings", "Assets": "Assets", "Assign": "Assign", "Assign and notify": "Assign and notify", "Assign and Notify": "Assign and Notify", "Assign assets maps to team and users": "Assign assets maps to team and users", "Assign Drawing": "Assign Drawing", "Assign group": "Assign group", "Assign Group": "Assign Group", "Assign groups": "Assign groups", "Assign Groups": "Assign Groups", "Assign roles to this team": "Assign roles to this team", "Assign roles to users": "Assign roles to users", "Assign sheets to show": "Assign sheets to show", "Assign team members to review & approve to proceed to the next step in the workflow.": "Assign team members to review & approve to proceed to the next step in the workflow.", "Assign the team to all members of the asset": "Assign the team to all members of the asset", "Assign the team to all members of the organization": "Assign the team to all members of the organization", "Assign to": "Assign to", "Assign to the user making progress update": "Assign to the user making progress update", "Assign weightage": "Assign weightage", "Assign weightages to folders, subfolders and files": "Assign weightages to folders, subfolders and files", "Assigned": "Assigned", "Assigned to": "Assigned to", "Assigned to me": "Assigned to me", "Assignee": "Assignee", "Assignee breakdown": "Assignee breakdown", "assignees": "assignees", "Assignees": "Assignees", "Assignees count": "Assignees count", "Assignees will receive a notification and will be responsible for marking it as done": "Assignees will receive a notification and will be responsible for marking it as done", "Assignment details": "Assignment details", "Associate blocks": "Associate blocks", "Associate Blocks": "Associate Blocks", "Associate metadata": "Associate metadata", "Associate workflows": "Associate workflows", "associated": "associated", "Associated feature": "Associated feature", "Associated with defects": "Associated with defects", "At least one document is required": "At least one document is required", "At least one field is required.": "At least one field is required.", "At least one member should be assigned to the step": "At least one member should be assigned to the step", "at this location?": "at this location?", "Attach fields": "Attach fields", "Attach file": "Attach file", "Attach files or documents that provide additional context or information for the activity.": "Attach files or documents that provide additional context or information for the activity.", "Attach form": "Attach form", "Attach Forms": "Attach Forms", "Attach task": "Attach task", "Attached successfully": "Attached successfully", "Attached to": "Attached to", "attachment": "attachment", "Attachment": "Attachment", "attachments": "attachments", "Attachments": "Attachments", "attachments added": "attachments added", "Attribute": "Attribute", "Audios": "Audios", "Audit logs": "Audit logs", "Aug": "Aug", "August": "August", "Authorised Signatory": "Authorised Signatory", "Authorized signatory": "Authorized signatory", "Auto Number": "Auto Number", "Auto numbering schema": "Auto numbering schema", "Auto sync": "Auto sync", "Auto-assigned": "Auto-assigned", "Auto-numbering will pick up the fields which are only above the auto-number field.": "Auto-numbering will pick up the fields which are only above the auto-number field.", "Automatch columns": "Automatch columns", "Automate Plan OCR": "Automate Plan OCR", "Automate transmittal number creation with a customized schema": "Automate transmittal number creation with a customized schema", "Automatic": "Automatic", "Automatic Closing": "Automatic Closing", "Automatically approve/reject if there is no outcome within the defined time period.": "Automatically approve/reject if there is no outcome within the defined time period.", "Automation": "Automation", "Automation based on change of status": "Automation based on change of status", "Automation based on custom field": "Automation based on custom field", "Automation based on frequency of days/weeks/months": "Automation based on frequency of days/weeks/months", "Automation based on planned finish date": "Automation based on planned finish date", "Automation based on planned start date": "Automation based on planned start date", "Automation based on progress exceeding or lagging": "Automation based on progress exceeding or lagging", "Automations": "Automations", "Autonumbering": "Autonumbering", "Autonumbering scope": "Autonumbering scope", "Available": "Available", "Available columns": "Available columns", "Available fields": "Available fields", "Available filters": "Available filters", "Available layers": "Available layers", "Available/Used (BOM)": "Available/Used (BOM)", "Average duration": "Average duration", "Average remaining duration": "Average remaining duration", "Average work rate": "Average work rate", "avg": "avg", "Avg": "Avg", "Avg % overdue tasks": "Avg % overdue tasks", "Back": "Back", "Back to details": "Back to details", "Back to Home": "Back to Home", "Back to Sign in": "Back to Sign in", "Background": "Background", "Background color": "Background color", "Bar chart": "Bar chart", "Barcode scan": "Barcode scan", "Base height": "Base height", "Baseline": "Baseline", "Baseline duration": "Baseline duration", "Baseline Duration": "Baseline Duration", "Baseline finish": "Baseline finish", "Baseline Finish": "Baseline Finish", "Baseline start": "Baseline start", "Baseline Start": "Baseline Start", "Basic": "Basic", "Basic + View published charts,Download project reports,Create/modify pivot,Add/Update feature extraProperties and change feature type, Modify feature type symbology": "Basic + View published charts,Download project reports,Create/modify pivot,Add/Update feature extraProperties and change feature type, Modify feature type symbology", "Basic access:": "Basic access:", "Basic details": "Basic details", "Basic options": "Basic options", "Basic schedule details": "Basic schedule details", "Bcc": "Bcc", "before": "before", "Before": "Before", "Begin crafting the template by providing basic details": "Begin crafting the template by providing basic details", "Behind": "Behind", "Below are the different transaction types available in the system. The workflows can be customized as per your business requirements. Get in touch with our team for help.": "Below are the different transaction types available in the system. The workflows can be customized as per your business requirements. Get in touch with our team for help.", "Below is the link for the recently generated submittal": "Below is the link for the recently generated submittal", "Better Load Balancing.": "Better Load Balancing.", "between": "between", "Bill of Material": "Bill of Material", "Bill Of Material (BOM)": "Bill Of Material (BOM)", "Bill of material could not be synced. Please try again.": "Bill of material could not be synced. Please try again.", "Bill of Materials": "Bill of Materials", "Billing": "Billing", "Billing History": " Billing History", "Billing of Materials": "Billing of Materials", "Billing/Subscription": "Billing/Subscription", "BL Finish": "BL Finish", "BL Start": "BL Start", "Black": "Black", "Blank": "Blank", "Blank schedule": "Blank schedule", "blank/not blank": "blank/not blank", "Block": "Block", "Block already exists": "Block already exists", "Block cloning failed!": "Block cloning failed!", "Block created successfully": "Block created successfully", "Block Deleted successfully": "Block Deleted successfully", "block executed": "block executed", "Block name is required": "Block name is required", "Block summaries": "Block summaries", "Block synced successfully": "Block synced successfully", "Block updated successfully": "Block updated successfully", "Block wise report": "Block wise report", "blocked": "blocked", "blocking": "blocking", "Blocking": "Blocking", "Blocks": "Blocks", "Blue": "Blue", "Bold": "Bold", "BOM": "BOM", "BOM (available/used)": "BOM (available/used)", "BOM report exported": "BOM report exported", "BOM saved": "BOM saved", "Bookmarked": "Bookmarked", "Both": "Both", "Bottom": "Bottom", "Bottom to top": "Bottom to top", "Breakdown by": "Breakdown by", "Breakdown scope and quantities by warehouses": "Breakdown scope and quantities by warehouses", "Breakdown the task using #checklists": "Breakdown the task using #checklists", "Browse files": "Browse files", "Browse Files": "Browse Files", "Browser": "Browser", "Build form": "Build form", "Build Form template for prepping a list of questions for your customers.": "Build Form template for prepping a list of questions for your customers.", "Build Workflow template for automating your daily tasks in one go": "Build Workflow template for automating your daily tasks in one go", "Builder": "Builder", "Bulk fill": "Bulk fill", "Bulk fill forms": "Bulk fill forms", "Bulk update": "Bulk update", "Bulk Update": "Bulk Update", "Bullet List": "Bullet List", "Button label": "Button label", "by": "by", "By logging in to Sensehawk, you accept Sensehawk's": "By logging in to Sense<PERSON>, you accept Sense<PERSON>'s", "By logging in to Sensehawk, you accept Sensehawk’s": "By logging in to Sense<PERSON>, you accept Sensehawk’s", "By logging in to TaskMapper, you accept Sensehawk's": "By logging in to TaskMapper, you accept Sensehawk's", "By logging in to TaskMapper, you accept Sensehawk’s": "By logging in to TaskMapper, you accept Sensehawk’s", "Calculate elevation": "Calculate elevation", "Calculate value": "Calculate value", "Calculate volume": "Calculate volume", "Calculated value": "Calculated value", "Calculating": "Calculating", "Calendar": "Calendar", "Calendar view": "Calendar view", "Calender view": "Calender view", "Calibrate": "Calibrate", "Calibration is not set. Please set the calibration": "Calibration is not set. Please set the calibration", "Camera": "Camera", "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own  transactions and create transactions to which user has access in accessible assets.": "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own  transactions and create transactions to which user has access in accessible assets.", "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access in accessible assets.": "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access in accessible assets.", "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access in asset.": "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access in asset.", "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access.": "Can access and view items-stock and their quantities from warehouses to which user has access. can view records of own transactions and create transactions to which user has access.", "Can access shared schedules": "Can access shared schedules", "Can access, view, create and modify all items, transactions, warehouses and vendors in accessible assets": "Can access, view, create and modify all items, transactions, warehouses and vendors in accessible assets", "Can access, view, create and modify all items, transactions, warehouses and vendors in accessible assets.": "Can access, view, create and modify all items, transactions, warehouses and vendors in accessible assets.", "Can access, view, create and modify all items, transactions, warehouses and vendors in asset.": "Can access, view, create and modify all items, transactions, warehouses and vendors in asset.", "Can access, view, create and modify all items, transactions, warehouses and vendors.": "Can access, view, create and modify all items, transactions, warehouses and vendors.", "Can approve the report": "Can approve the report", "Can create": "Can create", "Can create & share documents": "Can create & share documents", "Can create and modify all transactions": "Can create and modify all transactions", "Can create and modify all transactions in accessible assets": "Can create and modify all transactions in accessible assets", "Can create and modify all transactions in accessible assets.": "Can create and modify all transactions in accessible assets.", "Can create and modify all transactions in assets.": "Can create and modify all transactions in assets.", "Can create and modify all transactions.": "Can create and modify all transactions.", "Can create and share document, create transmittal and view DMS workflow template": "Can create and share document, create transmittal and view DMS workflow template", "Can create and share documents": "Can create and share documents", "Can create and share documents.": "Can create and share documents.", "Can create and view all schedules the asset.": "Can create and view all schedules the asset.", "Can create and view schedules in all accessible assets": "Can create and view schedules in all accessible assets", "Can create and view schedules in all accessible assets.": "Can create and view schedules in all accessible assets.", "Can create form instances and view responses": "Can create form instances and view responses", "Can create form instances and view responses.": "Can create form instances and view responses.", "Can create form instances and view responses. Can import shared form templates from other assets.": "Can create form instances and view responses. Can import shared form templates from other assets.", "Can create form instances and view responses. Can import shared templates from assets.": "Can create form instances and view responses. Can import shared templates from assets.", "Can create observations, view and modify assigned task": "Can create observations, view and modify assigned task", "Can create observations, view and modify assigned task.": "Can create observations, view and modify assigned task.", "Can create schedules": "Can create schedules", "Can create tasks, modify assigned forms and close assigned and owned forms": "Can create tasks, modify assigned forms and close assigned and owned forms", "Can create tasks, modify assigned tasks and close assigned and owned tasks": "Can create tasks, modify assigned tasks and close assigned and owned tasks", "Can create tasks, modify assigned tasks and close assigned and owned tasks.": "Can create tasks, modify assigned tasks and close assigned and owned tasks.", "Can create tasks, modify assigned tasks, close assigned and owned tasks": "Can create tasks, modify assigned tasks, close assigned and owned tasks", "Can create tasks, modify assigned tasks, close assigned and owned tasks.": "Can create tasks, modify assigned tasks, close assigned and owned tasks.", "Can create transmittals": "Can create transmittals", "Can create, modify, share annotations": "Can create, modify, share annotations", "Can create, modify, use task templates": "Can create, modify, use task templates", "Can create, modify, use task templates.": "Can create, modify, use task templates.", "Can create, view and modify schedules in all accessible assets.": "Can create, view and modify schedules in all accessible assets.", "Can create, view and modify schedules in the asset.": "Can create, view and modify schedules in the asset.", "Can create, view, share and modify all dashboards.": "Can create, view, share and modify all dashboards.", "Can create, view, share, and modify all dashboards.": "Can create, view, share, and modify all dashboards.", "Can create/modify form templates and instances. Can import shared templates from assets. Can view responses.": "Can create/modify form templates and instances. Can import shared templates from assets. Can view responses.", "Can create/modify form templates and instances. Can import shared templates from other assets. Can view responses.": "Can create/modify form templates and instances. Can import shared templates from other assets. Can view responses.", "Can create/view/share documents & transmittals. Custom statuses, review statuses, Issue purposes, transmittal schema. Can view, create & modify DMS workflow templates, DMS custom fields, DMS statuses": "Can create/view/share documents & transmittals. Custom statuses, review statuses, Issue purposes, transmittal schema. Can view, create & modify DMS workflow templates, DMS custom fields, DMS statuses", "Can create/view/share,modify documents & transmittals. Custom statuses, review statuses, Issue purposes, transmittal schema. Can view, create & modify DMS workflow templates, DMS custom fields, DMS statuses": "Can create/view/share,modify documents & transmittals. Custom statuses, review statuses, Issue purposes, transmittal schema. Can view, create & modify DMS workflow templates, DMS custom fields, DMS statuses", "Can edit": "Can edit", "Can edit, publish, unpublish, modify activities and manage the schedule": "Can edit, publish, unpublish, modify activities and manage the schedule", "Can invite users and modify teams & categories": "Can invite users and modify teams & categories", "Can invite users and modify teams & categories.": "Can invite users and modify teams & categories.", "Can manage": "Can manage", "Can mark plans as private": "Can mark plans as private", "Can modify all dashboards": "Can modify all dashboards", "Can modify all, create and share documents. Can share docs with guest users": "Can modify all, create and share documents. Can share docs with guest users", "Can modify all, create and share documents. Can share docs with guest users.": "Can modify all, create and share documents. Can share docs with guest users.", "Can modify documents": "Can modify documents", "Can modify schedules": "Can modify schedules", "Can modify transmittals": "Can modify transmittals", "Can modify users, teams, roles": "Can modify users, teams, roles", "Can modify users, teams, roles.": "Can modify users, teams, roles.", "Can modify users, teams, roles. Can modify billing details.": "Can modify users, teams, roles. Can modify billing details.", "Can not access unless shared with them": "Can not access unless shared with them", "Can not bulk fill more than 100 forms": "Can not bulk fill more than 100 forms", "Can not change type for the features associated with a workflow": "Can not change type for the features associated with a workflow", "Can not create more than 100 forms at once": "Can not create more than 100 forms at once", "Can not create more than 100 tasks at once": "Can not create more than 100 tasks at once", "Can not delete more than 100 forms": "Can not delete more than 100 forms", "Can not delete more than 100 forms at once": "Can not delete more than 100 forms at once", "Can not export more than 100 forms": "Can not export more than 100 forms", "Can not export more than 100 tasks": "Can not export more than 100 tasks", "Can not move the features associated with a different project": "Can not move the features associated with a different project", "Can not navigate to the location. You don't have access, or the location is no longer available": "Can not navigate to the location. You don't have access, or the location is no longer available", "Can not update more than 100 forms at once": "Can not update more than 100 forms at once", "Can not update progress for the features of different types": "Can not update progress for the features of different types", "Can only access schedules that are directly shared": "Can only access schedules that are directly shared", "Can only access schedules that are directly shared.": "Can only access schedules that are directly shared.", "can only be downloaded and cannot be opened on our platform.": "can only be downloaded and cannot be opened on our platform.", "Can only view public locations": "Can only view public locations", "Can only view public plans. Can view annotations": "Can only view public plans. Can view annotations", "Can only view shared dashboards": "Can only view shared dashboards", "Can only view shared dashboards.": "Can only view shared dashboards.", "Can only view the report": "Can only view the report", "Can review the document and approve it": "Can review the document and approve it", "Can select past dates": "Can select past dates", "Can share all dashboards": "Can share all dashboards", "Can submit": "Can submit", "Can use": "Can use", "Can view": "Can view", "Can view all dashboards": "Can view all dashboards", "Can view all dashboards.": "Can view all dashboards.", "Can view all documents, create and share documents. Can share docs with guest users": "Can view all documents, create and share documents. Can share docs with guest users", "Can view all documents, create and share documents. Can share docs with guest users.": "Can view all documents, create and share documents. Can share docs with guest users.", "Can view all form templates and create a form for any template": "Can view all form templates and create a form for any template", "Can view all locations including private locations and associate tasks & forms with them": "Can view all locations including private locations and associate tasks & forms with them", "Can view all locations including private locations and associate tasks & forms with them. Can create new locations and mark them as private": "Can view all locations including private locations and associate tasks & forms with them. Can create new locations and mark them as private", "Can view all plans including private plans": "Can view all plans including private plans", "Can view all plans including private plans. Can create, modify, share annotations": "Can view all plans including private plans. Can create, modify, share annotations", "Can view all plans including private plans. Can mark plans as private. Can create, modify, share annotations": "Can view all plans including private plans. Can mark plans as private. Can create, modify, share annotations", "Can view all public locations and assciate tasks & forms with them": "Can view all public locations and assciate tasks & forms with them", "Can view all schedules in the asset.": "Can view all schedules in the asset.", "Can view all transactions": "Can view all transactions", "Can view and download this file": "Can view and download this file", "Can view and edit": "Can view and edit", "Can view and share all dashboards": "Can view and share all dashboards", "Can view and share all dashboards.": "Can view and share all dashboards.", "Can view and submit assigned forms": "Can view and submit assigned forms", "Can view and submit assigned forms.": "Can view and submit assigned forms.", "Can view DMS workflow templates": "Can view DMS workflow templates", "Can view documents & transmittals": "Can view documents & transmittals", "Can view items-stock, view only created transactions": "Can view items-stock, view only created transactions", "Can view only shared files/folders": "Can view only shared files/folders", "Can view only shared files/folders.": "Can view only shared files/folders.", "Can view public plans and annotations": "Can view public plans and annotations", "Can view public plans. Can create, modify, share annotations": "Can view public plans. Can create, modify, share annotations", "Can view records of all transactions and create transactions to which user has access in accessible assets": "Can view records of all transactions and create transactions to which user has access in accessible assets", "Can view records of all transactions and create transactions to which user has access in accessible assets.": "Can view records of all transactions and create transactions to which user has access in accessible assets.", "Can view records of all transactions and create transactions to which user has access in asset.": "Can view records of all transactions and create transactions to which user has access in asset.", "Can view records of all transactions and create transactions to which user has access.": "Can view records of all transactions and create transactions to which user has access.", "Can view schedules in all accessible assets": "Can view schedules in all accessible assets", "Can view schedules in all accessible assets.": "Can view schedules in all accessible assets.", "Can view shared dashboards": "Can view shared dashboards", "Can view terra containers and their data like features, reports, etc": "Can view terra containers and their data like features, reports, etc", "Can view the items of the folder and subfolders": "Can view the items of the folder and subfolders", "Can view users & teams": "Can view users & teams", "Can view users and teams": "Can view users and teams", "Can view users and teams.": "Can view users and teams.", "Can view, create and modify form templates.": "Can view, create and modify form templates.", "Can view, create and modify form templates. Can import templates from other assets": "Can view, create and modify form templates. Can import templates from other assets", "Can view, create and modify form templates. Can import templates from other assets.": "Can view, create and modify form templates. Can import templates from other assets.", "Can view, create and modify task templates": "Can view, create and modify task templates", "Can view, create and modify task templates.": "Can view, create and modify task templates.", "Can View, create, modify terra containers . can create, modify, delete terra feature types and features": "Can View, create, modify terra containers . can create, modify, delete terra feature types and features", "Can view, create, modify terra containers, features and feature types. Can create, modify, delete item workflows": "Can view, create, modify terra containers, features and feature types. Can create, modify, delete item workflows", "Can view, download, update versions, share with other members": "Can view, download, update versions, share with other members", "Can view, edit, and manage": "Can view, edit, and manage", "Can view, modify, close all forms. Can use task templates. Can import task templates shared by other assets.": "Can view, modify, close all forms. Can use task templates. Can import task templates shared by other assets.", "Can view, modify, close all tasks. Can create, modify, use task templates. Can import task templates shared by other assets.": "Can view, modify, close all tasks. Can create, modify, use task templates. Can import task templates shared by other assets.", "Can view, modify, close all tasks. Can use task templates. Can import task templates shared by other assets": "Can view, modify, close all tasks. Can use task templates. Can import task templates shared by other assets", "Can view, modify, close all tasks. Can use task templates. Can import task templates shared by other assets.": "Can view, modify, close all tasks. Can use task templates. Can import task templates shared by other assets.", "Can work on assigned and owned forms. Can create observations": "Can work on assigned and owned forms. Can create observations", "Can work on assigned and owned tasks": "Can work on assigned and owned tasks", "Can work on assigned and owned tasks. Can create observations": "Can work on assigned and owned tasks. Can create observations", "cancel": "cancel", "Cancel": "Cancel", "Cancel document": "Cancel document", "Cancel document request": "Cancel document request", "Cancel documents": "Cancel documents", "Cancel now": "Cancel now", "Cancel Request": "Cancel Request", "Cancel Subscription": "Cancel Subscription", "Cancel transmittal": "Cancel transmittal", "Cancel your subscription": "Cancel your subscription", "Cancelled": "Cancelled", "cancelled the document": "cancelled the document", "cancelled the document  with a message": "cancelled the document  with a message", "cancelled the document on": "cancelled the document on", "cancelled the file in the transmittal": "cancelled the file in the transmittal", "cancelled the sub-workflow": "cancelled the sub-workflow", "cancelled the transmittal": "cancelled the transmittal", "cancelled the transmittal on": "cancelled the transmittal on", "Cannot change the type for features as selected class in not associated with the feature": "Cannot change the type for features as selected class in not associated with the feature", "Cannot change to": "Cannot change to", "Cannot change type for features associated with a percentage workflow": "Cannot change type for features associated with a percentage workflow", "Cannot change type for features to previous stage of workflow.": "Cannot change type for features to previous stage of workflow.", "Cannot delete parent node": "Cannot delete parent node", "Cannot delete the only field. Please add another field to continue.": "Cannot delete the only field. Please add another field to continue.", "Cannot delete the only section. Please add another section to continue.": "Cannot delete the only section. Please add another section to continue.", "Cannot edit previously published fields": "Cannot edit previously published fields", "cannot have duplicates": "cannot have duplicates", "cannot have its ancestor": "cannot have its ancestor", "Cannot restore document": "Cannot restore document", "Cannot update type for the feature in this workflow": "Cannot update type for the feature in this workflow", "Capacity": "Capacity", "Card Number": "Card Number", "Catch Up Rate": "Catch Up Rate", "Categories": "Categories", "Categories & Tags": "Categories & Tags", "Categorize by property": "Categorize by property", "category": "category", "Category": "Category", "Category detail": "Category detail", "Category is already exist.": "Category is already exist.", "Category Name": "Category Name", "Category of the document": "Category of the document", "Category updated successfully": "Category updated successfully", "Category updated suvvessfully": "Category updated suvvessfully", "cc": "cc", "Cc": "Cc", "CC": "CC", "Cc/Information": "Cc/Information", "Change": "Change", "Change class color & pattern symbology": "Change class color & pattern symbology", "Change email settings": "Change email settings", "Change feature color & pattern": "Change feature color & pattern", "Change feature type": "Change feature type", "Change Language": "Change Language", "Change Organization": "Change Organization", "Change Owner": "Change Owner", "Change ownership": "Change ownership", "Change Password": "Change Password", "Change status": "Change status", "changed access for": "changed access for", "changed access for the members": "changed access for the members", "changed category": "changed category", "changed description": "changed description", "changed due date": "changed due date", "changed owner": "changed owner", "changed owner to": "changed owner to", "changed progress": "changed progress", "changed start date": "changed start date", "changed the access for the members": "changed the access for the members", "changed the location": "changed the location", "changed the privacy": "changed the privacy", "changed the version": "changed the version", "changed the version to": "changed the version to", "Changes detected": "Changes detected", "changes to": "changes to", "Changes to bom saved successfully": "Changes to bom saved successfully", "Changes to the item saved successfully.": "Changes to the item saved successfully.", "Changes to the warehouse saved successfully.": "Changes to the warehouse saved successfully.", "Character limit maxed out": "Character limit maxed out", "characters": "characters", "Chart": "Chart", "Chart display mode": "Chart display mode", "Chart type": "Chart type", "Charts": "Charts", "Chat with us": "Chat with us", "Check all items": "Check all items", "Check markers": "Check markers", "Check your downloads folder": "Check your downloads folder", "Check your email": "Check your email", "Checkbox": "Checkbox", "Checkboxes": "Checkboxes", "Checklist": "Checklist", "Checklist % progress": "Checklist % progress", "Checklist name": "Checklist name", "Checklist Name": "Checklist Name", "Checklist Percent": "Checklist Percent", "Checklist should have atleast one item": "Checklist should have atleast one item", "Checklists": "Checklists", "Chooose Pattern": "<PERSON><PERSON><PERSON>", "Choose": "<PERSON><PERSON>", "Choose a basic/checklist/table template below to add fields from the template to the section.": "Choose a basic/checklist/table template below to add fields from the template to the section.", "Choose a date by which you expect the document submission": "Choose a date by which you expect the document submission", "Choose a date only in case if it's different from the data date": "Choose a date only in case if it's different from the data date", "Choose a field whose values needs to be searched": "Choose a field whose values needs to be searched", "Choose a form": "Choose a form", "Choose a form below to fill the template with form data and click on the Generate button.": "Choose a form below to fill the template with form data and click on the Generate button.", "Choose a form below to test the entered formula.": "Choose a form below to test the entered formula.", "Choose a form block to rollback the form execution on reaching this block. If not selected, it will automatically go to the previous form block.": "Choose a form block to rollback the form execution on reaching this block. If not selected, it will automatically go to the previous form block.", "Choose a map": "Choose a map", "Choose a member to review selected document on your behalf": "Choose a member to review selected document on your behalf", "Choose a member to submit selected document on your behalf": "Choose a member to submit selected document on your behalf", "Choose a property to label the features": "Choose a property to label the features", "Choose a site": "Choose a site", "Choose a template": "Choose a template", "Choose a template to continue to build template": "Choose a template to continue to build template", "Choose a template to copy sections/fields": "Choose a template to copy sections/fields", "Choose a workflow template to import the data": "Choose a workflow template to import the data", "Choose an account": "Choose an account", "Choose an action for the affected forms": "Choose an action for the affected forms", "Choose an attachment fields that should be synced": "Choose an attachment fields that should be synced", "Choose an existing member or type to add a new custom resource": "Choose an existing member or type to add a new custom resource", "Choose an Icon": "Choose an Icon", "Choose assets": "Choose assets", "Choose assets to grant access to the team members.": "Choose assets to grant access to the team members.", "Choose assets/templates": "Choose assets/templates", "Choose assignees for the form": "Choose assignees for the form", "Choose category": "Choose category", "Choose color": "Choose color", "Choose Color": "Choose Color", "Choose columns": "Choose columns", "Choose columns to display": "Choose columns to display", "Choose documents to request": "Choose documents to request", "Choose drawing": "<PERSON>ose drawing", "Choose existing": "Choose existing", "Choose existing file": "Choose existing file", "Choose existing form": "Choose existing form", "Choose existing settings of previous project": "Choose existing settings of previous project", "Choose existing task": "Choose existing task", "Choose fields": "Choose fields", "Choose fields to aggregate": "Choose fields to aggregate", "Choose fields to attach": "Choose fields to attach", "Choose fields to filter": "Choose fields to filter", "Choose file": "Choose file", "Choose from DMS": "<PERSON>ose from DMS", "Choose from existing files.": "Choose from existing files.", "Choose Icon": "<PERSON><PERSON>", "Choose input type": "Choose input type", "Choose members": "Choose members", "Choose members and teams to assign/inherit role permissions": "Choose members and teams to assign/inherit role permissions", "Choose members to add them to the team.": "Choose members to add them to the team.", "Choose members to assign the team": "Choose members to assign the team", "Choose members whom you want request or review and approve the workflow.": "Choose members whom you want request or review and approve the workflow.", "Choose members(s) to review selected document": "Choose members(s) to review selected document", "Choose module": "Choose module", "Choose number format": "Choose number format", "Choose one/more attachment fields to be synced": "Choose one/more attachment fields to be synced", "Choose one/more construction activities": "Choose one/more construction activities", "Choose one/more layers": "Choose one/more layers", "Choose Plan": "Choose <PERSON>", "Choose project": "Choose project", "Choose project template": "Choose project template", "Choose quantities": "Choose quantities", "Choose roles to assign to the user. The permissions will combined if multiple roles are selected.": "Choose roles to assign to the user. The permissions will combined if multiple roles are selected.", "Choose roles to assign/inherit role permissions for the team members": "Choose roles to assign/inherit role permissions for the team members", "Choose status": "Choose status", "Choose step": "Choose step", "Choose Task": "Choose <PERSON>", "Choose teams to assign to the user. The user will also inherit permissions from the team's roles if they are added to the team.": "Choose teams to assign to the user. The user will also inherit permissions from the team's roles if they are added to the team.", "Choose template": "Choose template", "Choose Template": "<PERSON><PERSON>", "Choose template or Create new": "Choose template or Create new", "Choose the assets to add the invited users": "Choose the assets to add the invited users", "Choose the date when the progress is reported": "Choose the date when the progress is reported", "Choose the date/range for which the values are recorded": "Choose the date/range for which the values are recorded", "Choose the files to upload to the activity": "Choose the files to upload to the activity", "Choose the folder to which the attachments to be uploaded": "Choose the folder to which the attachments to be uploaded", "Choose the horizontal/vertical layout between the components": "Choose the horizontal/vertical layout between the components", "Choose the indexing strategy to use. This is how the components are numbered": "Choose the indexing strategy to use. This is how the components are numbered", "Choose the initial document status": "Choose the initial document status", "Choose the locations if you want to filter the stock/transactions from particular locations.": "Choose the locations if you want to filter the stock/transactions from particular locations.", "Choose the members whom you want to request documents from i.e reviewers": "Choose the members whom you want to request documents from i.e reviewers", "Choose the number of days before the schedule to create the": "Choose the number of days before the schedule to create the", "Choose the roles to add the invited users": "Choose the roles to add the invited users", "Choose the teams to add the invited users": "Choose the teams to add the invited users", "Choose the time in your local timezone to trigger the sync": "Choose the time in your local timezone to trigger the sync", "Choose the upstream/downstream connections": "Choose the upstream/downstream connections", "Choose to upgrade plan": "Choose to upgrade plan", "Choose type": "Choose type", "Choose upto 20 columns to display in the table": "Choose upto 20 columns to display in the table", "Choose upto 4 fields to group assets": "Choose upto 4 fields to group assets", "Choose upto 5 options": "Choose upto 5 options", "Choose users or teams to send notifications on publishing a transaction": "Choose users or teams to send notifications on publishing a transaction", "Choose which members are allowed to be selected in this field. You can select one or more options to restrict the eligible members. If no options are selected, all members will be visible by default.": "Choose which members are allowed to be selected in this field. You can select one or more options to restrict the eligible members. If no options are selected, all members will be visible by default.", "choose-devices-for-shortcut-availability": "Choose the devices on which the shortcut should be available.", "Circle": "Circle", "City": "City", "Civil documents": "Civil documents", "Class": "Class", "Class is not available for the feature in this workflow": "Class is not available for the feature in this workflow", "Class Symbology": "Class Symbology", "Classes": "Classes", "Classes breakdown": "Classes breakdown", "Classify by property": "Classify by property", "Clear": "Clear", "Clear all": "Clear all", "Clear column": "Clear column", "Clear duplicates": "Clear duplicates", "Clear duplicates in Scan": "Clear duplicates in Scan", "Clear duplicates in Scan and serial number correction": "Clear duplicates in Scan and serial number correction", "Clear filters": "Clear filters", "Clear Filters": "Clear Filters", "Clear Format": "Clear Format", "Clear mapping": "Clear mapping", "Clear matching": "Clear matching", "Clear progress data": "Clear progress data", "Clear Progress data": "Clear Progress data", "Clear values": "Clear values", "Click Alt/Option+Enter to exit this mode.": "Click Alt/Option+Enter to exit this mode.", "Click on the button below to verify the domain": "Click on the button below to verify the domain", "Click to import": "Click to import", "Click to navigate to the error cell": "Click to navigate to the error cell", "Click to upload": "Click to upload", "Clone": "<PERSON><PERSON>", "Clone settings": "Clone settings", "Close": "Close", "Close after": "Close after", "Close Transmittal": "Close Transmittal", "Close/Submit the forms": "Close/Submit the forms", "Closed": "Closed", "closed the transmittal": "closed the transmittal", "Cloud": "Cloud", "CMS": "CMS", "Code": "Code", "Collaborate with your team using #comments": "Collaborate with your team using #comments", "Collapse": "Collapse", "Collapse all": "Collapse all", "Collapse branch": "Collapse branch", "Color": "Color", "Color by property": "Color by property", "Color range": "Color range", "Column": "Column", "Column mapper": "Column mapper", "Column name": "Column name", "Column settings": "Column settings", "Column Summary": "Column Summary", "Columns": "Columns", "comment": "Comment", "Comment": "Comment", "Comment here": "Comment here", "commented": "commented", "Comments": "Comments", "Comments in": "Comments in", "Comments only": "Comments only", "Comments out": "Comments out", "Comments related to selected annotation": "Comments related to selected annotation", "Commissioning and Certification": "Commissioning and Certification", "Company": "Company", "Compare": "Compare", "Compare current vs previous schedule progress": "Compare current vs previous schedule progress", "Compare to": "Compare to", "Compare with previous range": "Compare with previous range", "complete": "complete", "Complete": "Complete", "Complete Transmittal": "Complete Transmittal", "completed": "completed", "Completed": "Completed", "completed a sub-workflow for the document ": "completed a sub-workflow for the document ", "Completed approvals": "Completed approvals", "completed the document": "completed the document", "completed the file in the transmittal": "completed the file in the transmittal", "Completes Submission": "Completes Submission", "Completion status": "Completion status", "Component": "Component", "Component Master": "Component Master", "Component name": "Component name", "Components": "Components", "Compose email": "Compose email", "Condition": "Condition", "Condition Filter": "Condition Filter", "Condition settings": "Condition settings", "Conditional Block": "Conditional Block", "Conditional formatting": "Conditional formatting", "Conditional Formatting": "Conditional Formatting", "Conditional settings": "Conditional settings", "Conditions": "Conditions", "Config": "Config", "Config module": "Config module", "Configuration": "Configuration", "Configuration has been created successfully": "Configuration has been created successfully", "Configuration has been updated successfully": "Configuration has been updated successfully", "Configure": "Configure", "Configure Activity Reporting": "Configure Activity Reporting", "Configure and manage roles to set specific permission levels for users and teams. Tailor access rights to ensure appropriate resource access and enhance operational security.": "Configure and manage roles to set specific permission levels for users and teams. Tailor access rights to ensure appropriate resource access and enhance operational security.", "Configure auto sync": "Configure auto sync", "Configure auto update": "Configure auto update", "Configure automatic number generation": "Configure automatic number generation", "Configure automation": "Configure automation", "Configure buttons": "Configure buttons", "Configure columns": "Configure columns", "Configure Display": "Configure Di<PERSON><PERSON>", "Configure filters": "Configure filters", "Configure granular permissions and assign to members/teams": "Configure granular permissions and assign to members/teams", "Configure granular permissions for respective modules for the asset.": "Configure granular permissions for respective modules for the asset.", "Configure granular permissions for respective modules. Users/teams will inherit these permissions for the assets they are granted access.": "Configure granular permissions for respective modules. Users/teams will inherit these permissions for the assets they are granted access.", "Configure label": "Configure label", "Configure one/more destinations and control the sync to either sync all documents or certain documents that match a specific criteria. Allow other members to use the integration on your behalf and delegate the integration capabilities without sharing the Sharepoint credentials or additional licenses.": " Configure one/more destinations and control the sync to either sync all documents or certain documents that match a specific criteria. Allow other members to use the integration on your behalf and delegate the integration capabilities without sharing the Sharepoint credentials or additional licenses.", "Configure options for the report and click Preview": "Configure options for the report and click Preview", "Configure reminders to send email/push notifications for the assignees to submit the form.": "Configure reminders to send email/push notifications for the assignees to submit the form.", "Configure reminders to send email/push notifications for the pending members to take an action.": "Configure reminders to send email/push notifications for the pending members to take an action.", "Configure reminders to send periodic emails push to pending approvers to take an action": "Configure reminders to send periodic emails push to pending approvers to take an action", "Configure reminders to send periodic emails to pending with reviewers to take an action": "Configure reminders to send periodic emails to pending with reviewers to take an action", "Configure tabs": "Configure tabs", "Configure Tabs": "Configure <PERSON><PERSON>", "Configure tags to label resources like tasks, forms, documents, etc to provide additional context and further segregate them": "Configure tags to label resources like tasks, forms, documents, etc to provide additional context and further segregate them", "Configure transaction types, statuses, vendors, item types and other settings": "Configure transaction types, statuses, vendors, item types and other settings", "Configure Update Feature": "Configure Update Feature", "Confirm": "Confirm", "Confirm Enable workflow": "Confirm Enable workflow", "Confirm location": "Confirm location", "Confirm new password": "Confirm new password", "Confirm version numbering based on issue purpose": "Confirm version numbering based on issue purpose", "Confirm your request": "Confirm your request", "Confirm/Send": "Confirm/Send", "Confirming this will enable the workflow processes in the terra module.": "Confirming this will enable the workflow processes in the terra module.", "Confirming will lead to deactivation of your subscription permanently. Are you sure you want to cancel your subscription?": "Confirming will lead to deactivation of your subscription permanently. Are you sure you want to cancel your subscription?", "Connect one/more accounts.": "Connect one/more accounts.", "Connected by": "Connected by", "Connection types": "Connection types", "Connections": "Connections", "Consolidator": "Consolidator", "Consolidator's feedback": "Consolidator's feedback", "Constraint": "Constraint", "Constraint date": "Constraint date", "Constraint type": "Constraint type", "Construction progress report": "Construction progress report", "Contact Sales to Enable Dashboard": "Contact Sales to Enable Dashboard", "Contact us": "Contact us", "contains": "contains", "contains all of": "contains all of", "contains any of": "contains any of", "Contextual numbering": "Contextual numbering", "Continue": "Continue", "Continue with the old workflow": "Continue with the old workflow", "Contractual documents": "Contractual documents", "conversations": "Conversations", "Conversations": "Conversations", "Convert to admin": "Convert to admin", "Convert to free": "Convert to free", "Convert to global admin": "Convert to global admin", "Convert to guest": "Convert to guest", "Convert to member": "Convert to member", "Convert to milestone": "Convert to milestone", "Convert to paid": "Convert to paid", "Convert to Receipt": "Convert to Receipt", "Convert to subtask": "Convert to subtask", "Convert to Subtask": "Convert to Subtask", "Convert to task": "Convert to task", "Converted to Receipt": "Converted to Receipt", "Cookie Policy": "<PERSON><PERSON>", "Coordinate tasks effortlessly with well-defined dependencies such as blocking, waiting and link relevant tasks": "Coordinate tasks effortlessly with well-defined dependencies such as blocking, waiting and link relevant tasks", "Coordinates": "Coordinates", "Copied": "<PERSON>pied", "Copied!": "Copied!", "Copy": "Copy", "Copy a template from an asset into one/more assets. This permission will automatically provide create_form_templates permission in the current scope": "Copy a template from an asset into one/more assets. This permission will automatically provide create_form_templates permission in the current scope", "Copy a template from an asset into one/more assets. This permission will automatically provide create_form_templates permission in the current scope.": "Copy a template from an asset into one/more assets. This permission will automatically provide create_form_templates permission in the current scope.", "Copy all": "Copy all", "Copy field": "Copy field", "Copy Link": "Copy Link", "Copy section": "Copy section", "Copy unaccounted stock": "Copy unaccounted stock", "Copy URL": "Copy URL", "Core": "Core", "cost": "cost", "Cost": "Cost", "Cost per item": "Cost per item", "Cost Performace Index (CPI)": "Cost Performace Index (CPI)", "Cost Performance Index (CPI)": "Cost Performance Index (CPI)", "Cost tracking": "Cost tracking", "Cost type": "Cost type", "Cost variance": "Cost variance", "Cost Variance": "Cost <PERSON><PERSON>", "Cost(per unit)": "Cost(per unit)", "Cost/Work history": "Cost/Work history", "could not be archived, please check submission status and block type": "could not be archived, please check submission status and block type", "count": "count", "Count": "Count", "count is between": "count is between", "Country": "Country", "Cover image": "Cover image", "Cover Image": "Cover Image", "CPI": "CPI", "Craft your workflow outcome strategy to determine the document status": "Craft your workflow outcome strategy to determine the document status", "Create": "Create", "Create & modify DMS workflow templates, custom fields, statuses, issue purposes, schema": "Create & modify DMS workflow templates, custom fields, statuses, issue purposes, schema", "Create & modify DMS workflow templates, DMS custom fields, DMS statuses": "Create & modify DMS workflow templates, DMS custom fields, DMS statuses", "Create & modify terra pivot": "Create & modify terra pivot", "Create & Update asset fields": "Create & Update asset fields", "Create a blank schedule and start from scratch": "Create a blank schedule and start from scratch", "Create a from for any template": "Create a from for any template", "Create a from for any template.": "Create a from for any template.", "Create a new block here": "Create a new block here", "Create a new component here": "Create a new component here", "Create a new form": "Create a new form", "Create a new form step or add an existing one": "Create a new form step or add an existing one", "Create a new form template": "Create a new form template", "Create a new item": "Create a new item", "Create a new plan here": "Create a new plan here", "Create a new schedule from scratch all together": "Create a new schedule from scratch all together", "Create a new schedule or import an existing schedule from Primavera P6 or Microsoft projects": "Create a new schedule or import an existing schedule from Primavera P6 or Microsoft projects", "Create a new simulation from scratch all together": "Create a new simulation from scratch all together", "Create a new task here": "Create a new task here", "Create a new template or choose an existing one to overwrite it. These templates can be used across the organization in any form templates.": "Create a new template or choose an existing one to overwrite it. These templates can be used across the organization in any form templates.", "Create a new transmittal to request documents, reviews, or both from suppliers, clients, and internal team members.": "Create a new transmittal to request documents, reviews, or both from suppliers, clients, and internal team members.", "Create a new workflow": "Create a new workflow", "Create a placeholder document to request a submission by transmittal or directly upload the document when it is available.": "Create a placeholder document to request a submission by transmittal or directly upload the document when it is available.", "Create a record of all the incoming shipments": "Create a record of all the incoming shipments", "Create a report": "Create a report", "Create a workflow for this form?": "Create a workflow for this form?", "Create Account": "Create Account", "Create and assign forms using the template": "Create and assign forms using the template", "Create and manage roles with granular permission configuration as per your need.": "Create and manage roles with granular permission configuration as per your need.", "Create and modify all transactions(at asset level)": "Create and modify all transactions(at asset level)", "Create and modify item details": "Create and modify item details", "Create and modify task templates": "Create and modify task templates", "Create and modify views": "Create and modify views", "Create and modify warehouse details": "Create and modify warehouse details", "Create approval workflows to update document status based on the chosen outcome strategy": "Create approval workflows to update document status based on the chosen outcome strategy", "Create category": "Create category", "Create conditions and rules to trigger actions in your workflow.": "Create conditions and rules to trigger actions in your workflow.", "Create connections between activities to indicate the order in which they should be completed.": "Create connections between activities to indicate the order in which they should be completed.", "Create custom issue purposes to explain the intent of sending documents. Use these Issue purposes to create document version numbers. Each version number will include the issue purpose code and a unique number in sequence.": "Create custom issue purposes to explain the intent of sending documents. Use these Issue purposes to create document version numbers. Each version number will include the issue purpose code and a unique number in sequence.", "Create dashboard": "Create dashboard", "Create Dashboard": "Create Dashboard", "Create document": "Create document", "Create drafts or publish transactions for the types that are explicitly granted": "Create drafts or publish transactions for the types that are explicitly granted", "Create field": "Create field", "Create form": "Create form", "Create forms": "Create forms", "Create group": "Create group", "Create Group": "Create Group", "Create hierarchies of subtasks to plan tasks with multiple levels of depth and keep everything organized": "Create hierarchies of subtasks to plan tasks with multiple levels of depth and keep everything organized", "Create new": "Create new", "Create New C&C Report": "Create New C&C Report", "Create New File here": "Create New File here", "Create new form templates": "Create new form templates", "Create new forms.": "Create new forms.", "Create New Map here": "Create New Map here", "Create new organization": "Create new organization", "Create New Schedule here": "Create New Schedule here", "Create new schedules": "Create new schedules", "Create new transmittals": "Create new transmittals", "Create or select roles to add this member": "Create or select roles to add this member", "Create or select teams to add this member": "Create or select teams to add this member", "Create placeholder document": "Create placeholder document", "Create Tag": "Create Tag", "Create task": "Create task", "Create tasks": "Create tasks", "Create tasks, modify assigned tasks, close assigned and owned tasks": "Create tasks, modify assigned tasks, close assigned and owned tasks", "Create template": "Create template", "Create transactions of all types": "Create transactions of all types", "Create Transmittal": "Create Transmittal", "Create view": "Create view", "Create, update and delete views": "Create, update and delete views", "Create, view, modify all form templates. Can copy template from an asset into one/more assets": "Create, view, modify all form templates. Can copy template from an asset into one/more assets", "Create, view, modify all form templates. Can copy template from an asset into one/more assets. Can create a form for any template. Can view all forms/responses and historical submissions. Can submit all forms": "Create, view, modify all form templates. Can copy template from an asset into one/more assets. Can create a form for any template. Can view all forms/responses and historical submissions. Can submit all forms", "Create, view, modify all form templates. Can copy template from an asset into one/more assets. Can create a form for any template. Can view all forms/responses and historical submissions. Can submit and modify all forms": "Create, view, modify all form templates. Can copy template from an asset into one/more assets. Can create a form for any template. Can view all forms/responses and historical submissions. Can submit and modify all forms", "Created": "Created", "created a new file": "created a new file", "created a new transmittal": "created a new transmittal", "Created at": "Created at", "Created At": "Created At", "Created by": "Created by", "Created By": "Created By", "Created by me": "Created by me", "Created date": "Created date", "created on": "created on", "Created on": "Created on", "Created On": "Created On", "Created successfully": "Created successfully", "created the document": "created the document", "created the file": "created the file", "created the folder": "created the folder", "created the form": "created the form", "created the template": "created the template", "Creating block": "Creating block", "Creation date": "Creation date", "Creator": "Creator", "Creator of the form": "Creator of the form", "critical": "critical", "Critical": "Critical", "Critical path": "Critical path", "CRM": "CRM", "CTA button title": "CTA button title", "Ctrl+Enter to send": "Ctrl+Enter to send", "Cummulative line": "Cummulative line", "Cumulative Actual": "Cumulative Actual", "Cumulative Planned": "Cumulative Planned", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Current": "Current", "Current access": "Current access", "Current password": "Current password", "Current Plan": "Current Plan", "Current Qty": "Current Qty", "Current Signature": "Current Signature", "Current step": "Current step", "Current version": "Current version", "Current Version": "Current Version", "Custom": "Custom", "Custom blocks": "Custom blocks", "Custom Color": "Custom Color", "Custom date": "Custom date", "Custom Email": "Custom Email", "Custom field": "Custom field", "Custom Field": "Custom Field", "Custom field changes to": "Custom field changes to", "Custom field created": "Custom field created", "Custom field creation failed. Please try again.": "Custom field creation failed. Please try again.", "Custom field deleted successfully": "Custom field deleted successfully", "Custom field deletion failed. Please try again.": "Custom field deletion failed. Please try again.", "Custom field with this name already exists": "Custom field with this name already exists", "Custom Fields": "Custom Fields", "Custom Filter": "Custom Filter", "Custom Permissions": "Custom Permissions", "Custom range": "Custom range", "Custom resource": "Custom resource", "Custom resources": "Custom resources", "Custom roles": "Custom roles", "Custom Status": "Custom Status", "Custom table": "Custom table", "Custom Types": "Custom Types", "Custom view has been saved successfully.": "Custom view has been saved successfully.", "Custom weights": "Custom weights", "Customize approval buttons to align with your specific workflow needs.": "Customize approval buttons to align with your specific workflow needs.", "Customize Approval Email": "Customize Approval Email", "Customize columns": "Customize columns", "Customize document statuses to match your organization's preferences and apply them to your documents. These statuses can also be used for mapping review statuses from transmittals.": "Customize document statuses to match your organization's preferences and apply them to your documents. These statuses can also be used for mapping review statuses from transmittals.", "Customize email": "Customize email", "Customize Email Notification": "Customize Email Notification", "Customize Escalation Email": "Customize Escalation Email", "Customize fields": "Customize fields", "Customize report": "Customize report", "Customize the reason": "Customize the reason", "Customize your permissions based on roles, transactions and to fit all your business requirements": "Customize your permissions based on roles, transactions and to fit all your business requirements", "Cut": "Cut", "CV": "CV", "Cyan": "<PERSON><PERSON>", "Daily": "Daily", "Daily log": "Daily log", "Daily progress": "Daily progress", "Daily summaries include overview of upcoming and overdue tasks and forms": "Daily summaries include overview of upcoming and overdue tasks and forms", "Daily summary emails": "Daily summary emails", "Dashboard": "Dashboard", "Dashboard duplicated successfully": "Dashboard duplicated successfully", "Dashboard Saved": "Dashboard Saved", "Dashboards": "Dashboards", "Dashed": "Dashed", "Data": "Data", "Data collection dates": "Data collection dates", "Data collection details": "Data collection details", "Data date": "Data date", "Data date line": "Data date line", "Data pasted successfully": "Data pasted successfully", "Data Preview": "Data Preview", "Data source": "Data source", "Data Source": "Data Source", "date": "date", "Date": "Date", "Date & Time": "Date & Time", "Date and time": "Date and time", "Date format → DD MMM YYYY": "Date format → DD MMM YYYY", "Date range": "Date range", "Date Range": "Date Range", "Date Time": "Date Time", "dates": "dates", "Dates": "Dates", "Datetime": "Datetime", "DateTime": "DateTime", "day": "day", "Day": "Day", "day early": "day early", "day late": "day late", "day(s)": "day(s)", "days": "days", "Days": "Days", "days before": "days before", "Days before": "Days before", "days early": "days early", "days late": "days late", "days left to finish": "days left to finish", "days overdue": "days overdue", "Days overdue": "Days overdue", "days remaining": "days remaining", "DC capacity": "DC capacity", "DC layout": "DC layout", "Deactivate": "Deactivate", "Deactivate integration": "Deactivate integration", "Deactivated": "Deactivated", "Deactivated items can not be used in any transactions. Are you sure you want to deactivate the item?": "Deactivated items can not be used in any transactions. Are you sure you want to deactivate the item?", "Deadline": "Deadline", "Deadline line": "Deadline line", "Dec": "Dec", "December": "December", "default": "default", "Default": "<PERSON><PERSON><PERSON>", "Default review statuses": "Default review statuses", "Default roles": "Default roles", "Default timerange": "<PERSON><PERSON><PERSON> <PERSON>range", "Default: false": "Default: false", "Defect details": "Defect details", "Defect filters": "Defect filters", "Defect priority": "Defect priority", "Defect status": "Defect status", "Defect type": "Defect type", "Defect Type": "Defect Type", "defects": "defects", "Defects": "Defects", "Defects By Inverter": "Defects By Inverter", "Defects by Inverters": "Defects by Inverters", "Defects by Priority": "Defects by Priority", "Defects by severity": "Defects by severity", "Defects By Severity": "Defects By Severity", "Defects by status": "Defects by status", "Defects classification": "Defects classification", "Defects count": "Defects count", "Defects count by assignee": "Defects count by assignee", "Defects count by defect type": "Defects count by defect type", "Defects List": "Defects List", "Defects resolution": "Defects resolution", "Defects selected": "Defects selected", "Defects summary": "Defects summary", "Defects updated successfully": "Defects updated successfully", "Deficient stock": "Deficient stock", "Define actions, sequences of tasks and set up approvals, automating diverse processes in your organisation.": "Define actions, sequences of tasks and set up approvals, automating diverse processes in your organisation.", "Define conditions to determine whether to display or hide this field.": "Define conditions to determine whether to display or hide this field.", "Define custom weights for activities in respective hierarchies for more granular control over the progress updates and roll-ups.": "Define custom weights for activities in respective hierarchies for more granular control over the progress updates and roll-ups.", "Define properties to record additional information for all assets which can be used for filtering, grouping and reporting": "Define properties to record additional information for all assets which can be used for filtering, grouping and reporting", "Define properties/metadata to track along with the component while viewing/exporting the form.": "Define properties/metadata to track along with the component while viewing/exporting the form.", "Define schema for the version name": "Define schema for the version name", "Define the name of the output file": "Define the name of the output file", "Define your business processes and workflows and automate data collection, decision-making, establish sequences, approvals, and more.": "Define your business processes and workflows and automate data collection, decision-making, establish sequences, approvals, and more.", "delayed": "delayed", "Delayed": "Delayed", "Delayed by": "Delayed by", "Delegate": "Delegate", "Delegate added successfully": "Delegate added successfully", "Delegate has not been added": "Delegate has not been added", "Delegate to": "Delegate to", "Delegate's feedback": "Delegate's feedback", "delegated the document for": "delegated the document for", "delegated the document to you for review on": "delegated the document to you for review on", "delegated the document to you on": "delegated the document to you on", "delete": "Delete", "Delete": "Delete", "Delete account": "Delete account", "Delete activity": "Delete activity", "Delete all": "Delete all", "Delete all instances": "Delete all instances", "Delete Asset": "Delete Asset", "Delete Attachment": "Delete Attachment", "Delete Block": "Delete Block", "Delete Category": "Delete Category", "Delete checklist": "Delete checklist", "Delete Checklist": "Delete Checklist", "Delete column": "Delete column", "Delete component": "Delete component", "Delete Component": "Delete Component", "Delete connection": "Delete connection", "Delete custom field": "Delete custom field", "Delete Dashboard": "Delete Dashboard", "Delete document status": "Delete document status", "Delete domain": "Delete domain", "Delete Drawing": "Delete Drawing", "Delete entry": "Delete entry", "Delete feature": "Delete feature", "Delete features": "Delete features", "Delete field": "Delete field", "Delete Field": "Delete Field", "Delete File": "Delete File", "Delete Folder": "Delete Folder", "Delete Form": "Delete Form", "Delete Form Instance": "Delete Form Instance", "Delete Form Template": "Delete Form Template", "Delete forms": "Delete forms", "Delete Forms": "Delete Forms", "Delete Group": "Delete Group", "Delete instance": "Delete instance", "Delete Instances": "Delete Instances", "Delete issue purpose": "Delete issue purpose", "Delete item": "Delete item", "Delete Item": "Delete Item", "Delete item type": "Delete item type", "Delete item(s)": "Delete item(s)", "Delete Items": "Delete Items", "Delete link": "Delete link", "Delete node": "Delete node", "Delete Node": "Delete Node", "Delete Report": "Delete Report", "Delete resource": "Delete resource", "Delete resources": "Delete resources", "Delete Role": "Delete Role", "Delete row": "Delete row", "Delete Schedule": "Delete Schedule", "Delete Section": "Delete Section", "Delete Sheets": "Delete Sheets", "Delete step": "Delete step", "Delete stock status": "Delete stock status", "Delete subtask": "Delete subtask", "Delete table": "Delete table", "Delete Tag": "Delete Tag", "Delete Task": "Delete Task", "Delete tasks": "Delete tasks", "Delete Tasks": "Delete Tasks", "Delete Team": "Delete Team", "Delete Template": "Delete Template", "Delete this layer": "Delete this layer", "Delete Transaction": "Delete Transaction", "Delete values": "Delete values", "Delete vendor": "Delete vendor", "Delete View": "Delete View", "Delete warehouse type": "Delete warehouse type", "Delete Widget": "Delete Widget", "Delete workflow": "Delete workflow", "Deleted": "Deleted", "deleted a file": "deleted a file", "deleted a folder": "deleted a folder", "deleted a sub folder": "deleted a sub folder", "deleted a version": "deleted a version", "Deleted Columns": "Deleted Columns", "Deleted On": "Deleted On", "deleted successfully": "deleted successfully", "Deleted successfully": "Deleted successfully", "deleted the block": "deleted the block", "deleted the document": "deleted the document", "deleted the field": "deleted the field", "deleted the file": "deleted the file", "deleted the folder": "deleted the folder", "deleted the form": "deleted the form", "deleted the section": "deleted the section", "deleted the tags": "deleted the tags", "deleted the template": "deleted the template", "deleted the transmittal": "deleted the transmittal", "deleted the version": "deleted the version", "Deleting the template will also delete all its forms": "Deleting the template will also delete all its forms", "Deleting the workflow will remove all associated fields and all data collected for the features/fields. This action is irreversible. Are you sure you want to delete the workflow?": "Deleting the workflow will remove all associated fields and all data collected for the features/fields. This action is irreversible. Are you sure you want to delete the workflow?", "Delivered quantity": "Delivered quantity", "Dependencies": "Dependencies", "Dependency": "Dependency", "Dependency Warning": "Dependency Warning", "Description": "Description", "Design": "Design", "Design custom email with dynamic data": "Design custom email with dynamic data", "Design form": "Design form", "Designation": "Designation", "Desktop": "Desktop", "Desktop notifications": "Desktop notifications", "Destination": "Destination", "Detach": "<PERSON><PERSON>", "Detach component": "Detach component", "Detach Component": "Detach Component", "Detach File": "Detach File", "Detach Task": "Detach Task", "Detail view": "Detail view", "Detailed breakdown of defects by each block across various parameters": "Detailed breakdown of defects by each block across various parameters", "Detailed summary": "Detailed summary", "Detailed tracking of the defects assigned and it's resolution progress": "Detailed tracking of the defects assigned and it's resolution progress", "details": "details", "Details": "Details", "Device": "<PERSON><PERSON>", "Didn't receive an email? Resend in": "Didn't receive an email? Resend in", "Didn’t receive an email? Resend in": "Didn’t receive an email? Resend in", "Didnt receive an email?": "Didn't receive an email?", "Digital twin": "Digital twin", "Direct user permission": "Direct user permission", "Disabled": "Disabled", "Discard": "Discard", "Discipline": "Discipline", "Discoverable and open to anyone with a certain domain": "Discoverable and open to anyone with a certain domain", "Dismiss": "<PERSON><PERSON><PERSON>", "Display all": "Display all", "Display chart": "Display chart", "Display Labels": "Display Labels", "Display settings": "Display settings", "Display sVR": "Display sVR", "Display Tasks and Forms of only visible component's": "Display Tasks and Forms of only visible component's", "Display Tasks and Forms of only visible component’s": "Display Tasks and Forms of only visible component’s", "Display the form in all the respective component detail pages.": "Display the form in all the respective component detail pages.", "Display values": "Display values", "Display various types of transaction workflows with detailed information, along with a few configuration options and access controls.": "Display various types of transaction workflows with detailed information, along with a few configuration options and access controls.", "Display when": "Display when", "Displayed fields": "Displayed fields", "Displayed filters": "Displayed filters", "Displayed layers": "Displayed layers", "Displays complete or custom information from the selected form.": "Displays complete or custom information from the selected form.", "Distance": "Distance", "Dms": "Dms", "DMS": "DMS", "Do not disturb": "Do not disturb", "Do not disturb me on my days off": "Do not disturb me on my days off", "do not have access to the file": "do not have access to the file", "Do you want to create tasks for the selected issues?": "Do you want to create tasks for the selected issues?", "Do you want to customize the report?": "Do you want to customize the report?", "Do you want to remove activity trackings?": "Do you want to remove activity trackings?", "Do you want to restore the previous session?": "Do you want to restore the previous session?", "Do you want to save your changes before navigating away?": "Do you want to save your changes before navigating away?", "document": "document", "Document": "Document", "Document details": "Document details", "Document exported": "Document exported", "Document generation": "Document generation", "Document management system": "Document management system", "Document marked as ": "Document marked as ", "Document marked as submitted": "Document marked as submitted", "Document marking as submitted failed": "Document marking as submitted failed", "Document name": "Document name", "Document No": "Document No", "Document number": "Document number", "Document properties": "Document properties", "Document reassigned successfully": "Document reassigned successfully", "Document reassigning failed": "Document reassigning failed", "Document reopened successfully": "Document reopened successfully", "Document reopening failed": "Document reopening failed", "Document Security.": "Document Security.", "Document Statuses": "Document Statuses", "Document synced successfully": "Document synced successfully", "Document syncing failed ": "Document syncing failed ", "Document template": "Document template", "Document unlocked successfully": "Document unlocked successfully", "Document unlocking failed": "Document unlocking failed", "document(s) selected": "document(s) selected", "Documentation": "Documentation", "Documents": "Documents", "Documents cancellation failed": "Documents cancellation failed", "Documents cancelled successfully": "Documents cancelled successfully", "Documents exported": "Documents exported", "Documents list": "Documents list", "Documents un-cancellation failed": "Documents un-cancellation failed", "Documents uncancelled successfully": "Documents uncancelled successfully", "documents will be deleted. You can restore them from Trash": "documents will be deleted. You can restore them from Trash.", "documents will be deleted. You can restore them from Trash.": "documents will be deleted. You can restore them from Trash.", "does not contain": "does not contain", "does not have access to the file": "does not have access to the file", "Don't have an account?": "Don't have an account?", "Don't send": "Don't send", "Don't show this again": "Don't show this again", "Done": "Done", "Download": "Download", "Download a pre-formatted XLSX file to use as a template for your import.": "Download a pre-formatted XLSX file to use as a template for your import.", "Download all": "Download all", "Download cancelled": "Download cancelled", "Download completed successfully": "Download completed successfully", "Download layer based reports - DSM, DTM": "Download layer based reports - DSM, DTM", "Download now": "Download now", "Download sample csv": "Download sample csv", "Download sample file to enter your data": "Download sample file to enter your data", "Download successfully cancelled": "Download successfully cancelled", "Download table": "Download table", "Download template": "Download template", "Download without customization": "Download without customization", "downloaded a file": "downloaded a file", "downloaded a folder": "downloaded a folder", "downloaded the file": "downloaded the file", "downloaded the folder": "downloaded the folder", "draft": "draft", "Draft": "Draft", "Draft transaction created": "Draft transaction created", "Drafts": "Drafts", "drag and drop": "drag and drop", "Drag and Drop": "Drag and Drop", "Drag and drop file here": "Drag and drop file here", "Drag/resize boxes around the location of your sheet number and description": "Drag/resize boxes around the location of your sheet number and description", "Draw": "Draw", "Drawing": "Drawing", "Drawing & Document No": "Drawing & Document No", "Drawing category": "Drawing category", "Drawing could not be deleted": "Drawing could not be deleted", "Drawing created": "Drawing created", "Drawing deleted successfully": "Drawing deleted successfully", "Drawing name": "Drawing name", "Drawing with name": "Drawing with name", "DRE": "DRE", "Dropdown": "Dropdown", "Dual axis": "Dual axis", "due date": "due date", "Due date": "Due date", "Due Date": "Due Date", "Due on": "Due on", "due to mandatory inspection check in": "due to mandatory inspection check in", "DueDate": "DueDate", "Duplicate": "Duplicate", "Duplicate activity IDs are present": "Duplicate activity IDs are present", "Duplicate Asset": "Duplicate Asset", "Duplicate Checklist": "Duplicate Checklist", "Duplicate component": "Duplicate component", "Duplicate field": "Duplicate field", "Duplicate Form": "Duplicate Form", "Duplicate IDs found": "Duplicate IDs found", "Duplicate of": "Duplicate of", "Duplicate role": "Duplicate role", "Duplicate sheets found": "Duplicate sheets found", "Duplicate Task": "Duplicate Task", "Duplicate team": "Duplicate team", "Duplicate template": "Duplicate template", "Duplicate to": "Duplicate to", "Duplicate value": "Duplicate value", "Duplicate workflow": "Duplicate workflow", "Duplicated at": "Duplicated at", "Duplicated successfully": "Duplicated successfully", "Duplicates": "Duplicates", "Duplicates found": "Duplicates found", "Duration": "Duration", "Duration can be updated only in unpublished mode": "Duration can be updated only in unpublished mode", "durations": "durations", "Durations": "Durations", "Earlier": "Earlier", "early": "early", "Earned value": "Earned value", "Earned Value": "Earned Value", "Earned value analysis": "Earned value analysis", "earned values": "earned values", "Earned Values": "Earned Values", "Edit": "Edit", "Edit approver": "Edit approver", "Edit block": "Edit block", "Edit Category": "Edit Category", "Edit Checklist": "Edit Checklist", "Edit Class": "Edit Class", "Edit column": "Edit column", "Edit columns": "Edit columns", "Edit condition": "Edit condition", "Edit dashboard": "Edit dashboard", "Edit Dashboard": "Edit Dashboard", "Edit dependency": "Edit dependency", "Edit details": "Edit details", "Edit entry": "Edit entry", "Edit feature type": "Edit feature type", "Edit field": "Edit field", "Edit Field": "Edit Field", "Edit Folder": "Edit <PERSON>", "Edit geometry": "Edit geometry", "Edit Group": "Edit Group", "Edit Information": "Edit Information", "Edit issue purpose": "Edit issue purpose", "Edit Item": "<PERSON>em", "Edit Map": "Edit Map", "Edit ordered": "Edit ordered", "Edit Properties": "Edit Properties", "Edit property": "Edit property", "Edit purchase order": "Edit purchase order", "Edit Reminder": "<PERSON>minder", "Edit report": "Edit report", "Edit Report": "Edit Report", "Edit resource": "Edit resource", "Edit Rule": "Edit Rule", "Edit schedule settings": "Edit schedule settings", "Edit scope": "Edit scope", "Edit section": "Edit section", "Edit Section": "Edit Section", "Edit status": "Edit status", "Edit subsection": "Edit subsection", "Edit subtask": "Edit subtask", "Edit Task": "Edit Task", "Edit template": "Edit template", "Edit Template": "Edit Template", "Edit transmittal": "Edit transmittal", "Edit View": "Edit View", "Edit warehouse": "Edit warehouse", "Edit widgets": "Edit widgets", "Edit workflow": "Edit workflow", "Edit Workflow": "Edit Workflow", "Editable": "Editable", "Editor": "Editor", "Efficiency": "Efficiency", "Efficiently import and manage your project schedules": "Efficiently import and manage your project schedules", "Effortlessly manage document reviews with our predefined statuses": "Effortlessly manage document reviews with our predefined statuses", "Electrical": "Electrical", "Elevation": "Elevation", "Elevation profile": "Elevation profile", "Elevation Profile": "Elevation Profile", "Ellipse": "Ellipse", "Email": "Email", "Email & Notification": "Email & Notification", "Email & Notifications": "Email & Notifications", "Email Address": "Email Address", "Email and calendar, together in one place. Send, receive, and manage your email.": "Email and calendar, together in one place. Send, receive, and manage your email.", "Email and Notification": "Email and Notification", "Empty folder": "Empty folder", "Empty project (recommended)": "Empty project (recommended)", "Enable pallet number tracking": "Enable pallet number tracking", "Enable resources tracking to view this": "Enable resources tracking to view this", "Enable scan": "Enable scan", "Enable serial number tracking": "Enable serial number tracking", "Enable step": "Enable step", "Enable sync": "Enable sync", "Enable the workflows by clicking on the enable button to access them.": "Enable the workflows by clicking on the enable button to access them.", "Enable this option if you want to track cost/budget for activities, resources and track the activities. You can disable this option anytime.": "Enable this option if you want to track cost/budget for activities, resources and track the activities. You can disable this option anytime.", "Enable this option to define labor, material and other resources, assign them to the activities to track workload efficiently. You can create/manage resources once the schedule is created. You can disable this option anytime.": "Enable this option to define labor, material and other resources, assign them to the activities to track workload efficiently. You can create/manage resources once the schedule is created. You can disable this option anytime.", "Enable Workflow": "Enable Workflow", "Enable Workflows": "Enable Workflows", "enable-reset-progress-data-irreversible": "Enable the below option to override all recorded actual progress data so far for the activities and reset to the selected version. This operation is irreversible.", "Enabled": "Enabled", "end": "end", "End": "End", "End date": "End date", "ending in": "ending in", "ends with": "ends with", "Enter": "Enter", "Enter a location": "Enter a location", "Enter a schedule name": "Enter a schedule name", "Enter address": "Enter address", "Enter bcc": "Enter bcc", "Enter block name": "Enter block name", "Enter category name": "Enter category name", "Enter cc": "Enter cc", "Enter city": "Enter city", "Enter code": "Enter code", "Enter comments": "Enter comments", "Enter company": "Enter company", "Enter component name": "Enter component name", "Enter country": "Enter country", "Enter current password": "Enter current password", "Enter date": "Enter date", "Enter description": "Enter description", "Enter Description": "Enter Description", "Enter designation": "Enter designation", "Enter drawing name": "Enter drawing name", "Enter email": "Enter email", "Enter Email": "<PERSON><PERSON>", "Enter email address": "Enter email address", "Enter Email Addresses": "Enter Email Addresses", "Enter entry name": "Enter entry name", "Enter field name": "Enter field name", "Enter first name": "Enter first name", "Enter form name": "Enter form name", "Enter Form Name": "Enter Form Name", "enter form template description": "Enter Form Template Description", "enter form template name": "Enter Form Template Name", "Enter Formula": "Enter Formula", "Enter formula here": "Enter formula here", "Enter info here": "Enter info here", "Enter interval": "Enter interval", "Enter issue purpose": "Enter issue purpose", "Enter item name": "Enter item name", "Enter item number": "Enter item number", "Enter item type": "Enter item type", "Enter key": "Enter key", "Enter last name": "Enter last name", "Enter measurement between two points": "Enter measurement between two points", "Enter measurement unit name": "Enter measurement unit name", "Enter message here": "Enter message here", "Enter minimum number of responses required for approval": "Enter minimum number of responses required for approval", "Enter mobile number": "Enter mobile number", "Enter name": "Enter name", "Enter Name": "Enter Name", "Enter name of duplicate field": "Enter name of duplicate field", "Enter name of duplicate section": "Enter name of duplicate section", "Enter new password": "Enter new password", "Enter new password again": "Enter new password again", "Enter new version name": "Enter new version name", "Enter number": "Enter number", "Enter number of days": "Enter number of days", "Enter option value": "Enter option value", "Enter or paste item names here": "Enter or paste item names here", "Enter ordered quantity": "Enter ordered quantity", "Enter organisation address": "Enter organisation address", "Enter organization address": "Enter organization address", "Enter password": "Enter password", "Enter password again": "Enter password again", "Enter plural label": "Enter plural label", "Enter prefix": "Enter prefix", "Enter property": "Enter property", "Enter property name": "Enter property name", "Enter purchase order quantity": "Enter purchase order quantity", "Enter Qty here": "Enter Qty here", "Enter quantity label": "Enter quantity label", "Enter reason": "Enter reason", "Enter reason for cancellation": "Enter reason for cancellation", "Enter remarks": "Enter remarks", "Enter role name": "Enter role name", "Enter schema": "Enter schema", "Enter scope": "Enter scope", "Enter section name": "Enter section name", "Enter serial number": "Enter serial number", "Enter Signature": "Enter Signature", "Enter starting number": "Enter starting number", "Enter status name": "Enter status name", "Enter subject": "Enter subject", "Enter subtitle": "Enter subtitle", "Enter suffix": "Enter suffix", "Enter symbol": "Enter symbol", "Enter tag name": "Enter tag name", "enter template description": "Enter template description", "enter template name": "Enter template name", "Enter template name": "Enter template name", "Enter Template Name": "Enter Template Name", "Enter text here": "Enter text here", "Enter Text Here": "Enter Text Here", "Enter the currency": "Enter the currency", "Enter the name of the schedule to delete": "Enter the name of the schedule to delete", "Enter the name of the workflow to delete": "Enter the name of the workflow to delete", "Enter the plural name": "Enter the plural name", "Enter the serial/pallet numbers for the PV Modules.": "Enter the serial/pallet numbers for the PV Modules.", "Enter the singular name": "Enter the singular name", "Enter the template name": "Enter the template name", "Enter title": "Enter title", "Enter value": "Enter value", "Enter values": "Enter values", "Enter Vehicle Number": "Enter Vehicle Number", "Enter vendor description": "Enter vendor description", "Enter vendor name": "Enter vendor name", "Enter view name": "Enter view name", "Enter warehouse name": "Enter warehouse name", "Enter warehouse number": "Enter warehouse number", "Enter warehouse type name": "Enter warehouse type name", "Enter weight": "Enter weight", "Enter your email": "Enter your email", "Enter your email address and we'll send you a link to reset your password": "Enter your email address and we'll send you a link to reset your password", "Enter your name": "Enter your name", "Enter your notes": "Enter your notes", "Enter your password to confirm cancellation": "Enter your password to confirm cancellation", "entries": "entries", "entry": "entry", "Entry created successfully": "Entry created successfully", "Entry Deleted successfully": "Entry Deleted successfully", "Entry status": "Entry status", "Entry updated successfully": "Entry updated successfully", "EPC Approver": "EPC Approver", "equals exactly": "equals exactly", "Error": "Error", "Error in generating a transaction number": "Error in generating a transaction number", "Error in row": "Error in row", "Error while resending verification code": "Error while resending verification code", "errors to proceed with import": "errors to proceed with import", "esc": "esc", "Escalate": "Escalate", "Escalate after": "Escalate after", "Escalate to other members if there is no outcome within in the defined time period": "Escalate to other members if there is no outcome within in the defined time period", "Escalate to other members if there is no outcome within the defined time period.": "Escalate to other members if there is no outcome within the defined time period.", "Estimated cost": "Estimated cost", "Estimated finish date": "Estimated finish date", "Estimated Finish Date": "Estimated Finish Date", "Event": "Event", "events": "events", "every": "every", "Every": "Every", "Every day": "Every day", "Every month": "Every month", "Every week": "Every week", "Everyone": "Everyone", "Everything": "Everything", "Exact date": "Exact date", "Exact material": "Exact material", "Exact stock": "Exact stock", "example": "example", "Example": "Example", "exceeding by": "exceeding by", "Exceeding by": "Exceeding by", "exceeds": "exceeds", "Exceeds": "Exceeds", "excess": "excess", "Excess quantity added": "Excess quantity added", "Excess quantity entered": "Excess quantity entered", "Execute when": "Execute when", "Executive summary": "Executive summary", "Executive Summary": "Executive Summary", "Existing user’s email address": "Existing user’s email address", "Exit compare": "Exit compare", "Expand": "Expand", "Expand all": "Expand all", "Expand branch": "Expand branch", "Expected Date": "Expected Date", "Expected Delivery Date": "Expected Delivery Date", "Expired": "Expired", "Expired on": "Expired on", "Expires on": "Expires on", "export": "Export", "Export": "Export", "Export all": "Export all", "Export as": "Export as", "Export as CSV": "Export as CSV", "Export as PDF": "Export as PDF", "Export BOM report": "Export BOM report", "Export CSV": "Export CSV", "Export data": "Export data", "Export data to spreadsheet": "Export data to spreadsheet", "Export defects": "Export defects", "Export Defects": "Export Defects", "Export Defects without Attachments": "Export Defects without Attachments", "Export Excel": "Export Excel", "Export form data as a PDF using a custom layout/structure.": "Export form data as a PDF using a custom layout/structure.", "Export forms": "Export forms", "Export only tracked activities": "Export only tracked activities", "Export pdf": "Export pdf", "Export PDF": "Export PDF", "Export progress data": "Export progress data", "Export Progress data": "Export Progress data", "Export progress history": "Export progress history", "Export register": "Export register", "Export request received": "Export request received", "Export schedule": "Export schedule", "Export Summary": "Export Summary", "Export tasks": "Export tasks", "Export transactions": "Export transactions", "Export transmittal": "Export transmittal", "Export xlsx": "Export xlsx", "export-only-tracked-activities-description": "Activities whose progress is updated through the platform either manually or automatically", "export-schedule-description": "The schedule will be exported in an xlsx format that can be used to import and update existing schedule on Primavera P6. The exported file will include all activities by default. Check the below options to import specific activities", "Exported CSV": "Exported CSV", "Exported PDF": "Exported PDF", "Exported XLSX": "Exported XLSX", "Exporting BOM report": "Exporting BOM report", "Exporting CSV": "Exporting CSV", "Exporting document": "Exporting document", "Exporting documents": "Exporting documents", "Exporting History": "Exporting History", "Exporting PDF": "Exporting PDF", "Exporting progress report": "Exporting progress report", "Exporting Report": "Exporting Report", "Exporting Stocks": "Exporting Stocks", "Exporting Template": "Exporting Template", "Exporting to CSV": "Exporting to CSV", "Exporting to PDF": "Exporting to PDF", "Exporting to XLSX": "Exporting to XLSX", "Exporting Transactions": "Exporting Transactions", "External": "External", "External Images": "External Images", "External stakeholders will be unable to submit any documents that have already been submitted.": "External stakeholders will be unable to submit any documents that have already been submitted.", "External submissions": "External submissions", "External submittal link generated": "External submittal link generated", "Extract a single value metric for the selected activities from the schedule.": "Extract a single value metric for the selected activities from the schedule.", "Extract insights, s-curve, charts and other metrics.": "Extract insights, s-curve, charts and other metrics.", "Extract members of the team": "Extract members of the team", "Extracting sheet": "Extracting sheet", "Extracting sheet as pdf": "Extracting sheet as pdf", "Failed to configure columns": "Failed to configure columns", "Failed to create a draft of the transaction": "Failed to create a draft of the transaction", "Failed to create custom field": "Failed to create custom field", "Failed to create item": "Failed to create item", "Failed to create role": "Failed to create role", "Failed to create team": "Failed to create team", "Failed to create transaction": "Failed to create transaction", "Failed to create warehouse": "Failed to create warehouse", "Failed to create workflow template": "Failed to create workflow template", "Failed to delete custom field": "Failed to delete custom field", "Failed to delete item": "Failed to delete item", "Failed to delete item type": "Failed to delete item type", "Failed to delete measurement": "Failed to delete measurement", "Failed to delete role": "Failed to delete role", "Failed to delete stock status": "Failed to delete stock status", "Failed to delete team": "Failed to delete team", "Failed to delete vendor": "Failed to delete vendor", "Failed to delete warehouse": "Failed to delete warehouse", "Failed to duplicate template": "Failed to duplicate template", "Failed to export transaction": "Failed to export transaction", "Failed to export transactions": "Failed to export transactions", "Failed to generate a document for": "Failed to generate a document for", "Failed to generate a document for 'Inspection report'. Please update the document and retry.": "Failed to generate a document for 'Inspection report'. Please update the document and retry.", "Failed to generate a document for “Inspection report”. Please update the document and retry.": "Failed to generate a document for “Inspection report”. Please update the document and retry.", "Failed to generate transaction number": "Failed to generate transaction number", "Failed to import aliases.": "Failed to import aliases.", "Failed to import your data.": "Failed to import your data.", "Failed to invite users": "Failed to invite users", "Failed to load template": "Failed to load template", "Failed to parse your data.": "Failed to parse your data.", "Failed to publish transaction": "Failed to publish transaction", "Failed to remove user from the asset": "Failed to remove user from the asset", "Failed to save template": "Failed to save template", "Failed to sync bill of material": "Failed to sync bill of material", "Failed to update item": "Failed to update item", "Failed to update item status": "Failed to update item status", "Failed to update role": "Failed to update role", "Failed to update team": "Failed to update team", "Failed to update the Activity ID": "Failed to update the Activity ID", "Failed to update the field value": "Failed to update the field value", "Failed to update user": "Failed to update user", "Failed to update warehouse": "Failed to update warehouse", "Fallback": "Fallback", "False": "False", "feature": "feature", "Feature filters": "Feature filters", "Feature name": "Feature name", "Feature names are copied to your clipboard.": "Feature names are copied to your clipboard.", "Feature properties": "Feature properties", "Feature Type Symbology": "Feature Type Symbology", "Feature types": "Feature types", "features": "features", "Features": "Features", "Features are colored by properties. Click Reset to color by classes.": "Features are colored by properties. Click Reset to color by classes.", "Features do not belong to the same workflow": "Features do not belong to the same workflow", "Features types": "Features types", "Feb": "Feb", "February": "February", "Feedback": "<PERSON><PERSON><PERSON>", "field": "field", "Field": "Field", "Field created successfully": "Field created successfully", "Field deletion failed!": "Field deletion failed!", "Field duplicated successfully": "Field duplicated successfully", "Field is required": "Field is required", "Field label": "Field label", "Field Mapping": "Field Mapping", "Field mappings": "Field mappings", "Field name": "Field name", "Field Name": "Field Name", "Field Quantity": "Field Quantity", "Field type": "Field type", "Field Type": "Field Type", "Field updated successfully": "Field updated successfully", "Field with same name already exist": "Field with same name already exist", "field(s) can be selected": "field(s) can be selected", "field(s) have to be selected": "field(s) have to be selected", "Fields": "Fields", "Fields letter Assignment": "Fields letter Assignment", "Fields to show": "Fields to show", "Fields visibility": "Fields visibility", "file": "file", "File": "File", "File archived successfully": "File archived successfully", "File deleted successfully": "File deleted successfully", "file downloaded": "file downloaded", "file has been archived successfully": "file has been archived successfully", "file has been archived sucessfully": "file has been archived sucessfully", "file has been deleted successfully": "file has been deleted successfully", "File has been restored successfully": "File has been restored successfully", "file has been unarchived successfully": "file has been unarchived successfully", "File preview": "File preview", "File renamed successfully": "File renamed successfully", "File restored successfully": "File restored successfully", "File type is not supported for preview": "File type is not supported for preview", "File unarchived successfully": "File unarchived successfully", "File upload": "File upload", "File Upload": "File Upload", "File upload failed": "File upload failed", "File(s) uploaded successfully": "File(s) uploaded successfully", "File/folder deleted successfully": "File/folder deleted successfully", "file/folder has been deleted successfully": "file/folder has been deleted successfully", "File/folder moved successfully": "File/folder moved successfully", "File/folder shared successfully": "File/folder shared successfully", "files": "Files", "Files": "Files", "Files are being zipped to a folder and will be downloaded soon": "Files are being zipped to a folder and will be downloaded soon", "Files Connect SharePoint Method.": "Files Connect SharePoint Method.", "Files count": "Files count", "Files downloaded": "Files downloaded", "Files shared successfully": "Files shared successfully", "FILES_ADDED_PASSWORD": "added a password", "FILES_BULK_UPLOAD_FILES": "uploaded files", "FILES_CHANGED_PASSWORD": "changed a password", "FILES_CREATE_FILE": "created a new file", "FILES_CREATE_FILE_VERSION": "added a new version", "FILES_CREATE_FOLDER": "created a new sub folder", "FILES_DELETE_CHILD_FOLDER": "deleted a child folder", "FILES_DELETE_FILE": "deleted the file", "FILES_DELETE_FILE_VERSION": "deleted the version", "FILES_DELETE_FOLDER": "deleted a folder", "FILES_DOWNLOAD_FILE": "downloaded the file", "FILES_DOWNLOAD_FOLDER": "downloaded the folder", "FILES_DOWNLOAD_SHARED_FILE": "downloaded shared file", "FILES_FILE_DEFAULT_VERSION_CHANGED": "marked the version as active", "FILES_READ_FILE": "viewed the file", "FILES_RESET_PASSWORD": "reset a password", "FILES_RESTORE_FILE": "restored the file", "FILES_RESTORE_FOLDER": "restored the folder", "FILES_SHARE_FILE": "shared a file", "FILES_UPDATE_FILE": "renamed the file to ", "FILES_UPDATE_FOLDER": "renamed the folder to ", "files-uploaded-to-project": "The following files will be uploaded to the project", "files/folders archived": "files/folders archived", "Files/folders archived successfully": "Files/folders archived successfully", "Files/folders deleted successfully": "Files/folders deleted successfully", "files/folders have been archived successfully": "files/folders have been archived successfully", "files/folders have been deleted successfully": "files/folders have been deleted successfully.", "files/folders have been restored successfully": "files/folders have been restored successfully", "files/folders have been shared successfully": "files/folders have been shared successfully", "files/folders have been unarchived successfully": "files/folders have been unarchived successfully", "files/folders have been zipped and downloaded successfully": "files/folders have been zipped and downloaded successfully", "files/folders moved successfully": "files/folders moved successfully", "Files/folders restored successfully": "Files/folders restored successfully", "Files/folders shared successfully": "Files/folders shared successfully", "files/folders shared with you": "files/folders shared with you", "Files/folders unarchived successfully": "Files/folders unarchived successfully", "Fill": "Fill", "Fill and submit any form irrespective of the assignment": "Fill and submit any form irrespective of the assignment", "Fill color": "Fill color", "Fill form": "Fill form", "Fill Items": "Fill Items", "Fill multiple forms at the same time": "Fill multiple forms at the same time", "Fill necessary details": "Fill necessary details", "Fill opacity": "Fill opacity", "Fill Opacity": "Fill Opacity", "Fill pattern": "Fill pattern", "Fill serial numbers": "Fill serial numbers", "Fill the details using the template and upload the file below to import instances": "Fill the details using the template and upload the file below to import instances", "Fill the required transaction fields": "Fill the required transaction fields", "Filling out this form is mandatory to publish transaction": "Filling out this form is mandatory to publish transaction", "Filling out this form is optional": "Filling out this form is optional", "Filter": "Filter", "Filter activities": "Filter activities", "Filter configurations": "Filter configurations", "Filter defects": "Filter defects", "Filter filters": "Filter filters", "Filter for components": "Filter for components", "Filter for records where": "Filter for records where", "Filter layers": "Filter layers", "Filter name": "Filter name", "Filter records where": "Filter records where", "Filter tasks": "Filter tasks", "filters": "Filters", "Filters": "Filters", "Filters are not applied to this widget": "Filters are not applied to this widget", "Final block decision": "Final block decision", "Finance/Budget managment": "Finance/Budget managment", "Find and update": "Find and update", "Finish": "Finish", "Finish date": "Finish date", "Finish date status": "Finish date status", "Finish No Earlier Than": "Finish No Earlier Than", "Finish No Later Than": "Finish No Later Than", "Finish to finish": "Finish to finish", "Finish to Finish": "Finish to Finish", "Finish to start": "Finish to start", "Finish to Start": "Finish to Start", "finished": "finished", "Finished": "Finished", "First name": "First name", "first-letter-of-friday": "F", "first-letter-of-monday": "M", "first-letter-of-saturday": "S", "first-letter-of-sunday": "S", "first-letter-of-thursday": "T", "first-letter-of-tuesday": "T", "first-letter-of-wednesday": "W", "First-time customers can try SenseHawk for free for 30 days. If you want, we'll provide you with a free, personalized onboarding call to get you up and running as soon as possible.": "First-time customers can try SenseHawk for free for 30 days. If you want, we'll provide you with a free, personalized onboarding call to get you up and running as soon as possible.", "First-time customers can try SenseHawk for free for 30 days. If you want, we’ll provide you with a free, personalized onboarding call to get you up and running as soon as possible.": "First-time customers can try SenseHawk for free for 30 days. If you want, we’ll provide you with a free, personalized onboarding call to get you up and running as soon as possible.", "Fix": "Fix", "Fixed": "Fixed", "Fixed Tilt": "Fixed Tilt", "Flat minimum": "Flat minimum", "Flight parameters": "Flight parameters", "Float": "Float", "Fly to bounds": "Fly to bounds", "folder": "folder", "Folder": "Folder", "Folder archived successfully": "Folder archived successfully", "Folder deleted successfully": "Folder deleted successfully", "folder has been archived successfully": "folder has been archived successfully", "folder has been deleted successfully": "folder has been deleted successfully", "Folder has been restored successfully": "Folder has been restored successfully", "folder has been unarchived successfully": "folder has been unarchived successfully", "Folder name": "Folder name", "Folder renamed successfully": "Folder renamed successfully", "Folder restored successfully": "Folder restored successfully", "Folder unarchived successfully": "Folder unarchived successfully", "Folder upload": "Folder upload", "Folder(s) uploaded successfully": "Folder(s) uploaded successfully", "Folders": "Folders", "Font size": "Font size", "for": "for", "for approval block": "for approval block", "For security reasons, we need to verify your domain. Please enter your company email below": "For security reasons, we need to verify your domain. Please enter your company email below", "for submission": "for submission", "for the document is": "for the document is", "Foreground": "Foreground", "Forgot password": "Forgot password", "Forgot password?": "Forgot password?", "Forgot your password": "Forgot your password", "form": "form", "Form": "Form", "Form Block": "Form Block", "Form created successfully": "Form created successfully", "Form creation failed!": "Form creation failed!", "Form escalated": "Form escalated", "Form field": "Form field", "Form id": "Form id", "Form Instance deleted successfully": "Form Instance deleted successfully", "form is integrated": "form is integrated", "Form name": "Form name", "Form Name": "Form Name", "Form not found": "Form not found", "Form reopened due to": "Form reopened due to", "Form responses": "Form responses", "Form rolledback due to": "Form rolledback due to", "form schedule triggered": "form schedule triggered", "form submitted": "Form submitted", "Form submitted": "Form submitted", "form submitted for": "form submitted for", "Form template": "Form template", "Form template deleted successfully": "Form template deleted successfully", "Form Updated Successfully": "Form Updated Successfully", "Form Updating failed!": "Form Updating failed!", "Form Updation Failed!": "Form Updation Failed!", "Form Workflow": "Form Workflow", "form-submission-note": "Submitted by accident? Don't worry, your administrator can revert the form back to you.", "form-submission-successful": "Your submission was successful. You will receive a copy of your submission in your email.", "Format: XLSX": "Format: XLSX", "formats supported": "formats supported", "Formatting": "Formatting", "forms": "forms", "Forms": "Forms", "FORMS": "FORMS", "forms are integrated": "forms are integrated", "forms are pending for review. Deselect them in order to proceed": "forms are pending for review. Deselect them in order to proceed", "Forms could not be saved.": "Forms could not be saved.", "forms could not be submitted.": "forms could not be submitted.", "Forms could not be submitted.": "Forms could not be submitted.", "forms have been archived": "forms have been archived", "forms have been archived as the rest are in different blocks.": "forms have been archived as the rest are in different blocks.", "forms have been unarchived": "forms have been unarchived", "Forms list": "Forms list", "Forms List": "Forms List", "Forms must belong to the same step to bulk fill": "Forms must belong to the same step to bulk fill", "Forms saved successfully.": "Forms saved successfully.", "forms selected": "forms selected", "Forms submitted successfully.": "Forms submitted successfully.", "Formula": "Formula", "Formula test": "Formula test", "Formula will pick up the fields which are only above the formula field.": "Formula will pick up the fields which are only above the formula field.", "Four weeks": "Four weeks", "Frame rate": "Frame rate", "Free float": "Free float", "Free slack": "Free slack", "Free Slack": "Free Slack", "Free trial expired": "Free trial expired", "Freehand": "Freehand", "Frequency": "Frequency", "Fri": "<PERSON><PERSON>", "Friday": "Friday", "from": "from", "From": "From", "From Project": "From Project", "Frontal overlap": "Frontal overlap", "Fulfilled": "Fulfilled", "Full access": "Full access", "Full Access": "Full Access", "Full access to all assets": "Full access to all assets", "Full access to all modules and resources in the entire organization and manage asset admin users.": "Full access to all modules and resources in the entire organization and manage asset admin users.", "Full access to all modules and resources in the entire organization and manage global and asset admins.": "Full access to all modules and resources in the entire organization and manage global and asset admins.", "Full access to all modules and resources within the asset.": "Full access to all modules and resources within the asset.", "Full access to all modules and resources within the respective assets.": "Full access to all modules and resources within the respective assets.", "Full access to the schedule": "Full access to the schedule", "Full Name": "Full Name", "Fullscreen": "Fullscreen", "Fullscreen mode": "Fullscreen mode", "Gallery": "Gallery", "Gallery view": "Gallery view", "Gallery View": "Gallery View", "Gantt chart": "Gantt chart", "Gantt settings": "Gantt settings", "general": "general", "General": "General", "GENERAL": "GENERAL", "General access": "General access", "General profile settings": "General profile settings", "General settings": "General settings", "Generate": "Generate", "Generate documents": "Generate documents", "Generate new invite link": "Generate new invite link", "Generate Pattern": "Generate Pattern", "Generate PDF": "Generate PDF", "generated/ exported report": "generated/ exported report", "Generating report": "Generating report", "Geofencing": "Geofencing", "Geography": "Geography", "Get ready to import your data": "Get ready to import your data", "Get sharable link": "Get sharable link", "Get Started": "Get Started", "Get Started ": "Get Started ", "Getting started": "Getting started", "Global admin": "Global admin", "Global Admin": "Global Admin", "Global admins": "Global admins", "Go back": "Go back", "Go back to gallery": "Go back to gallery", "Go paperless with forms by enabling easy creation with diverse fields, and seamlessly assign, prioritize, and collect data.": "Go paperless with forms by enabling easy creation with diverse fields, and seamlessly assign, prioritize, and collect data.", "Go to all assets": "Go to all assets", "Go to edit mode": "Go to edit mode", "Go to login": "Go to login", "Go to read mode": "Go to read mode", "Go to template": "Go to Template", "Go to today": "Go to today", "Grant access and share your account with other team members to use in TaskMapper.": "Grant access and share your account with other team members to use in TaskMapper.", "Grant access to all assets": "Grant access to all assets", "Graphical distribution of defects in relation to temperature differences.": "Graphical distribution of defects in relation to temperature differences.", "Green": "Green", "Grey": "Grey", "Grid": "Grid", "Grid view": "Grid view", "Grid-timeline view": "Grid-timeline view", "Group": "Group", "Group by": "Group by", "Group By": "Group By", "Group by warehouse": "Group by warehouse", "Group name": "Group name", "Groups": "Groups", "GSD": "GSD", "Guest": "Guest", "has been added as the domain name": "has been added as the domain name", "has been successfully unarchived.": "has been successfully unarchived.", "has date": "has date", "Has new message": "Has new message", "has updated": "has updated", "have been submitted.": "have been submitted.", "Heading": "Heading", "Health, Safety & Envionment": "Health, Safety & Envionment", "Height": "Height", "Height(m)": "Height(m)", "Henceforth, the concerned person will be responsible to complete the task and complete within the due date": "Henceforth, the concerned person will be responsible to complete the task and complete within the due date", "here": "here", "Hi": "Hi", "Hi Team": "Hi Team", "Hidden Columns": "Hidden Columns", "Hide": "<PERSON>de", "Hide Activity": "Hide Activity", "Hide empty": "Hide empty", "Hide replies": "<PERSON><PERSON> replies", "Hide summary": "Hide summary", "Hide Summary": "Hide Summary", "Hierarchy": "Hierarchy", "Hierarchy List view": "Hierarchy List view", "High": "High", "High-level overview of the impact and resolution status of defects across the site": "High-level overview of the impact and resolution status of defects across the site", "Highlight": "Highlight", "history": "History", "History": "History", "History Exported": "History Exported", "Home": "Home", "hook triggered": "hook triggered", "Horizontal": "Horizontal", "Horizontal Bar Chart": "Horizontal Bar Chart", "Horizontal bar chart displaying the count of forms grouped by their current statuses plotted against their completed steps.": "Horizontal bar chart displaying the count of forms grouped by their current statuses plotted against their completed steps.", "Horizontal bar chart representing the count of forms by their asset of origin for a specific template and matching the filtering criteria.": "Horizontal bar chart representing the count of forms by their asset of origin for a specific template and matching the filtering criteria.", "Horizontal graph": "Horizontal graph", "Hour": "Hour", "hours": "hours", "Hours": "Hours", "Hours per day": "Hours per day", "Hours per month": "Hours per month", "Hours per quarter": "Hours per quarter", "Hours per week": "Hours per week", "Hours per year": "Hours per year", "Hover over a line on left for more details": "Hover over a line on left for more details", "hrs": "hrs", "I acknowledge that the provided information will overwrite existing data": "I acknowledge that the provided information will overwrite existing data", "I agree with the": "I agree with the", "i.e.": "i.e.", "ID": "ID", "If multiple assets are selected, BOM feature will not be supported.": "If multiple assets are selected, BOM feature will not be supported.", "If rejected": "If rejected", "If specified, activities with dates greater than the deadline will also be considered as critical.": "If specified, activities with dates greater than the deadline will also be considered as critical.", "If there are no values set for the current, the existing values will be retained or set to zero.": "If there are no values set for the current, the existing values will be retained or set to zero.", "Ignore above filters for defects without tasks": "Ignore above filters for defects without tasks", "Ignoring": "Ignoring", "Image": "Image", "Images": "Images", "Import": "Import", "Import .mpp schedules.": "Import .mpp schedules.", "Import a template from an asset into other assets.": "Import a template from an asset into other assets.", "Import an existing file from your system": "Import an existing file from your system", "Import boundaries": "Import boundaries", "Import CSV/Excel": "Import CSV/Excel", "Import failed": "Import failed", "Import file": "Import file", "Import from Primavera P6 or Microsoft Projects": "Import from Primavera P6 or Microsoft Projects", "Import Instance": "Import Instance", "Import instances": "Import instances", "Import map": "Import map", "Import schedule": "Import schedule", "Import spreadsheet": "Import spreadsheet", "Import successful": "Import successful", "Import Template": "Import Template", "Import values": "Import values", "Import was successful, imported items should reflect in the list": "Import was successful, imported items should reflect in the list", "Import XER/XML schedules.": "Import XER/XML schedules.", "Import your .csv, .xlsx files": "Import your .csv, .xlsx files", "Import your .xlsx files": "Import your .xlsx files", "Import/Export vectors, modify and delete vectors": "Import/Export vectors, modify and delete vectors", "Import/Export, modify and delete features": "Import/Export, modify and delete features", "Import/upload your doc seamlessly.": "Import/upload your doc seamlessly.", "Importing aliases…": "Importing aliases…", "Importing will overwrite your existing data": "Importing will overwrite your existing data", "Importing your data…": " Importing your data…", "Improved Team Collaboration.": "Improved Team Collaboration.", "in": "in", "In active": "In active", "In days": "In days", "in list view": "in list view", "In progress": "In progress", "In Progress": "In Progress", "In stock": "In stock", "In-progress": "In-progress", "Inactive": "Inactive", "Inactive item": "Inactive item", "Incidents": "Incidents", "Include": "Include", "Include in PDF": "Include in PDF", "Include in template": "Include in template", "Include ongoing submissions": "Include ongoing submissions", "Include subtasks": "Include subtasks", "Incoming": "Incoming", "Incomplete": "Incomplete", "Incorrect verification code.": "Incorrect verification code.", "Indent": "Indent", "Index": "Index", "Indigo": "Indigo", "Info": "Info", "Information": "Information", "Information/CC": "Information/CC", "Initial details": "Initial details", "Initial document status": "Initial document status", "Initializing download": "Initializing download", "Initiate a new transmittal to request documents or review, or both, from various members.": "Initiate a new transmittal to request documents or review, or both, from various members.", "Initiate new transmittal": "Initiate new transmittal", "Initiate seamless document submission from vendors": "Initiate seamless document submission from vendors", "Initiate seamless review for selected documents": "Initiate seamless review for selected documents", "Initiate workflow": "Initiate workflow", "Inprogress": "Inprogress", "Input": "Input", "Input cannot be negative": "Input cannot be negative", "Input is restricted to a maximum of 9 digits.": "Input is restricted to a maximum of 9 digits.", "Input type": "Input type", "Input/Review": "Input/Review", "Insert column left": "Insert column left", "Insert column right": "Insert column right", "Insert left column": "Insert left column", "Insert right column": "Insert right column", "Insert row above": "Insert row above", "Insert row below": "Insert row below", "instance": "instance", "Instance": "Instance", "Instance name": "Instance name", "Instance status": "Instance status", "Instance type": "Instance type", "Instances": "Instances", "Instantiate maintenance schedule": "Instantiate maintenance schedule", "Insufficient quantity entered": "Insufficient quantity entered", "Integrate": "Integrate", "Integrate and sync folder items from TaskMapper to Sharepoint.": "Integrate and sync folder items from TaskMapper to Sharepoint.", "Integrate with Sharepoint": "Integrate with Sharepoint", "Integrate your software application with other applications and systems seamlessly": "Integrate your software application with other applications and systems seamlessly", "Integrated by": "Integrated by", "Integrated forms cannot be deleted. Please remove the integration to delete them.": "Integrated forms cannot be deleted. Please remove the integration to delete them.", "Integrated with Sharepoint": "Integrated with Sharepoint", "integration": "integration", "Integration": "Integration", "Integrations": "Integrations", "Internal": "Internal", "Interval": "Interval", "Invalid configuration. Please reconfigure and try again.": "Invalid configuration. Please reconfigure and try again.", "Invalid data. Results in simultaneous forms": "Invalid data. Results in simultaneous forms", "Invalid data. Results in simultaneous tasks": "Invalid data. Results in simultaneous tasks", "Invalid dependency": "Invalid dependency", "Invalid duration in row": "Invalid duration in row", "Invalid location name": "Invalid location name", "Invalid min and max input range": "Invalid min and max input range", "Invalid name": "Invalid name", "Invalid planned start date in row": "Invalid planned start date in row", "Invalid predecessor in row": "Invalid predecessor in row", "Invalid predecessors found. The following ID(s) do not exist": "Invalid predecessors found. The following ID(s) do not exist", "Invalid stock": "Invalid stock", "Invalid task name": "Invalid task name", "Invalid WBS code in row": "Invalid WBS code in row", "inventory": "Inventory", "Inventory": "Inventory", "Inventory Items": "Inventory Items", "Inverse positive/negative": "Inverse positive/negative", "Invert selection": "Invert selection", "Inverter": "Inverter", "Inverter completion": "Inverter completion", "Inverter manufacturer": "Inverter manufacturer", "Inverter name": "Inverter name", "Inverters": "Inverters", "Invitation Only": "Invitation Only", "Invitation sent": "Invitation sent", "Invitations": "Invitations", "Invite": "Invite", "Invite by email": "Invite by email", "Invite colleagues": "Invite colleagues", "Invite external stakeholders to submit documents easily": "Invite external stakeholders to submit documents easily", "Invite link": "Invite link", "Invite members": "Invite members", "Invite Members": "Invite Members", "Invite members of your team to collaborate.": "Invite members of your team to collaborate.", "Invite new users, manage teams and categories": "Invite new users, manage teams and categories", "Invite new users, manager users, teams and categories": "Invite new users, manager users, teams and categories", "Invite sent": "<PERSON><PERSON><PERSON> sent", "Invite Team": "Invite Team", "Invite team members to ": "Invite team members to ", "Invite users via direct link": "Invite users via direct link", "Invite users via email": "Invite users via email", "Invite users via email and set permissions accordingly": "Invite users via email and set permissions accordingly", "Invite users via link": "Invite users via link", "Invite your team": "Invite your team", "Invite your team members to ": "Invite your team members to ", "Invite, view and manage users. Assign teams, roles and grant access and permissions.": "Invite, view and manage users. Assign teams, roles and grant access and permissions.", "Invited": "Invited", "Invited By": "Invited By", "Invited On": "Invited On", "Invites": "<PERSON><PERSON><PERSON>", "Invoice Number": "Invoice Number", "IP address": "IP address", "Irradiance": "Irradiance", "is": "is", "is a required field": "is a required field", "is a target of a few link(s). Therefore, the planned start dates mentioned in this table may be ignored.": "is a target of a few link(s). Therefore, the planned start dates mentioned in this table may be ignored.", "is after": "is after", "is any of": "is any of", "is before": "is before", "is between": "is between", "is completed with final outcome as": "is completed with final outcome as", "is equal to": "is equal to", "is greater than": "is greater than", "is invalid": "is invalid", "is less than": "is less than", "is not": "is not", "is not between": "is not between", "is not equal to": "is not equal to", "is of invalid format": "is of invalid format", "Issue purpose": "Issue purpose", "Issue transmittal to request for new documents or for an updated version of existing documents from the recipients": "Issue transmittal to request for new documents or for an updated version of existing documents from the recipients", "Issue transmittal to request review for the documents either by manually selecting recipients or through a workflow": "Issue transmittal to request review for the documents either by manually selecting recipients or through a workflow", "Issues": "Issues", "It is only available for download": "It is only available for download", "It is waiting for review": "It is waiting for review", "It looks like there is no history to display here": "It looks like there is no history to display here", "It seems like you're trying to upload an unsupported file type. Please try again with a supported file format.": "It seems like you're trying to upload an unsupported file type. Please try again with a supported file format.", "It will give full access to everyone": "It will give full access to everyone", "It will only be synced to the form created in that asset.": "It will only be synced to the form created in that asset.", "Italic": "Italic", "Item": "<PERSON><PERSON>", "Item already exists": "Item already exists", "Item code already exists. Please enter a different item code to continue.": "Item code already exists. Please enter a different item code to continue.", "Item created": "Item created", "Item creation failed. Please try again.": "Item creation failed. Please try again.", "Item deleted": "Item deleted", "Item deletion failed. Please try again.": "Item deletion failed. Please try again.", "Item details": "Item details", "Item duplicated": "Item duplicated", "Item Image": "Item Image", "Item marked active": "Item marked active", "Item marked inactive": "Item marked inactive", "Item master": "<PERSON>em master", "Item name": "Item name", "Item Name": "Item Name", "Item name already exists. Please enter a different item name to continue.": "Item name already exists. Please enter a different item name to continue.", "Item number": "Item number", "Item Number": "Item Number", "Item status update failed. Please try again.": "Item status update failed. Please try again.", "Item type": "Item type", "Item type already exists": "Item type already exists", "Item type already exists. Please enter a different item type to continue.": "Item type already exists. Please enter a different item type to continue.", "Item type created successfully": "Item type created successfully", "Item type deleted successfully": "Item type deleted successfully", "Item type deletion failed. Please try again.": "Item type deletion failed. Please try again.", "Item type updated successfully": "Item type updated successfully", "Item types": "Item types", "Item updated": "Item updated", "item(s) to...": "item(s) to...", "items": "items", "Items": "Items", "Items marked as active can be used in transactions. Are you sure you want to mark the selected item as active?": "Items marked as active can be used in transactions. Are you sure you want to mark the selected item as active?", "Items marked as inactive cannot be used in any transactions. Are you sure you want to mark the selected item as inactive?": "Items marked as inactive cannot be used in any transactions. Are you sure you want to mark the selected item as inactive?", "Items quantity can not be zero": "Items quantity can not be zero", "Items used in the asset.": "Items used in the asset.", "Jan": "Jan", "January": "January", "Job status": "Job status", "Job Status": "Job Status", "Jobs": "Jobs", "Jobsite Management, Simplified": "Jobsite Management, Simplified", "<EMAIL>": "<EMAIL>", "Join": "Join", "Jul": "Jul", "July": "July", "Jump back to the previous form block.": "Jump back to the previous form block.", "Jump to": "Jump to", "Jun": "Jun", "June": "June", "Kanban view": "Kanban view", "Key": "Key", "Keyboard Shortcuts": "Keyboard Shortcuts", "Kilometers": "Kilometers", "KML or GeoJSON files": "KML or GeoJSON files", "KW": "KW", "Label": "Label", "Labels": "Labels", "Lag": "Lag", "Lag (days)": "Lag (days)", "Lag %": "Lag %", "lags": "lags", "Lags": "Lags", "Land management": "Land management", "Last": "Last", "Last 120 days": "Last 120 days", "Last 14 days": "Last 14 days", "Last 30 days": "Last 30 days", "Last 45 days": "Last 45 days", "Last 60 days": "Last 60 days", "Last 7 days": "Last 7 days", "Last 90 days": "Last 90 days", "Last Active on": "Last Active on", "Last activity": "Last activity", "Last modified": "Last modified", "Last month": "Last month", "Last name": "Last name", "Last quarter": "Last quarter", "Last submitted by": "Last submitted by", "Last submitted on": "Last submitted on", "Last updated": "Last updated", "Last Updated": "Last Updated", "Last updated at": "Last updated at", "Last week": "Last week", "Last year": "Last year", "Latest first": "Latest first", "Latest version synced": "Latest version synced", "Layer": "Layer", "Layer 1": "Layer 1", "Layer 2": "Layer 2", "Layer not present": "Layer not present", "Layers": "Layers", "Layout": "Layout", "layout-hawknavbar-company-description": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Ullam vero consequatur omnis natus inventore. Sequi soluta odit explicabo ad velit doloremque veniam quam atque aperiam! Nulla quibusdam vel mollitia labore", "Lead time": "Lead time", "Learn more": "Learn more", "Least of all blocks": "Least of all blocks", "Left": "Left", "Left to right": "Left to right", "Legend": "Legend", "length should be less than": "length should be less than", "Lens model": "Lens model", "less": "less", "less than required": "less than required", "less than scope": "less than scope", "Let's confirm this is your domain": "Let's confirm this is your domain", "Let’s confirm this is your domain": "Let’s confirm this is your domain", "Level": "Level", "Levels": "Levels", "Limit": "Limit", "Limited": "Limited", "Limited + View all maps/layers,Share any map/layer,Update schedule tracker,Clear duplicate barcodes,Modify feature geometry,import/export feature json, properties, type and delete features": "Limited + View all maps/layers,Share any map/layer,Update schedule tracker,Clear duplicate barcodes,Modify feature geometry,import/export feature json, properties, type and delete features", "Limited access": "Limited access", "Line": "Line", "Line chart": "Line chart", "Line Chart": "Line Chart", "Link": "Link", "Link one or more blocks to the warehouse to capture the bill of material from the designs": "Link one or more blocks to the warehouse to capture the bill of material from the designs", "Link one or more blocks to the warehouse to capture the bill of material from the designs. You can also define the blocks manually and adjust their scopes as per the requirement": "Link one or more blocks to the warehouse to capture the bill of material from the designs. You can also define the blocks manually and adjust their scopes as per the requirement", "Link to Receipt": "Link to Receipt", "Link to Shipment": "Link to Shipment", "Link with components": "Link with components", "linked": "linked", "Linked Tasks": "Linked Tasks", "Linked to schedule": "Linked to schedule", "Links": "Links", "List of items": "List of items", "List of roles": "List of roles", "List view": "List view", "load": "Load", "Load from template": "Load from template", "Load more": "Load more", "Loading": "Loading", "Loading files": "Loading files", "Loading layers": "Loading layers", "Location": "Location", "Location not found": "Location not found", "Lock Document": "Lock Document", "Lock/Unlock Request": "Lock/Unlock Request", "Log out": "Log out", "Log out of other sessions": "Log out of other sessions", "Logged out from all devices!": "Logged out from all devices!", "Logo": "Logo", "Logout": "Logout", "Looks like there are no duplicates here.": "Looks like there are no duplicates here.", "Low": "Low", "Low stock": "Low stock", "Low Stock": "Low Stock", "Maintenance": "Maintenance", "Maintenance schedules": "Maintenance schedules", "Make report data specific to the viewer": "Make report data specific to the viewer", "Make sure necessary permissions are granted for the users to create forms.": "Make sure necessary permissions are granted for the users to create forms.", "Manage activities, work breakdown structure and milestones.": "Manage activities, work breakdown structure and milestones.", "Manage details": "Manage details", "Manage document reviews with our predefined statuses, which can be renamed as needed. Customize these statuses during transmittal creation to fit different workflows. 'Rejected' and 'Revise and Resubmit' are default reject statuses used for sending documents back to the initiator": "Manage document reviews with our predefined statuses, which can be renamed as needed. Customize these statuses during transmittal creation to fit different workflows. 'Rejected' and 'Revise and Resubmit' are default reject statuses used for sending documents back to the initiator", "Manage multiple versions.": "Manage multiple versions.", "Manage roles": "Manage roles", "Manage users, teams and roles": "Manage users, teams and roles", "Manager": "Manager", "Manager + Add/modify terra progress workflows": "Manager + Add/modify terra progress workflows", "Managers": "Managers", "Managing the project": "Managing the project", "Mandatory": "Mandatory", "Mandatory Field?": "Mandatory Field?", "Manual": "Manual", "manually added": "manually added", "Manually triggered the sync": "Manually triggered the sync", "Manually update progress": "Manually update progress", "Map": "Map", "Map all the required columns": "Map all the required columns", "Map columns": "Map columns", "Map CSV columns to required columns": "Map CSV columns to required columns", "Map data": "Map data", "Map feature properties with form fields": "Map feature properties with form fields", "Map Fields": "Map Fields", "Map legend": "Map legend", "Map name": "Map name", "Map Name": "Map Name", "Map review statuses": "Map review statuses", "Map the review statuses that should automatically be mapped to the corresponding document statuses.": "Map the review statuses that should automatically be mapped to the corresponding document statuses.", "Map view": "Map view", "Map view displaying forms by their statuses and matching the filter criteria.": "Map view displaying forms by their statuses and matching the filter criteria.", "Maps": "Maps", "Maps & Layers": "Maps & Layers", "Maps archived": "Maps archived", "Maps/Layers": "Maps/Layers", "MAPS/LAYERS": "MAPS/LAYERS", "Mar": "Mar", "March": "March", "Mark as active": "<PERSON> as active", "Mark as Active": "<PERSON> as Active", "Mark as admin": "<PERSON> as admin", "Mark as complete": "Mark as complete", "Mark as current": "<PERSON> as current", "Mark as current version": "Mark as current version", "Mark as default": "Mark as default", "Mark as inactive": "<PERSON> as inactive", "Mark as private": "<PERSON> as private", "Mark as submitted": "<PERSON> as submitted", "Mark sheet as private": "Mark sheet as private", "marked the document  as": "marked the document  as", "marked the document as ": "marked the document as ", "marker": "marker", "Markups": "Markups", "Maroon": "Maroon", "Master item list": "Master item list", "Match all filters (AND)": "Match all filters (AND)", "Match any filter (OR)": "Match any filter (OR)", "Match columns": "Match columns", "Material": "Material", "Material tracking": "Material tracking", "max": "max", "Max": "Max", "Max files": "Max files", "Max value allowed": "Max value allowed", "Max values to be selected": "Max values to be selected", "Max. Characters allowed": "Max. Characters allowed", "Maximum File Size": "Maximum File Size", "Maximum of 256 characters": "Maximum of 256 characters", "May": "May", "Measure": "Measure", "Measure the amount of work actually performed for a schedule beyond the basic review of cost and schedule reports": "Measure the amount of work actually performed for a schedule beyond the basic review of cost and schedule reports", "Measurement": "Measurement", "Measurement already exists": "Measurement already exists", "Measurement already exists. Please enter a different name for your measurement to continue.": "Measurement already exists. Please enter a different name for your measurement to continue.", "Measurement deletion failed. Please try again.": "Measurement deletion failed. Please try again.", "Measurement symbol already exists": "Measurement symbol already exists", "Measurement Units": "Measurement Units", "Medium": "Medium", "Member": "Member", "Member Info": "Member Info", "member on": "member on", "members": "members", "Members": "Members", "Members of the template": "Members of the template", "Members whom you want to keep in loop": "Members whom you want to keep in loop", "Members whom you want to request the documents": "Members whom you want to request the documents", "Members whom you want to review the documents": "Members whom you want to review the documents", "Members whom you want to review/approve documents along with you": "Members whom you want to review/approve documents along with you", "Members with access": "Members with access", "Members/Teams": "Members/Teams", "Mentioned me": "Mentioned me", "Mentions": "Mentions", "Merge cells": "Merge cells", "Merge Features": "Merge Features", "Message": "Message", "Message sent successfully": "Message sent successfully", "Meter": "<PERSON>er", "meters": "meters", "Meters": "Meters", "Method": "Method", "Methodology - Plant layout": "Methodology - Plant layout", "Metric": "Metric", "Metrics reflect forms submitted within the week displayed.": "Metrics reflect forms submitted within the week displayed.", "Metrics unavailable for unpublished schedules": "Metrics unavailable for unpublished schedules", "Migrate documents": "Migrate documents", "Miles": "<PERSON>", "Milestone": "Milestone", "Milestones": "Milestones", "min": "min", "Min": "Min", "Min value allowed": "Min value allowed", "Min values to be selected": "Min values to be selected", "Min. Characters allowed": "Min. Characters allowed", "Minimum approval": "Minimum approval", "Minimum Approval": "Minimum Approval", "Minimum approvals": "Minimum approvals", "Minimum approvals must be greater than or equal to 1": "Minimum approvals must be greater than or equal to 1", "Minute": "Minute", "minutes": "minutes", "Minutes": "Minutes", "Missing activity name in row": "Missing activity name in row", "Missing ID in row": "Missing ID in row", "Missing parent activities": "Missing parent activities", "Mobile": "Mobile", "Mobile App": "Mobile App", "Mobile number": "Mobile number", "Mobile Number": "Mobile Number", "mode only": "mode only", "Modified on": "Modified on", "Modified On": "Modified On", "modified the schedule": "modified the schedule", "modified the settings": "modified the settings", "Modify": "Modify", "Modify all forms": "Modify all forms", "Modify and close all tasks, import task templates from other assets": "Modify and close all tasks, import task templates from other assets", "Modify any form i.e change assignees, priority, dates, category, tags, etc": "Modify any form i.e change assignees, priority, dates, category, tags, etc", "Modify any form template i.e workflow, form and their settings": "Modify any form template i.e workflow, form and their settings", "Modify any unpublished transactions": "Modify any unpublished transactions", "Modify assigned defect properties": "Modify assigned defect properties", "Modify defect properties": "Modify defect properties", "Modify defect type": "Modify defect type", "Modify map data - name, description, add/update/delete projects, groups for maps that are manually created": "Modify map data - name, description, add/update/delete projects, groups for maps that are manually created", "Modify map data - name, description, add/update/delete projects, groups for self-serve terra": "Modify map data - name, description, add/update/delete projects, groups for self-serve terra", "Modify map data - name, description, add/update/delete projects, groups for self-serve terra, Add/modify feature type groups and types": "Modify map data - name, description, add/update/delete projects, groups for self-serve terra, Add/modify feature type groups and types", "Modify transmittal properties, manage members and perform actions on all transmittals/submittals except reviewal.": "Modify transmittal properties, manage members and perform actions on all transmittals/submittals except reviewal.", "Module": "<PERSON><PERSON><PERSON>", "Module number": "Module number", "Module progress": "Module progress", "Module serial number": "Module serial number", "Module Type": "Module Type", "Module(X,Y)": "<PERSON><PERSON><PERSON>(X,Y)", "Modules": "<PERSON><PERSON><PERSON>", "Mon": "Mon", "Monday": "Monday", "Monday morning - ": "Monday morning - ", "Money": "Money", "Month": "Month", "Month Name full": "Month Name full", "Month Name short": "Month Name short", "Month Number": "Month Number", "Month to date": "Month to date", "month(s)": "month(s)", "Monthly": "Monthly", "months": "months", "Months": "Months", "months before": "months before", "more": "more", "More": "More", "more than required": "more than required", "more than scope": "more than scope", "Most affected block": "Most affected block", "Most common defect": "Most common defect", "Most popular plan": "Most popular plan", "Move": "Move", "Move a task down within a hierarchy": "Move a task down within a hierarchy", "Move a task up within a hierarchy": "Move a task up within a hierarchy", "Move down": "Move down", "Move Here": "Move Here", "Move successful": "Move successful", "Move to next step": "Move to next step", "Move to...": "Move to...", "Move up": "Move up", "moved a file": "moved a file", "moved a file here": "moved a file here", "moved a folder here": "moved a folder here", "moved the document": "moved the document", "moved the file from": "moved the file from", "moved the file from main directory": "moved the file from main directory", "moved the file here": "moved the file here", "moved the folder": "moved the folder", "moved the folder from": "moved the folder from", "moved the folder from main directory": "moved the folder from main directory", "moved the folder here": "moved the folder here", "moved the folder to": "moved the folder to", "Multi label": "Multi label", "Multi text": "Multi text", "Multiple filters active": "Multiple filters active", "Multiple items detected": "Multiple items detected", "Multiple lines detected": "Multiple lines detected", "Multiple project tasks found. Only one project task is allowed.": "Multiple project tasks found. Only one project task is allowed.", "Multiple times": "Multiple times", "Must Finish On": "Must Finish On", "Must Start On": "Must Start On", "MW": "MW", "My Profile": "My Profile", "My submissions": "My submissions", "N/A": "N/A", "NA": "NA", "Name": "Name", "Name must be unique": "Name must be unique", "Name of Pilot": "Name of Pilot", "Name of the document": "Name of the document", "Name of the folder to import the documents": "Name of the folder to import the documents", "Names added": "Names added", "Navigation": "Navigation", "Need the following quantities to update": "Need the following quantities to update", "Need to publish to make progress changes": "Need to publish to make progress changes", "Net": "Net", "New": "New", "New Activity": "New Activity", "New Advance Shipment": "New Advance Shipment", "New asset": "New asset", "New Asset": "New Asset", "New automation": "New automation", "New block": "New block", "New category": "New category", "New Category": "New Category", "New changes have been detected, please reload to preview": "New changes have been detected, please reload to preview", "New changes is been detected. You can reload to view new changes.": "New changes is been detected. You can reload to view new changes.", "New checklist": "New checklist", "New Checklist": "New Checklist", "New column": "New column", "New comment": "New comment", "New component": "New component", "New Component": "New Component", "New condition": "New condition", "New Connection": "New Connection", "New dashboard": "New dashboard", "New Dashboard": "New Dashboard", "New Dependency": "New Dependency", "New document": "New document", "New Document": "New Document", "New document status added successfully": "New document status added successfully", "New drawing": "New drawing", "New Drawing": "New Drawing", "New entry": "New entry", "New field": "New field", "New Field": "New Field", "New file": "New file", "New folder": "New folder", "New Folder": "New Folder", "New form": "New form", "New Form": "New form", "new form template": "new form template", "New Form Template": "New Form Template", "New Forms": "New Forms", "New Instance": "New Instance", "New issue purpose": "New issue purpose", "New issue purpose added successfully": "New issue purpose added successfully", "New Item": "New Item", "New item type": "New item type", "New Items": "New Items", "New layer": "New layer", "New location detected": "New location detected", "New maintenance schedule": "New maintenance schedule", "New Map": "New Map", "New mapping": "New mapping", "New material": "New material", "New parameter": "New parameter", "New password": "New password", "New property": "New property", "New Property": "New Property", "New report": "New report", "New Report": "New Report", "New request": "New request", "New resource": "New resource", "New role": "New role", "New Role": "New Role", "New schedule": "New schedule", "New Schedule": "New Schedule", "New section": "New section", "New sheet version added successfully": "New sheet version added successfully", "New simulation": "New simulation", "New status": "New status", "New Status": "New Status", "New stock status": "New stock status", "New subsection": "New subsection", "New subtask": "New subtask", "New Subtask": "New Subtask", "New Subtasks": "New Subtasks", "New tab": "New tab", "New Tag": "New Tag", "New task": "New task", "New Task": "New Task", "New Team": "New Team", "New template": "New template", "New Template": "New Template", "New timezone": "New timezone", "New Transaction": "New Transaction", "New transmittal": "New transmittal", "New Transmittal": "New Transmittal", "New users must be invited": "New users must be invited", "New vendor": "New vendor", "New Version": "New Version", "New version could not be added": "New version could not be added", "New version of the document will be synced immediately in the register": "New version of the document will be synced immediately in the register", "New version successfully uploaded": "New version successfully uploaded", "New warehouse": "New warehouse", "New Warehouse": "New Warehouse", "New warehouse type": "New warehouse type", "New workflow": "New workflow", "New Workflow": "New Workflow", "Next": "Next", "Next 30 days": "Next 30 days", "Next 7 days": "Next 7 days", "Next cell": "Next cell", "Next number": "Next number", "Next schedule": "Next schedule", "Next week": "Next week", "No": "No", "No #Connections found": "No #Connections found", "No #Defects found": "No #Defects found", "No #Files found": "No #Files found", "No #Forms found": "No #Forms found", "No #Items found": "No #Items found", "No #Properties found": "No #Properties found", "No #Tasks found": "No #Tasks found", "No access": "No access", "No Access": "No Access", "No access to map data": "No access to map data", "No accounts found": "No accounts found", "No Activities": "No Activities", "No activities match the selected filters.": "No activities match the selected filters.", "No alerts found": "No alerts found", "No Assets": "No Assets", "No attachments": "No attachments", "No Attachments": "No Attachments", "No attachments available": "No attachments available", "No attachments for this feature": "No attachments for this feature", "No bill of material added yet.": "No bill of material added yet.", "No Categories": "No Categories", "No Category Found": "No Category Found", "No classes available": "No classes available", "No comments yet": "No comments yet", "No Conversations": "No Conversations", "No custom fields added": "No custom fields added", "No data": "No data", "No Data": "No Data", "No data available": "No data available", "No data for printing": "No data for printing", "No data found": "No data found", "No data present": "No data present", "No description": "No description", "No Description": "No Description", "No description added": "No description added", "No details": "No details", "No document available": "No document available", "No due date": "No due date", "No duplicates": "No duplicates", "No entries are available to be submitted": "No entries are available to be submitted", "No entry available yet": "No entry available yet", "No feature types available": "No feature types available", "No features in given range": "No features in given range", "No fields selected": "No fields selected", "No Files": "No Files", "No folders": "No folders", "No folders found": "No folders found", "No Forms": "No Forms", "No forms available": "No forms available", "No forms found": "No forms found", "no image": "no image", "No instances available": "No instances available", "No Invitation": "No Invitation", "No item types added": "No item types added", "No Items": "No Items", "No items added yet": "No items added yet", "No layers available": "No layers available", "No Limit": "No Limit", "No location found": "No location found", "No maps": "No maps", "No maps found": "No maps found", "No matches": "No matches", "No members": "No members", "No Members": "No Members", "No Members Found": "No Members Found", "No Models": "No Models", "No of defects": "No of defects", "No operation": "No operation", "No options found": "No options found", "No permission": "No permission", "No permission to create a report": "No permission to create a report", "No permissions": "No permissions", "No Permissions": "No Permissions", "No Plans": "No Plans", "No Plans available": "No Plans available", "No progress updates available": "No progress updates available", "No projects available": "No projects available", "No properties available": "No properties available", "No published templates found": "No published templates found", "No report found": "No report found", "No report yet": "No report yet", "No resources found.": "No resources found.", "No Responses": "No Responses", "No Results": "No Results", "No results found": "No results found", "No results found for": "No results found for", "No Roles": "No Roles", "No roles assigned": "No roles assigned", "No Schedules": "No Schedules", "No section yet": "No section yet", "No simulation found": "No simulation found", "No stage": "No stage", "No stock available for the item": "No stock available for the item", "No stock information found": "No stock information found", "No stock statuses added": "No stock statuses added", "No Submissions": "No Submissions", "No sync history": "No sync history", "No tags": "No tags", "No Tags Found": "No Tags Found", "No tasks available": "No tasks available", "No tasks found": "No tasks found", "No Teams": "No Teams", "No teams assigned": "No teams assigned", "No template found": "No template found", "No Templates": "No Templates", "No transaction types added": "No transaction types added", "No transactions": "No transactions", "No transactions found": "No transactions found", "No transactions found in the last 7 days": "No transactions found in the last 7 days", "No vendors added": "No vendors added", "No version added for the document": "No version added for the document", "No warehouse types added": "No warehouse types added", "No Warehouses": "No Warehouses", "No warehouses added yet": "No warehouses added yet", "No Widgets": "No Widgets", "No work is done": "No work is done", "No workflow available for the selected features": "No workflow available for the selected features", "No workflows are present": "No workflows are present", "No Workflows Yet": "No Workflows Yet", "no-comments": "You can see all your comments list here.", "no-data-asset-settings": "It looks like you haven't added any fields yet.", "no-data-assets-list": "All of your assets will be listed here. You can add, delete and edit individual assets, as well as record any information for each asset.", "no-data-attachments": "It looks like you haven't added any attachments yet.", "no-data-categories": "It looks like you haven't created any categories yet.", "no-data-checklists": "Add checklists items either manually or pasting from your existing Excel templates to seamlessly breakdown and track the task progress", "no-data-comments": "Communicate and collaborate in real-time with assignees and other team members using comments.", "no-data-conversations": "You can see all your conversations listed here once you start conversing on tasks.", "no-data-dashboard-print": "There are no widgets that support printing in this dashboard.", "no-data-dashboard-widgets": "You have no report to show on the dashboard yet.", "no-data-enable-workflows": "Enable the workflows by clicking on the enable button to access them.", "no-data-entries": "It looks like there are no entries in the sections yet. Please create the entries to get started.", "no-data-fam": "Sorry, there are no results for this filters, please try another phrase.", "no-data-fam-gallery": "There are no attachments recorded yet.", "no-data-files": "There are no files to display here", "no-data-folders": "There are no folders to display here", "no-data-forms": "It looks like there are no forms to display here. Create forms, assign your team members to collect and manage data seamlessly.", "no-data-forms-responses": "Looks like there are no responses to display here.", "no-data-forms-submissions": "Looks like you haven't submitted any forms yet.", "no-data-forms-template-builder": "Please drag and drop fields from the right panel to start building form blocks.", "no-data-forms-template-details": "It looks like there are no forms to display here. Create forms, assign your team members to collect and manage data seamlessly.", "no-data-forms-templates-drafts": "Save your draft to continue with it later.", "no-data-forms-templates-public": "Manage data more easily with forms templates. Create form templates, add blocks, and customise workflows to suit all your project needs.", "no-data-forms-templates-published": "Manage data more easily with forms templates. Create form templates, add blocks, and customise workflows to suit all your project needs.", "no-data-inventory-assets-items": "Start adding items to your asset. You can add items manually, import a file, or register from existing organization items to this asset.", "no-data-inventory-bom": "You'll see items, their quantities, scope, and status listed here once your BOM is set up.", "no-data-inventory-history": "It looks like you haven't created any transactions.", "no-data-inventory-items": "The material master is currently empty. Once items are added, they will be listed here.", "no-data-inventory-report": "Start creating reports like delivery report, installation report etc.", "no-data-inventory-transactions": "It looks like you haven't created any transactions.", "no-data-inventory-warehouses": "Your warehouses will appear here once added.", "no-data-invitations": "It looks like there are no pending invitations.", "no-data-map-no-members": "This map is not shared with anyone yet. Add members to give access to your team members.", "no-data-no-workflows": "Add a new workflow template to automate your daily tasks.", "no-data-plans": "It looks like there are no plans to display here. <PERSON>reate plans to keep your team in sync and avoid any miscommunication.", "no-data-project-management": "It looks like there are no schedules to display here. Create a schedule to start tracking the health of your project.", "no-data-roles": "It looks like you haven't created any roles yet.", "no-data-sections": "It looks like there are no sections yet. You can get started by creating new section.", "no-data-system-model": "Looks like there are no models built to display here.", "no-data-tags": "It looks like you haven't created any tags yet.", "no-data-tasks": "It looks like there are no tasks to display here. You can create, update, assign, and track your tasks here.", "no-data-tasks-list": "It looks like there are no tasks to display here. You can create, update, assign, and track your tasks here.", "no-data-teams": "It looks like you haven't created any teams yet.", "no-data-terra": "It looks like there are no maps to display here.", "no-data-terra-scan": "Seamlessly track and monitor your scanning progress.", "no-data-therm-map": "It looks like there are no maps to display here.", "no-data-therm-simulation": "You can see your C&C simulation here. You can see default section like plant information, asset details etc", "no-data-users": "No members were added to your organization yet.", "no-data-workflow-no-permission": "Sorry, you don't have access. Send request to access the workflows.", "no-permission-text-files": "You don't have access to create files. Request access from your administrator.", "no-permission-text-forms": "You don't have access to create forms. Request access from your administrator.", "no-permission-text-inventory-items": "You don't have access to create inventory. Request access from your administrator.", "no-permission-text-plans": "You don't have access to create plans. Request access from your administrator.", "no-permission-text-project-management": "You don't have access to create project management. Request access from your administrator.", "no-permission-text-tasks": "You don't have access to create tasks. Request access from your administrator.", "No. members to respond": "No. members to respond", "No. of defects": "No. of defects", "Non-Critical": "Non-Critical", "None": "None", "Not a unique number": "Not a unique number", "Not all fields are mandatory. You can select few fields based on scenario.": "Not all fields are mandatory. You can select few fields based on scenario.", "Not associated with defects": "Not associated with defects", "Not available": "Not available", "Not blank": "Not blank", "not equal to": "not equal to", "Not set": "Not set", "Not started": "Not started", "Not Started": "Not Started", "Not submitted": "Not submitted", "Not verified": "Not verified", "note": "Note", "Note": "Note", "Note : You are marking the document as submitted. Please upload a document and state the reason for marking it as submitted.": "Note : You are marking the document as submitted. Please upload a document and state the reason for marking it as submitted.", "Note: 30 days free trial and then $25/month starting from": "Note: 30 days free trial and then $25/month starting from", "Note: Please save your progress to be able to add assignees": "Note: Please save your progress to be able to add assignees", "Note: Please save your progress to be able to add assignees.": "Note: Please save your progress to be able to add assignees.", "Note: Please save your progress to be able to add attachments, automations, and references.": "Note: Please save your progress to be able to add attachments, automations, and references.", "Note: Please save your progress to be able to add attachments.": "Note: Please save your progress to be able to add attachments.", "Note: Please save your progress to be able to add resources.": "Note: Please save your progress to be able to add resources.", "Notes": "Notes", "Nothing to show": "Nothing to show", "Notification related to assigned tasks, forms and @mentions": "Notification related to assigned tasks, forms and @mentions", "notification sent": "notification sent", "Notifications": "Notifications", "Notifications are not enabled for this site": "Notifications are not enabled for this site", "Notifications will be sent while the task is in the selected statuses": "Notifications will be sent while the task is in the selected statuses", "Notify": "Notify", "Notify team members": "Notify team members", "Nov": "Nov", "November": "November", "number": "number", "Number": "Number", "Number chart": "Number chart", "Number format": "Number format", "Number of days for reviewers/approvers to respond": "Number of days for reviewers/approvers to respond", "Number of days for reviewers/approvers to respond after the submission": "Number of days for reviewers/approvers to respond after the submission", "Number of days to finish the form": "Number of days to finish the form", "Number of days to respond after submission": "Number of days to respond after submission", "Number of days to submit the form": "Number of days to submit the form", "Number of days to take action": "Number of days to take action", "Number of days to wait after finishing the activity. Use -ve for setting lead.": "Number of days to wait after finishing the activity. Use -ve for setting lead.", "Number of members required to approve before proceeding.": "Number of members required to approve before proceeding.", "Number of seconds to wait for thumbnails before generating the document.": "Number of seconds to wait for thumbnails before generating the document.", "Number tag is mandatory": "Number tag is mandatory", "O&M Approver": "O&M Approver", "Oct": "Oct", "October": "October", "of": "of", "of scheduled": "of scheduled", "of total": "of total", "Offloading Quantity": "Offloading Quantity", "OK": "OK", "Ok, got it": "Ok, got it", "Okay": "Okay", "Oldest first": "Oldest first", "on": "on", "On days": "On days", "On pace": "On pace", "On particular dates": "On particular dates", "On Schedule": "On Schedule", "On the": "On the", "On track": "On track", "On Track": "On Track", "onboarding-cnc-content-description-four": "Summarize Commissioning & Certification status with custom fields, checklists, and documents", "onboarding-cnc-content-description-one": "Retrieve plant and equipment details along with documents effortlessly from the SDP.", "onboarding-cnc-content-description-three": "Invite members to provide collective inputs & review activities fostering seamless teamwork and improved collaboration.", "onboarding-cnc-content-description-two": "Track members' activities in real-time during commissioning and certification through intuitive dashboards.", "onboarding-cnc-content-header": "Here's what you can do with #C&C", "onboarding-cnc-content-title-four": "Generate Comprehensive Report", "onboarding-cnc-content-title-one": "Auto-fetch data from SDP", "onboarding-cnc-content-title-three": "Invite and Collaborate", "onboarding-cnc-content-title-two": "Track Progress", "onboarding-cnc-description": "Eliminate the manual paperwork and data collection during plant commissioning or handover. Streamline and organize the documents and activity lists for enhanced efficiency, and generate tailored, comprehensive reports.", "onboarding-cnc-title": "Welcome to Commissioning & Certification!", "onboarding-component-master-content-header": "What you can do with #Component Master", "onboarding-component-master-description": "Streamline site management with Observations & Punchlists. Collaborate in real-time and receive notifications for efficient project coordination and analytics.", "onboarding-component-master-description-four": "Capture, annotate and attach any images, documents, data sheets, create dashboards, etc.", "onboarding-component-master-description-one": "Create, assign, schedule and prioritise your site activities with ease.", "onboarding-component-master-description-three": "Create and initiate maintenance schedules for different assets", "onboarding-component-master-description-two": "Get global material information of all the assets", "onboarding-component-master-title": "Welcome to Component Master!", "onboarding-component-master-title-four": "Analytics & Reporting", "onboarding-component-master-title-one": "Categories & Components", "onboarding-component-master-title-three": "Maintenance schedule", "onboarding-component-master-title-two": "Materials information", "onboarding-files-content-description-four": "Update documents in real-time to ensure all team members stay aligned and every version is recorded.", "onboarding-files-content-description-one": "Seamlessly upload and access your documents, whether you're working in the field or in the office.", "onboarding-files-content-description-three": "Effortlessly organize folders and documents for easy retrieval when needed.", "onboarding-files-content-description-two": "Ensure you always have the latest documents readily available at all times.", "onboarding-files-content-header": "Here's what you can do with #files", "onboarding-files-content-title-four": "Version Control", "onboarding-files-content-title-one": "Upload from anywhere", "onboarding-files-content-title-three": "Stay Organised", "onboarding-files-content-title-two": "Stay up to date", "onboarding-files-description": "Effortlessly organize folders and documents for easy retrieval and enhanced productivity. Simplify your document management process today!", "onboarding-files-title": "Your #File list will appear here", "onboarding-forms-content-description-four": "Design customizable forms to meet your needs, collecting comprehensive data and managing workflows effectively.", "onboarding-forms-content-description-one": "Create templates and forms using our wide range of fields that enable you to collect all types of data and cover all your flows and activities.", "onboarding-forms-content-description-three": "Create tailored analytics and share with team members to make better decisions.", "onboarding-forms-content-description-two": "Customize workflows to meet your specific needs, enabling efficient management of repetitive field processes.", "onboarding-forms-content-header": "Here's what you can do with #forms", "onboarding-forms-content-title-four": "Customizable Data Form", "onboarding-forms-content-title-one": "Digitalize paper forms", "onboarding-forms-content-title-three": "Analyse", "onboarding-forms-content-title-two": "Workflows & Approvals", "onboarding-forms-description": "Go paperless and streamline data collection with our digital forms. Efficiently manage field processes with customizable workflows and approvals, tailored to your specific requirements.", "onboarding-forms-title": "Your #Forms list will appear here", "onboarding-inventory-items-content-description-four": "Manage your inventory, track different items, and make sure your stock levels are monitored.", "onboarding-inventory-items-content-description-one": "Improve tracking with unique serial and pallet numbers, enabling comprehensive item monitoring and easy access to item details.", "onboarding-inventory-items-content-description-three": "Create custom transactions to fit all your workflows and custom fields to record all required information.", "onboarding-inventory-items-content-description-two": "Our barcode scanners ensure fast & accurate stocktaking with their durability & reliability, providing precise counts anytime.", "onboarding-inventory-items-content-header": "Here's what you can do with #inventory", "onboarding-inventory-items-content-title-four": "BOM & Analytics", "onboarding-inventory-items-content-title-one": "Material Master", "onboarding-inventory-items-content-title-three": "Track serial numbers", "onboarding-inventory-items-content-title-two": "Receive, Issue, Transfer...", "onboarding-inventory-items-description": "Keep track of your material master, warehouses, stock, all your transactions, track serial and pallet box numbers, and gain valuable insights from out inventory reports and dashboards.", "onboarding-inventory-items-title": "Your #inventory list will appear here", "onboarding-plans-content-description-four": "Retrieve the latest drawings on any device, wherever and whenever you need, right at your fingertips.", "onboarding-plans-content-description-one": "Consolidate your architectural, civil, electrical, and other drawings in a single location.", "onboarding-plans-content-description-three": "Annotate, measure, comment, and collaborate live. Enhance efficiency and boost your productivity.", "onboarding-plans-content-description-two": "Consistently remain updated with the most recent drawing versions. Evaluate and monitor differences between revisions anytime.", "onboarding-plans-content-header": "Here's what you can do with #plans", "onboarding-plans-content-title-four": "Access anywhere", "onboarding-plans-content-title-one": "Design and Drawings", "onboarding-plans-content-title-three": "Realtime collaboration", "onboarding-plans-content-title-two": "Version Control", "onboarding-plans-description": "Centralize and collaborate on architectural, civil, electrical, and other drawings in real-time. Stay up to date and access drawings anytime, anywhere, on any device for enhanced productivity.", "onboarding-plans-title": "Your #Plans list will appear here", "onboarding-project-management-content-description-four": "Generate S-curve, activity breakdown, analyse critical path and get an in-depth understanding and share/export reports.", "onboarding-project-management-content-description-one": "Integrate your timetable with Primavera P6 and Microsoft Projects for effortless version management and sharing.", "onboarding-project-management-content-description-three": "Effortlessly integrate, update, and share schedules, ensuring smooth teamwork and enhanced group collaboration.", "onboarding-project-management-content-description-two": "Stay on schedule and within budget by managing directly from the site through our mobile application.", "onboarding-project-management-content-header": "Here's what you can do with #Project management", "onboarding-project-management-content-title-four": "Reports and Analytics", "onboarding-project-management-content-title-one": "Manage Project Schedule", "onboarding-project-management-content-title-three": "Centralise and Collaborate", "onboarding-project-management-content-title-two": "Track Progress Updates", "onboarding-project-management-description": "Seamlessly integrate and manage project schedules from Primavera P6 and Microsoft Projects. Track progress in real-time and access detailed analytics for informed decision-making.", "onboarding-project-management-title": "Your #Project schedules will appear here", "onboarding-tasks-content-description-four": "Capture, annotate and attach any images, documents, data sheets, create dashboards, etc.", "onboarding-tasks-content-description-one": "Create, assign, schedule and prioritise your site activities with ease.", "onboarding-tasks-content-description-three": "Stay connected & ensure effective communication through email and push notifications.", "onboarding-tasks-content-description-two": "Update tasks in real-time to keep team members on track work together more effectively.", "onboarding-tasks-content-header": "What you can do with #tasks", "onboarding-tasks-content-title-four": "Analytics & Reporting", "onboarding-tasks-content-title-one": "Observations & Punchlists", "onboarding-tasks-content-title-three": "Notifications", "onboarding-tasks-content-title-two": "Collaborate in realtime", "onboarding-tasks-description": "Streamline site management with Observations & Punchlists. Collaborate in real-time and receive notifications for efficient project coordination and analytics.", "onboarding-tasks-list-content-description-four": "Capture, annotate and attach any images, documents, data sheets, create dashboards, etc.", "onboarding-tasks-list-content-description-one": "Create, assign, schedule and prioritise your site activities with ease.", "onboarding-tasks-list-content-description-three": "Stay connected & ensure effective communication by receiving email or push notifications.", "onboarding-tasks-list-content-description-two": "Update tasks in real-time to keep team members on track and better collaboration.", "onboarding-tasks-list-content-header": "What you can do with #tasks", "onboarding-tasks-list-content-title-four": "Analytics & Reporting", "onboarding-tasks-list-content-title-one": "Observations & Punchlists", "onboarding-tasks-list-content-title-three": "Receive notifications", "onboarding-tasks-list-content-title-two": "Collaborate in realtime", "onboarding-tasks-list-description": "Create and manage tasks, stay on top of work with notifications, and keep your team in sync.", "onboarding-tasks-list-title": "Your #tasks list will appear here", "onboarding-tasks-title": "Your #tasks list will appear here", "onboarding-terra-charts-content-description-eight": "Comprehensive comparison of planned vs. actual progress with detailed insights.", "onboarding-terra-charts-content-description-five": "Compare progress over specific time frames for insightful trend analysis.", "onboarding-terra-charts-content-description-four": "Break down construction into detailed block-level activities, focusing on specific areas.", "onboarding-terra-charts-content-description-one": "Get a project-wide snapshot for quick assessment of overall construction progress", "onboarding-terra-charts-content-description-seven": "Compare key metrics such as work rate, units installed, catch-up rate, efficiency.", "onboarding-terra-charts-content-description-six": "Visualize construction progress intensity with Heat Maps for strategic decision-making.", "onboarding-terra-charts-content-description-three": "Keep projects on track by identifying areas that are ahead, behind or on track.", "onboarding-terra-charts-content-description-two": "Monitor construction pace for efficient resource allocation and improved project efficiency.", "onboarding-terra-charts-content-header": "Here's what you can do with #dashboard", "onboarding-terra-charts-content-title-eight": "Progress analysis", "onboarding-terra-charts-content-title-five": "Progress comparison", "onboarding-terra-charts-content-title-four": "Block level reporting", "onboarding-terra-charts-content-title-one": "Overall Progress", "onboarding-terra-charts-content-title-seven": "Track KPIs", "onboarding-terra-charts-content-title-six": "Block vs Activity map", "onboarding-terra-charts-content-title-three": "Status reports", "onboarding-terra-charts-content-title-two": "Work rate monitoring", "onboarding-terra-charts-description": "Seamlessly track and monitor your construction progress.", "onboarding-terra-charts-title": "Your #dashboard will appear here", "onboarding-terra-content-header": "Here's what you can do with #Terra", "onboarding-terra-description": "Seamlessly integrate and manage project schedules from Primavera P6 and Microsoft Projects. Track progress in real-time and access detailed analytics for informed decision-making.", "onboarding-terra-scan-description": "Seamlessly track and monitor your scanning progress.", "onboarding-terra-scan-title": "Your #dashboard for scan will appear here", "Once": "Once", "One of ": "One of ", "One of Yes, No, NA": "One of Yes, No, NA", "One or more subtasks do not have valid weights. The weights will be equally distributed among the subtasks if not specified.": "One or more subtasks do not have valid weights. The weights will be equally distributed among the subtasks if not specified.", "One/more comma separated values -  ": "One/more comma separated values -  ", "One/more folders or files do not have weights.": "One/more folders or files do not have weights.", "ongoing submissions will be affected by deleting this block.": "ongoing submissions will be affected by deleting this block.", "Only": "Only", "Only accessible to the users permitted in the DMS": "Only accessible to the users permitted in the DMS", "Only connected user can share the account": "Only connected user can share the account", "Only letters are allowed": "Only letters are allowed", "Only people with access can view this folder and its subfolders": "Only people with access can view this folder and its subfolders", "Only selected members can access": "Only selected members can access", "Only show activities that have both their planned start and planned finish dates within the selected range.": "Only show activities that have both their planned start and planned finish dates within the selected range.", "open": "open", "Open": "Open", "Open data table": "Open data table", "Open now": "Open now", "Open teams are visible and can be joined by any team member from this domain list": "Open teams are visible and can be joined by any team member from this domain list", "Open/Unlock Request": "Open/Unlock Request", "Open/Unlock response": "Open/Unlock response", "Opening stock": "Opening stock", "Opening Stock": "Opening Stock", "Operation": "Operation", "Operation type": "Operation type", "Operator": "Operator", "option": "option", "Optional": "Optional", "Options": "Options", "or": "or", "Or": "Or", "OR": "OR", "or drag and drop": "or drag and drop", "Orange": "Orange", "Order details": "Order details", "Order Details": "Order Details", "Ordered": "Ordered", "Ordered List": "Ordered List", "Ordered quantity": "Ordered quantity", "Organization": "Organization", "Organization and account administration related permissions": "Organization and account administration related permissions", "Organization Name": "Organization Name", "Organization updated successfully!": "Organization updated successfully!", "Organize and manage groups of users to streamline access, collaboration and operations within the asset.": "Organize and manage groups of users to streamline access, collaboration and operations within the asset.", "Organize and manage groups of users to streamline access, collaboration and operations within your organization.": "Organize and manage groups of users to streamline access, collaboration and operations within your organization.", "Orientation": "Orientation", "Other details": "Other details", "Other images": "Other images", "others": "others", "Others": "Others", "out of": "out of", "Out of stock": "Out of stock", "Out of Stock": "Out of Stock", "Outcome strategy": "Outcome strategy", "Outdent": "Outdent", "Outgoing": "Outgoing", "Outline color": "Outline color", "Outline opacity": "Outline opacity", "Outline/Fill color": "Outline/Fill color", "Output value": "Output value", "Overall progress": "Overall progress", "Overall summary": "Overall summary", "Overall summary of CnC module": "Overall summary of CnC module", "Overall Weight": "Overall Weight", "Overdue": "Overdue", "Overdue tasks": "Overdue tasks", "Override actuals": "Override actuals", "Overview": "Overview", "Overview view": "Overview view", "Owner": "Owner", "Owner successfully transferred": "Owner successfully transferred", "Page": "Page", "Page not found": "Page not found", "Palette": "Palette", "Pallet No": "Pallet No", "Pallet Number": "<PERSON><PERSON><PERSON> Number", "Pallet numbers": "Pallet numbers", "Pallet numbers tracking can only be set up at the time of item creation. You will not be able to alter this setting later.": "Pallet numbers tracking can only be set up at the time of item creation. You will not be able to alter this setting later.", "Pan": "Pan", "Paragraph": "Paragraph", "Parameter": "Parameter", "Parameter Name": "Parameter Name", "Parameters": "Parameters", "Parent": "Parent", "Parent Instance": "Parent Instance", "Parents": "Parents", "Parsing your data...": "Parsing your data...", "Password updated successfully": "Password updated successfully", "Past": "Past", "Past 1 day": "Past 1 day", "Past 1 hour": "Past 1 hour", "Past 1 month": "Past 1 month", "Past 1 week": "Past 1 week", "Past 15 minutes": "Past 15 minutes", "Past 2 weeks": "Past 2 weeks", "Past 30 days": "Past 30 days", "Past 30 minutes": "Past 30 minutes", "Paste": "Paste", "Paste data": "Paste data", "Paste items here": "Paste items here", "Paste the data copied from": "Paste the data copied from", "Pause Notification": "Pause Notification", "paused the file from the transmittal": "paused the file from the transmittal", "paused the file in the transmittal": "paused the file in the transmittal", "Payment method": "Payment method", "pcs": "pcs", "Peak watt Capacity": "Peak watt Capacity", "Pending": "Pending", "Pending & Delayed": "Pending & Delayed", "Pending approvals": "Pending approvals", "Pending review": "Pending review", "Pending submission": "Pending submission", "Pending upload": "Pending upload", "People with access": "People with access", "per hour": "per hour", "Per hour": "Per hour", "per item": "per item", "Per item": "Per item", "per page": "per page", "Percentage": "Percentage", "Percentage Completion": "Percentage Completion", "percentages": "percentages", "Percentages": "Percentages", "Permanent deletion": "Permanent deletion", "Permission": "Permission", "Permission matrix": "Permission matrix", "Permissions": "Permissions", "Permissions for users": "Permissions for users", "Permissions related to assets that the user is added to": "Permissions related to assets that the user is added to", "Permissions summary": "Permissions summary", "Permits": "Permits", "person who designs, builds, or maintains engines, machines, or structures.": "person who designs, builds, or maintains engines, machines, or structures.", "Personalize your document information with custom fields": "Personalize your document information with custom fields", "Personalize your transmittal information with custom fields": "Personalize your transmittal information with custom fields", "Phone": "Phone", "Phone number": "Phone number", "Phone Number": "Phone Number", "Pick Date": "Pick Date", "Pick date & time": "Pick date & time", "Pick Time": "Pick Time", "Pie chart": "Pie chart", "Pie Chart": "Pie Chart", "Pie chart representing the proportions of forms by their statuses for a specific template and matching the filtering criteria.": "Pie chart representing the proportions of forms by their statuses for a specific template and matching the filtering criteria.", "piles": "piles", "Pin dashboard": "Pin dashboard", "Pink": "Pink", "Pivot table": "Pivot table", "Placeholder": "Placeholder", "Plan": "Plan", "Plan your restock": "Plan your restock", "Planned": "Planned", "Planned actual": "Planned actual", "Planned cost": "Planned cost", "Planned Cost": "Planned Cost", "Planned duration": "Planned duration", "Planned Duration": "Planned Duration", "planned finish": "planned finish", "Planned finish": "Planned finish", "Planned Finish": "Planned Finish", "planned finish date": "planned finish date", "Planned finish date": "Planned finish date", "Planned progress": "Planned progress", "planned start": "planned start", "Planned start": "Planned start", "Planned Start": "Planned Start", "planned start date": "planned start date", "Planned start date": "Planned start date", "Planned Value": "Planned Value", "Planned vs Actual": "Planned vs Actual", "Planned Work": "Planned Work", "Planned/Actual": "Planned/Actual", "Plans": "Plans", "Plant name": "Plant name", "Please add prefix to all issue purposes": "Please add prefix to all issue purposes", "Please choose one option for recipients": "Please choose one option for recipients", "Please click download only to proceed further": "Please click download only to proceed further", "Please contact your administrator for access.": "Please contact your administrator for access.", "Please create/fill the form to continue to the next step": "Please create/fill the form to continue to the next step", "Please ensure the selected form has field value required to calculate the formula": "Please ensure the selected form has field value required to calculate the formula", "Please enter a value": "Please enter a value", "Please enter data for at least one field to ,submit the form.": "Please enter data for at least one field to submit the form.", "Please enter data for at least one field to submit the form.": "Please enter data for at least one field to submit the form.", "Please enter the 4-digit code sent to your email.": "Please enter the 4-digit code sent to your email.", "Please enter the 8-digit code sent to your email.": "Please enter the 8-digit code sent to your email.", "Please fill all the mandatory data": "Please fill all the mandatory data", "Please fill the form to continue to the next step": "Please fill the form to continue to the next step", "Please note that TaskMapper will be offline for planned maintenance on": "Please note that TaskMapper will be offline for planned maintenance on", "Please press enter to add an email": "Please press enter to add an email", "Please publish the template to make any operations": "Please publish the template to make any operations", "Please reload the page": "Please reload the page", "Please reload the page to see the updated schedule.": "Please reload the page to see the updated schedule.", "Please remove the integration to delete them.": "Please remove the integration to delete them.", "Please save the dashboard with the uploaded template to test the print": "Please save the dashboard with the uploaded template to test the print", "Please select a value": "Please select a value", "Please select a vector to calculate it's area/distance": "Please select a vector to calculate it's area/distance", "Please select a vector to calculate it’s area/distance": "Please select a vector to calculate it’s area/distance", "Please submit the form to proceed": "Please submit the form to proceed", "Please try again": "Please try again", "Please try again later": "Please try again later.", "Please try another name": "Please try another name", "Please update the document and retry.": "Please update the document and retry.", "Please upload a document template to print the dashboard.": "Please upload a document template to print the dashboard.", "Please upload file with .xlsx extension": "Please upload file with .xlsx extension", "Please wait": "Please wait", "Plot bar and pie charts using the standard/custom fields from the schedule.": "Plot bar and pie charts using the standard/custom fields from the schedule.", "Plot values such as progress, cost and variance for selected activities in a bar or pie chart.": "Plot values such as progress, cost and variance for selected activities in a bar or pie chart.", "Plural label": "Plural label", "Plural name": "Plural name", "PM": "PM", "pm-auto-update-progress-dms-sync-text-1": "Automatically sync progress for the activity by linking it with the documents from master document list based on their statuses. You can manually sync or choose to sync at regular intervals for the whole schedule", "pm-auto-update-progress-dms-sync-text-2": "If weightages are not defined, all documents will be considered equally important i.e weightage will be equally distributed.", "pm-auto-update-progress-sync-text-1": "Automatically sync progress for the activity by linking it with the map based construction monitoring activities. You can manually sync or choose to sync at regular intervals for the whole schedule.", "pm-auto-update-progress-sync-text-2": "The data date for the update will be considered as the timestamp of the sync irrespective of the actual recorded timestamps on the map.", "pm-hawk-share-edit-description": "Can view, download, update versions, share with other members", "pm-resources-tab-description": "Invite your team members, assign costs, and manage their access effortlessly. You can also create custom resources and handle them just as easily as you do with people. Set the cost for all resources on an hourly, per-item, or fixed basis.", "pm-sync-properties-text-1": "Automatically sync information from the schedule to assets properties to access key information directly on the asset without having to go through the schedule.", "pm-sync-properties-text-2": "The data is synced real-time if there are changes to below schedule properties.", "PNG or JPEG files": "PNG or JPEG files", "PNG, JPG, PDF": "PNG, JPG, PDF", "POD": "POD", "POD-2": "POD-2", "Point": "Point", "Polygon": "Polygon", "Polyline": "Polyline", "Power Loss Estimate": "Power Loss Estimate", "predecessor": "predecessor", "Predecessor": "Predecessor", "Predecessors": "Predecessors", "Preferences": "Preferences", "Prefix": "Prefix", "Prefix feature name": "Prefix feature name", "Prefix is required": "Prefix is required", "Prettify units": "Prettify units", "Prettify values": "Prettify values", "Preview": "Preview", "Preview and Publish": "Preview and Publish", "Preview for": "Preview for", "Preview not supported": "Preview not supported", "Preview/Publish": "Preview/Publish", "Previous": "Previous", "Previous cell": "Previous cell", "Previous revisions": "Previous revisions", "Previous Versions": "Previous Versions", "Previous week": "Previous week", "Pricing plans": "Pricing plans", "Print": "Print", "Print dashboard": "Print dashboard", "Print template": "Print template", "Priority": "Priority", "Privacy Policy": "Privacy Policy", "Proceed": "Proceed", "Processing file": "Processing file", "Profile Photo": "Profile Photo", "Profile picture": "Profile picture", "Profile Picture": "Profile Picture", "Profile Settings": "Profile Settings", "Profile updated successfully": "Profile updated successfully", "Progress": "Progress", "Progress data": "Progress data", "Progress history": "Progress history", "Progress report exported successfully": "Progress report exported successfully", "Progress Type": "Progress Type", "Progress updated": "Progress updated", "Progress updated successfully": "Progress updated successfully", "Progress updates": "Progress updates", "Progress would be lost, are you sure?": "Progress would be lost, are you sure?", "Project": "Project", "Project duration": "Project duration", "Project Id": "Project Id", "Project management": "Project management", "Project name": "Project name", "Project Outputs": "Project Outputs", "Project progress": "Project progress", "Projects": "Projects", "Projects List": "Projects List", "Properties": "Properties", "Property": "Property", "Public": "Public", "Publish": "Publish", "Publish report": "Publish report", "Publish Report": "Publish Report", "Publish schedule to view metrics": "Publish schedule to view metrics", "Publish the form to see the dashboard": "Publish the form to see the dashboard", "Publish to make progress updates": "Publish to make progress updates", "Published": "Published", "Published successfully": "Published successfully", "published the document": "published the document", "published the document ": "published the document ", "published the form": "published the form", "published the transmittal": "published the transmittal", "Punch lists": "Punch lists", "Purchase order": "Purchase order", "Purple": "Purple", "PV module manufacturer": "PV module manufacturer", "px": "px", "QA/QC - Inspections": "QA/QC - Inspections", "Quantity": "Quantity", "Quantity label": "Quantity label", "Quarter": "Quarter", "Quarter to date": "Quarter to date", "Quarterly": "Quarterly", "Quarters": "Quarters", "Quaterly": "Quaterly", "Quick Access": "Quick Access", "Quick filters": "Quick filters", "Quickly see the status of defects with visual icons. Easy decision-making and better organization for your container.": "Quickly see the status of defects with visual icons. Easy decision-making and better organization for your container.", "Racking type": "Racking type", "Radians": "Radians", "Radio": "Radio", "Range": "Range", "Raw images": "Raw images", "Raw Images": "Raw Images", "Re-assign members to the form and then notify": "Re-assign members to the form and then notify", "Reactivate Subscription": "Reactivate Subscription", "Read submissions": "Read submissions", "Ready to close": "Ready to close", "Real-time collaboration": "Real-time collaboration", "reason": "Reason", "Reason": "Reason", "Reason for cancellation": "Reason for cancellation", "Reason for reopen (required)": "Reason for reopen (required)", "Reason for rollback": "Reason for rollback", "Reason for rollback (required)": "Reason for rollback (required)", "Reassign": "Reassign", "Reassign ownership to delegate full access to another user": "Reassign ownership to delegate full access to another user", "Reassign to": "Reassign to", "Reassign with another member": "Reassign with another member", "Receive notifications directly on my device": "Receive notifications directly on my device", "Receive notifications when you're running out of stock.": "Receive notifications when you're running out of stock.", "Receive stock alerts": "Receive stock alerts", "Received by": "Received by", "Recent Transactions": "Recent Transactions", "Recipients": "Recipients", "Reconnect": "Reconnect", "Record at least duration, the amount spent or work done for the selected resource.": "Record at least duration, the amount spent or work done for the selected resource.", "Record of all the incoming shipments": "Record of all the incoming shipments", "recovered": "recovered", "Rectangle": "Rectangle", "Red": "Red", "Redo": "Redo", "Reference": "Reference", "References": "References", "Refresh to see new attachments": "Refresh to see new attachments", "Regenerate link": "Regenerate link", "Register item": "Register item", "Register items": "Register items", "Registry": "Registry", "Reimport and merge": "Reimport and merge", "reject": "Reject", "Reject": "Reject", "Rejected": "Rejected", "rejected the document": "rejected the document", "Relation": "Relation", "Relation type": "Relation type", "Reload": "Reload", "Reload Preview": "Reload Preview", "remaining": "remaining", "Remaining": "Remaining", "Remaining affected capacity": "Remaining affected capacity", "Remaining cost": "Remaining cost", "Remaining Cost": "Remaining Cost", "Remaining defects": "Remaining defects", "Remaining duration": "Remaining duration", "Remaining Duration": "Remaining Duration", "Remaining quantity": "Remaining quantity", "Remaining Schedule Duration": "Remaining Schedule Duration", "Remaining Work": "Remaining Work", "Remarks": "Remarks", "Remarks and attachments": "Remarks and attachments", "Remind": "Remind", "Remind every": "Remind every", "Reminder": "Reminder", "Reminder added successfully": "<PERSON><PERSON><PERSON> added successfully", "Reminder deleted successfully": "<PERSON><PERSON><PERSON> deleted successfully", "Reminder updated successfully": "Reminder updated successfully", "Reminders": "Reminders", "Reminders can be configured only when a due date is added to a task, and the task must be in either the 'Pending' or 'In Progress' status": "Reminders can be configured only when a due date is added to a task, and the task must be in either the 'Pending' or 'In Progress' status", "Removals": "Removals", "Remove": "Remove", "Remove Access": "Remove Access", "Remove account": "Remove account", "Remove column": "Remove column", "Remove integration": "Remove integration", "Remove row": "Remove row", "Remove Scheduled Cancellation": "Remove Scheduled Cancellation", "Remove tracking": "Remove tracking", "Remove user": "Remove user", "removed": "removed", "removed assignees from the form": "removed assignees from the form", "removed members from the form": "removed members from the form", "removed members from the template": "removed members from the template", "removed the category": "removed the category", "removed the due date": "removed the due date", "removed the location": "removed the location", "removed the members": "removed the members", "removed the start date": "removed the start date", "removed the tags": "removed the tags", "Rename": "<PERSON><PERSON>", "Rename Checklist": "Rename Checklist", "Rename field": "Rename field", "Rename Template": "<PERSON><PERSON>", "Rename this layer": "Rename this layer", "renamed the document": "renamed the document", "renamed the file": "renamed the file", "renamed the file to": "renamed the file to", "renamed the folder": "renamed the folder", "renamed the folder to": "renamed the folder to", "renamed the form": "renamed the form", "renamed the template": "renamed the template", "renamed the version": "renamed the version", "Render values on the chart for each component": "Render values on the chart for each component", "reopen": "Reopen", "Reopen for review": "Reopen for review", "Reopen to": "Reopen to", "reopened": "reopened", "reopened the ": "reopened the ", "reopened the form on": "reopened the form on", "Reorder point": "Reorder point", "Repeat": "Repeat", "Repeat for": "Repeat for", "Repeat forever": "Repeat forever", "Repeat until": "Repeat until", "Replace": "Replace", "Replace and reassign a reviewer with another member": "Replace and reassign a reviewer with another member", "Replace common defects properties with tasks data such as status, priority, assignees, tags, etc. Use this for legacy containers where users have used tasks for tracking defects.": "Replace common defects properties with tasks data such as status, priority, assignees, tags, etc. Use this for legacy containers where users have used tasks for tracking defects.", "Replace members": "Replace members", "Replace team with its members in the display options.": "Replace team with its members in the display options.", "Replace with": "Replace with", "Replace with another member": "Replace with another member", "replies": "replies", "Reply": "Reply", "Report configuration": "Report configuration", "Report deleted successfully": "Report deleted successfully", "Report Exported": "Report Exported", "Report published successfully": "Report published successfully", "Report Updated successfully": "Report Updated successfully", "Reports": "Reports", "Repository": "Repository", "Represent a number that is aggregated across all matching forms for the selected field with comparison trends.": "Represent a number that is aggregated across all matching forms for the selected field with comparison trends.", "Representation": "Representation", "Req number": "Req number", "Request": "Request", "Request Access": "Request Access", "Request Details": "Request Details", "Request documents": "Request documents", "Request for approval": "Request for approval", "Request for documents": "Request for documents", "Request for review": "Request for review", "Request for Review": "Request for Review", "Request for Upload": "Request for Upload", "Request No": "Request No", "Request review": "Request review", "request to view": "request to view", "Request Type": "Request Type", "requested review for the document  from ": "requested review for the document  from ", "requested you to review the document by": "requested you to review the document by", "Requester": "Requester", "Requesting": "Requesting", "Requests": "Requests", "Require all members to approve": "Require all members to approve", "Require any members to approve": "Require any members to approve", "Require comments": "Require comments", "required": "required", "Required": "Required", "Required Quantity": "Required Quantity", "Required/Invalid value": "Required/Invalid value", "Resend": "Resend", "Reset": "Reset", "Reset due dates": "Reset due dates", "Reset due dates for reviewers/approvers on resubmission": "Reset due dates for reviewers/approvers on resubmission", "Reset frequency": "Reset frequency", "Reset number to 1 for each sub folder": "Reset number to 1 for each sub folder", "Reset Password": "Reset Password", "Reset progress": "Reset progress", "Reset your password": "Reset your password", "Resolve": "Resolve", "resolved": "resolved", "Resolved": "Resolved", "Resolved capacity": "Resolved capacity", "Resolved defects": "Resolved defects", "Resource": "Resource", "Resource and Budget management.": "Resource and Budget management.", "Resource converted successfully": "Resource converted successfully", "Resource deleted": "Resource deleted", "Resource is overloaded": "Resource is overloaded", "Resource name": "Resource name", "Resource name already exists in schedule": "Resource name already exists in schedule", "Resource not found": "Resource not found", "Resource overloaded": "Resource overloaded", "Resource tracking": "Resource tracking", "resource-converted-description": "The custom resource is successfully converted into a member. The effects will reflect in the schedule", "Resources": "Resources", "Resources added to this task will appear here": "Resources added to this task will appear here", "Resources overloaded": "Resources overloaded", "Resources tracking is turned off": "Resources tracking is turned off", "Resources workload": "Resources workload", "respectively": "respectively", "Responses": "Responses", "Restock Value": "Restock Value", "Restore": "Rest<PERSON>", "Restore View": "Restore View", "restored a file": "restored a file", "restored a folder": "restored a folder", "restored the file": "restored the file", "restored the folder": "restored the folder", "Restricted": "Restricted", "resumed the file from the transmittal": "resumed the file from the transmittal", "resumed the file in the transmittal": "resumed the file in the transmittal", "Retry": "Retry", "review": "Review", "Review": "Review", "Review & Approve": "Review & Approve", "Review by engineering team is required as it doesn't meet requirements": "Review by engineering team is required as it doesn't meet requirements", "Review by engineering team is required as it doesn’t meet requirements": "Review by engineering team is required as it doesn’t meet requirements", "Review Completed": "Review Completed", "Review document": "Review document", "Review new version": "Review new version", "Review status": "Review status", "Review Statuses": "Review Statuses", "Review the document by": "Review the document by", "Review the document for": "Review the document for", "Review updated successfully": "Review updated successfully", "review with a message": "review with a message", "Review/Approve": "Review/Approve", "Reviewal entry status": "Reviewal entry status", "Reviewed": "Reviewed", "Reviewed on": "Reviewed on", "reviewed the document": "reviewed the document", "reviewed the document with a message": "reviewed the document with a message", "Reviewed/Approved on": "Reviewed/Approved on", "Reviewer": "Reviewer", "Reviewer's feedback": "Reviewer's feedback", "Reviewers": "Reviewers", "Reviewers added successfully": "Reviewers added successfully", "Reviewers has not been added": "Reviewers has not been added", "Reviewers/Approvers": "Reviewers/Approvers", "Revise & resubmit": "Revise & resubmit", "Revise & Resubmit": "Revise & Resubmit", "Revisions": "Revisions", "Revisions count": "Revisions count", "Revoke": "Revoke", "revoked the request to review from ": "revoked the request to review from ", "RFIs": "RFIs", "Rich text": "Rich text", "Right": "Right", "Right to left": "Right to left", "Risk management": "Risk management", "role": "role", "Role": "Role", "Role created": "Role created", "Role created successfully": "Role created successfully", "Role deleted": "Role deleted", "Role description": "Role description", "Role details": "Role details", "Role Details more text more text": "Role Details more text more text", "Role duplicated successfully": "Role duplicated successfully", "Role name": "Role name", "Role updated": "Role updated", "Role updated successfully": "Role updated successfully", "Roles": "Roles", "rollback": "Rollback", "Rollback": "Rollback", "Rollback Block": "Rollback Block", "Rollback to": "Rollback to", "Rollback to initiator on rejection / resubmission": "Rollback to initiator on rejection / resubmission", "Rollback to previous step": "Rollback to previous step", "rolled back": "Rolled back", "Rolled back": "Rolled back", "Rolled Back": "Rolled Back", "Rolled back from": "Rolled back from", "rolledback the form on": "rolledback the form on", "Row": "Row", "Row shortcuts": "Row shortcuts", "rows": "rows", "Rules": "Rules", "S-curve (planned/ actual progress)": "S-curve (planned/ actual progress)", "S.No.": "S.No.", "Safety/EHS": "Safety/EHS", "sample template": "sample template", "Sampling should be more than 0.1": "Sampling should be more than 0.1", "Sampling(M)": "<PERSON><PERSON>(M)", "Sanitize data": "Sanitize data", "Sat": "Sat", "Satellite": "Satellite", "Saturday": "Saturday", "Save": "Save", "Save & Publish": "Save & Publish", "Save and add another": "Save and add another", "Save as draft": "Save as draft", "Save as template": "Save as template", "Save Changes": "Save Changes", "Save geometry": "Save geometry", "Save the data for future reports": "Save the data for future reports", "Save view": "Save view", "save-custom-view": "The view has changed. Do you want to save this as custom view?", "saved the details": "saved the details", "Saving Dashboard Failed": "Saving Dashboard Failed", "Scale": "Scale", "Scan": "<PERSON><PERSON>", "Scan date": "Scan date", "Scan details": "Scan details", "Scan history": "Scan history", "Scanned": "Scanned", "Schedule": "Schedule", "Schedule automatically triggered successfully": "Schedule automatically triggered successfully", "Schedule complete": "Schedule complete", "Schedule Complete": "Schedule Complete", "Schedule created successfully": "Schedule created successfully", "schedule file": "schedule file", "Schedule is loading": "Schedule is loading", "Schedule is not published": "Schedule is not published", "Schedule member": "Schedule member", "Schedule name": "Schedule name", "Schedule Performace Index (SPI)": "Schedule Performace Index (SPI)", "Schedule Performance Index (SPI)": "Schedule Performance Index (SPI)", "Schedule property": "Schedule property", "Schedule send": "Schedule send", "Schedule settings": "Schedule settings", "Schedule settings updated successfully": "Schedule settings updated successfully", "Schedule tracker": "Schedule tracker", "Schedule upload failed": "Schedule upload failed", "Schedule variance": "Schedule variance", "Schedule Variance": "Schedule Variance", "Scheduled": "Scheduled", "Scheduled progress": "Scheduled progress", "Schedules": "Schedules", "Schema": "<PERSON><PERSON><PERSON>", "Schema Inheritance": "Schema Inheritance", "Schema Preview": "Schema Preview", "Scheme": "Scheme", "Scope": "<PERSON><PERSON>", "Scope in warehouses": "Scope in warehouses", "Scope(units)": "Scope(units)", "Scoped": "<PERSON><PERSON>", "Seamlessly connect third-party applications with TaskMapper to centralize information and boost collaboration": "Seamlessly connect third-party applications with TaskMapper to centralize information and boost collaboration", "Seamlessly import your existing project schedules from Primavera P6 into TaskMapper.": "Seamlessly import your existing project schedules from Primavera P6 into TaskMapper.", "Search": "Search", "Search activities": "Search activities", "Search activity": "Search activity", "Search Asset": "Search Asset", "Search by name or email": "Search by name or email", "Search by name or ID": "Search by name or ID", "Search by name or ID (at least 2 characters)": "Search by name or ID (at least 2 characters)", "Search category": "Search category", "Search currency": "Search currency", "Search email": "Search email", "Search features": "Search features", "Search field": "Search field", "Search for field": "Search for field", "Search for templates": "Search for templates", "Search item": "Search item", "Search member": "Search member", "Search organization": "Search organization", "Search pallet number/serial number": "Search pallet number/serial number", "Search property": "Search property", "Search resources": "Search resources", "Search results for": "Search results for", "Search Results for": "Search Results for", "Search roles": "Search roles", "Search stocks": "Search stocks", "Search tag": "Search tag", "Search teams": "Search teams", "Search this table": "Search this table", "seconds": "seconds", "secs": "secs", "Section": "Section", "Section created successfully": "Section created successfully", "Section deleted successfully": "Section deleted successfully", "Section deletion failed!": "Section deletion failed!", "Section name": "Section name", "Section updated successfully": "Section updated successfully", "Section with same name already exist": "Section with same name already exist", "Sections": "Sections", "Security": "Security", "See all": "See all", "See less": "See less", "select": "Select", "Select": "Select", "Select a .docx file as a template for filling the form data.": "Select a .docx file as a template for filling the form data.", "Select a block below": "Select a block below", "Select a container": "Select a container", "Select a document": "Select a document", "Select a feature": "Select a feature", "Select a form block": "Select a form block", "Select a map to consider the progress from": "Select a map to consider the progress from", "Select a member": "Select a member", "Select a page": "Select a page", "Select a project": "Select a project", "Select a row from the table which will be the header": "Select a row from the table which will be the header", "Select a status to set to the document on starting the review flow.": "Select a status to set to the document on starting the review flow.", "Select a status to set to the document on starting the reviewal flow.": "Select a status to set to the document on starting the reviewal flow.", "Select a status to set to the document on starting the submission flow.": "Select a status to set to the document on starting the submission flow.", "Select a template": "Select a template", "Select a template to add its fields to this section.": "Select a template to add its fields to this section.", "Select a template to automate reviewal process, or leave this field empty if you prefer not to use a workflow.": "Select a template to automate reviewal process, or leave this field empty if you prefer not to use a workflow.", "Select a version to compare": "Select a version to compare", "Select activities": "Select activities", "Select activity": "Select activity", "Select aggregation": "Select aggregation", "Select all": "Select all", "Select All": "Select All", "Select an option to create your automation": "Select an option to create your automation", "Select an option to create your schedule": "Select an option to create your schedule", "Select an option to create your simulation": "Select an option to create your simulation", "Select an option to create your template": "Select an option to create your template", "Select approver": "Select approver", "Select approvers": "Select approvers", "Select asset": "Select asset", "Select Asset": "Select Asset", "Select asset meta data": "Select asset meta data", "Select assets": "Select assets", "Select assets to give the member access to": "Select assets to give the member access to", "Select assignees": "Select assignees", "Select Assignees": "Select Assignees", "Select atleast one construction activity to automatically sync the completion progress": "Select atleast one construction activity to automatically sync the completion progress", "Select atleast one layer to lookup construction monitoring updates from": "Select atleast one layer to lookup construction monitoring updates from", "Select block": "Select block", "Select block to clone": "Select block to clone", "Select blocks": "Select blocks", "Select Blocks": "Select Blocks", "Select category": "Select category", "Select Category": "Select Category", "Select Checklists": "Select Checklists", "Select color": "Select color", "Select completion statuses for the documents selected": "Select completion statuses for the documents selected", "Select component": "Select component", "Select created at date": "Select created at date", "Select Created At Date": "Select Created At Date", "Select currency": "Select currency", "Select date": "Select date", "Select Date": "Select Date", "Select date & time": "Select date & time", "Select destination": "Select destination", "Select Destination": "Select Destination", "Select documents": "Select documents", "Select documents from Files": "Select documents from Files", "Select due date": "Select due date", "Select Due Date": "Select Due Date", "Select end date": "Select end date", "Select End Date": "Select End Date", "Select expected date of delivery": "Select expected date of delivery", "Select feature": "Select feature", "Select features within the same project": "Select features within the same project", "Select field type": "Select field type", "Select fields": "Select fields", "Select Fields Option": "Select Fields Option", "Select files": "Select files", "Select Folders": "Select Folders", "Select form": "Select form", "Select form field": "Select form field", "Select Forms": "Select Forms", "Select from Map": "Select from Map", "Select Groups": "Select Groups", "Select header": "Select header", "Select integration for each block before proceeding": "Select integration for each block before proceeding", "Select issue purpose": "Select issue purpose", "Select item": "Select item", "Select Item": "Select Item", "Select item type": "Select item type", "Select Layer": "Select Layer", "Select layers": "Select layers", "Select line": "Select line", "Select location": "Select location", "Select Location": "Select Location", "Select locations": "Select locations", "Select machine, trade, etc": "Select machine, trade, etc", "Select managers": "Select managers", "Select member": "Select member", "Select members": "Select members", "select members and teams": "Select Members and Teams", "Select members who can approve the documents along with you. The approvers can take an action irrespective of reviewer's decision at any time. Leave it empty if you don't want anyone else to provide final decision.": "Select members who can approve the documents along with you. The approvers can take an action irrespective of reviewer's decision at any time. Leave it empty if you don't want anyone else to provide final decision.", "Select only tasks to bulk update": "Select only tasks to bulk update", "Select option": "Select option", "Select options and preview": "Select options and preview", "Select Organisation": "Select Organisation", "Select owner": "Select owner", "Select Owner": "Select Owner", "Select palette": "Select palette", "Select Parents": "Select Parents", "Select point": "Select point", "Select polygon for volume calculation": "Select polygon for volume calculation", "Select priority": "Select priority", "Select Priority": "Select Priority", "Select projects": "Select projects", "Select range": "Select range", "Select reset frequency": "Select reset frequency", "Select resources": "Select resources", "Select reviewers": "Select reviewers", "Select roles": "Select roles", "Select roles to add this member": "Select roles to add this member", "Select sheet": "Select sheet", "Select start date": "Select start date", "Select Start Date": "Select Start Date", "Select status": "Select status", "Select Status": "Select Status", "Select submitted date": "Select submitted date", "Select Submitted Date": "Select Submitted Date", "Select Submitted on Day": "Select Submitted on Day", "Select tags": "Select tags", "Select Tags": "Select Tags", "Select Task": "Select Task", "Select teams": "Select teams", "Select teams to add the user to": "Select teams to add the user to", "Select teams to add this member": "Select teams to add this member", "Select template": "Select template", "Select Template": "Select Template", "Select Templates": "Select Templates", "Select the assets maps to assign to this user": "Select the assets maps to assign to this user", "Select the consolidator": "Select the consolidator", "Select the date for which the progress should be recorded.": "Select the date for which the progress should be recorded.", "Select the due date": "Select the due date", "Select the layers to lookup values in": "Select the layers to lookup values in", "Select the members to keep in the loop. These members can't take any action.": "Select the members to keep in the loop. These members can't take any action.", "Select the members to receive notifications for all form updates": "Select the members to receive notifications for all form updates", "Select the members to receive notifications for all task updates": "Select the members to receive notifications for all task updates", "Select the members who reviews/approves the document along with you.": "Select the members who reviews/approves the document along with you.", "Select the members whom you want to request documents from i.e submitters": "Select the members whom you want to request documents from i.e submitters", "Select the property to lookup if the entered values represents other than name": "Select the property to lookup if the entered values represents other than name", "Select the start and end times to set up working hours": "Select the start and end times to set up working hours", "Select the target feature type": "Select the target feature type", "Select the user who made the update": "Select the user who made the update", "Select the vendor fulfilling the shipment": "Select the vendor fulfilling the shipment", "Select time": "Select time", "select to filter annotation": "select to filter annotation", "Select type": "Select type", "Select Type": "Select Type", "Select unit of measure": "Select unit of measure", "Select User": "Select User", "Select users": "Select users", "Select users or teams": "Select users or teams", "Select users to be assigned this role": "Select users to be assigned this role", "Select Vendor": "Select Vendor", "Select Vendors": "Select Vendors", "Select warehouse type": "Select warehouse type", "Select Warehouses": "Select Warehouses", "Select Watchers": "Select Watchers", "Select Workflows": "Select Workflows", "Select your plan": "Select your plan", "Select your timezone": "Select your timezone", "selected": "selected", "Selected": "Selected", "selected as header": "selected as header", "Selected columns": "Selected columns", "Selected range": "Selected range", "Send": "Send", "Send a request to your supplier to request all the documents": "Send a request to your supplier to request all the documents", "Send an Email Block": "Send an Email Block", "Send approvals sequentially": "Send approvals sequentially", "Send customized emails to specific people manually or use dynamic fields": "Send customized emails to specific people manually or use dynamic fields", "Send daily summary emails": "Send daily summary emails", "Send documents": "Send documents", "Send email": "Send email", "Send email/push notifications based on the configured rules.": "Send email/push notifications based on the configured rules.", "Send email/push notifications based on the configured rules. Use Assign/Notify to escalate and assign to other members.": "Send email/push notifications based on the configured rules. Use Assign/Notify to escalate and assign to other members.", "Send for review": "Send for review", "Send me notifications for": "Send me notifications for", "Send notifications": "Send notifications", "Send Notifications": "Send Notifications", "Send notifications to people": "Send notifications to people", "Send now": "Send now", "Send submitted copies to": "Send submitted copies to", "Send summary": "Send summary", "Send summary emails everyday at": "Send summary emails everyday at", "Send Verification": "Send Verification", "send-email-push-notifications-assign-notify": "Send email/push notifications based on the configured rules. Use Assign/Notify to escalate and assign to other members.", "Sensor model": "Sensor model", "Sensor resolution": "Sensor resolution", "sent a message for the transmittal": "sent a message for the transmittal", "Sep": "Sep", "September": "September", "Serial / Pallet numbers not available": "Serial / Pallet numbers not available", "Serial & Pallet number details": "Serial & Pallet number details", "Serial No": "Serial No", "Serial Num": "Serial Num", "Serial number": "Serial number", "Serial Number": "Serial Number", "Serial number correction": "Serial number correction", "Serial numbers": "Serial numbers", "Serial Numbers": "Serial Numbers", "Serial numbers are copied to your clipboard.": "Serial numbers are copied to your clipboard.", "Serial numbers are not unique": "Serial numbers are not unique", "Serial numbers corrected through import will not reflect in Scan history.": "Serial numbers corrected through import will not reflect in Scan history.", "Serial numbers Table": "Serial numbers Table", "Serial or pallet numbers are not unique": "Serial or pallet numbers are not unique", "Serial tracking can only be set up at the time of item creation. You will not be able to alter this setting later.": "Serial tracking can only be set up at the time of item creation. You will not be able to alter this setting later.", "Server Error": "Server Error", "Session logout successful!": "Session logout successful!", "Set as default": "Set as default", "Set calibration": "Set calibration", "Set custom document statuses that align with your organization preference": "Set custom document statuses that align with your organization preference", "Set Custom Fields": "Set Custom Fields", "Set custom issue purposes that align with your organization preference": "Set custom issue purposes that align with your organization preference", "Set Document Statuses": "Set Document Statuses", "Set duration": "Set duration", "Set permissions for the users Or assign a role accordingly. You can also later access and set the permissions.": "Set permissions for the users Or assign a role accordingly. You can also later access and set the permissions.", "Set Review Statuses": "Set Review Statuses", "Set schedule": "Set schedule", "Set Schedule": "Set Schedule", "set the category": "set the category", "set the due date": "set the due date", "set the location": "set the location", "set the priority": "set the priority", "set the schedule": "set the schedule", "set the start date": "set the start date", "Set up issue purpose prefixes, which are short codes that can be used in document version numbering (Ex A for draft, B for review)": "Set up issue purpose prefixes, which are short codes that can be used in document version numbering (Ex A for draft, B for review)", "Set up version numbering based on issue purpose": "Set up version numbering based on issue purpose", "Setting": "Setting", "settings": "Settings", "Settings": "Settings", "Setup": "Setup", "Setup automatic activity progress update": "Setup automatic activity progress update", "Setup automatic progress sync": "Setup automatic progress sync", "Setup automations to notify, remind and run a workflow.": "Setup automations to notify, remind and run a workflow.", "Setup blocks": "Setup blocks", "Setup Blocks": "Setup Blocks", "Setup categories and sub-categories and attach to resources like tasks, forms, documents, etc to efficiently segregate": "Setup categories and sub-categories and attach to resources like tasks, forms, documents, etc to efficiently segregate", "Setup conditions to automatically execute business workflows when certain conditions occur": "Setup conditions to automatically execute business workflows when certain conditions occur", "Setup Domain Name": "Setup Domain Name", "Setup members, roles and grant access to assets and resources": "Setup members, roles and grant access to assets and resources", "Setup multiple destinations.": "Setup multiple destinations.", "Setup progress sync": "Setup progress sync", "Setup report": "Setup report", "Setup summary": "Setup summary", "Setup view/edit/hide access to specific sections": "Setup view/edit/hide access to specific sections", "setup-shortcuts-sidebar-app-drawer-desktop-mobile": "Setup shortcuts to quickly access the form directly from the sidebar and/or application drawer on desktop/mobile applications.", "Severity": "Severity", "Severity 1": "Severity 1", "Severity 2": "Severity 2", "Severity 3": "Severity 3", "Severity levels": "Severity levels", "Shape": "<PERSON><PERSON><PERSON>", "Share": "Share", "Share all accessible files and folders": "Share all accessible files and folders", "Share any dashboard": "Share any dashboard", "Share file": "Share file", "Share File": "Share File", "Share map": "Share map", "Share with": "Share with", "Share with people": "Share with people", "Share, organize, and discover information with Microsoft SharePoint": "Share, organize, and discover information with Microsoft SharePoint", "Shared by": "Shared by", "Shared items": "Shared items", "Shared on": "Shared on", "Shared with": "Shared with", "Shared With": "Shared With", "Shared with asset": "Shared with asset", "Shared with me": "Shared with me", "shared with members": "shared with members", "Shared with organization": "Shared with organization", "Sharepoint": "Sharepoint", "sharing": "sharing", "Sharing": "Sharing", "Sharing & Security": "Sharing & Security", "Sheet": "Sheet", "Sheet + Annotations": "Sheet + Annotations", "sheet is": "sheet is", "Sheet number": "Sheet number", "Sheet only": "Sheet only", "sheets": "sheets", "sheets are": "sheets are", "sheets have updated successfully with new version": "sheets have updated successfully with new version", "sheets to a drawing": "sheets to a drawing", "Shipment": "Shipment", "Shipment Number": "Shipment Number", "Shipment Status": "Shipment Status", "Show": "Show", "Show activities": "Show activities", "Show all form fields": "Show all form fields", "Show counts": "Show counts", "Show cumulative": "Show cumulative", "Show cumulative charts": "Show cumulative charts", "Show custom form fields": "Show custom form fields", "Show defects status icons": "Show defects status icons", "Show full comparison": "Show full comparison", "Show in stock": "Show in stock", "Show labels": "Show labels", "Show less": "Show less", "Show more": "Show more", "Show more details": "Show more details", "Show replies": "Show replies", "Show resolved": "Show resolved", "Show summary": "Show summary", "Show Summary": "Show Summary", "Shows a list of forms matching the filters.": "Shows a list of forms matching the filters.", "Shows a list of tasks matching the filters.": "Shows a list of tasks matching the filters.", "Shows daily count of modules scanned. Only scan additions are considered": "Shows daily count of modules scanned. Only scan additions are considered", "Side overlap": "Side overlap", "Sign": "Sign", "Sign in": "Sign in", "Sign in to SenseHawk": "Sign in to SenseHawk", "Sign in with Google": "Sign in with Google", "Sign up": "Sign up", "Sign up with Google": "Sign up with Google", "Signature": "Signature", "Signatures, Attachments will be retained and not copied": "Signatures, Attachments will be retained and not copied", "Signed by": "Signed by", "Simple, transparent pricing that grows with you. Try any plan free for 30 days.": "Simple, transparent pricing that grows with you. Try any plan free for 30 days.", "Single Axis": "Single Axis", "Single label": "Single label", "Singular name": "Singular name", "Site": "Site", "Site plan": "Site plan", "Six weeks": "Six weeks", "Skip": "<PERSON><PERSON>", "Skip step": "Skip step", "Slider": "Slide<PERSON>", "Slug mode enabled": "Slug mode enabled", "SM": "SM", "Snap while drawing": "Snap while drawing", "Solar asset management, Simplified": "Solar asset management, Simplified", "Solar Irradiance Annual": "Solar Irradiance Annual", "Solid": "Solid", "Some dependencies may be missing as the schedule is loaded on-demand.": "Some dependencies may be missing as the schedule is loaded on-demand.", "Some documents are already present in another transmittal": "Some documents are already present in another transmittal", "Some of the entered resource names already exist in the schedule.": "Some of the entered resource names already exist in the schedule.", "Someone shares a task, form, file, location or markup with you.": "Someone shares a task, form, file, location or markup with you.", "Something went wrong": "Something went wrong", "Something went wrong!": "Something went wrong!", "Soon": "Soon", "Sorry, there are no results for this search, please try another phrase.": "Sorry, there are no results for this search, please try another phrase.", "Sorry, you don't have access to this page": "Sorry, you don't have access to this page", "Sorry, you don’t have access to this page": "Sorry, you don’t have access to this page", "Sorry, you don't have access. Send request to access the workflows.": "Sorry, you don't have access. Send request to access the workflows.", "Sorry, you don’t have access. Send request to access the workflows.": "Sorry, you don’t have access. Send request to access the workflows.", "Sort": "Sort", "Sort by": "Sort by", "Sort Columns": "Sort Columns", "Source": "Source", "SPI": "SPI", "Split cell": "Split cell", "Spreadsheets": "Spreadsheets", "Square kilometers": "Square kilometers", "Square meters": "Square meters", "Stacked": "Stacked", "Standard": "Standard", "Starred": "Starred", "start": "start", "Start": "Start", "Start adding items to your asset. You can add items manually, import a file, or register from existing organization items to this asset.": "Start adding items to your asset. You can add items manually, import a file, or register from existing organization items to this asset.", "Start creating and sharing delivery, installation, transaction reports, etc., with team members for smooth inventory tracking.": "Start creating and sharing delivery, installation, transaction reports, etc., with team members for smooth inventory tracking.", "start date": "start date", "Start date": "Start date", "Start Date": "Start Date", "Start date status": "Start date status", "Start from scratch/template": "Start from scratch/template", "Start No Earlier Than": "Start No Earlier Than", "Start No Later Than": "Start No Later Than", "Start of this month": "Start of this month", "Start of this quarter": "Start of this quarter", "Start of this week": "Start of this week", "Start of this year": "Start of this year", "Start to finish": "Start to finish", "Start to Finish": "Start to Finish", "Start to start": "Start to start", "Start to Start": "Start to Start", "started": "started", "Started": "Started", "started a sub-workflow for the document ": "started a sub-workflow for the document ", "Starting a sub-workflow": "Starting a sub-workflow", "Starting number": "Starting number", "starts with": "starts with", "status": "Status", "Status": "Status", "Status Breakdown": "Status Breakdown", "status changed": "status changed", "Status changes can be made only for the document without an active transmittal.": "Status changes can be made only for the document without an active transmittal.", "Status changes can be made only for the documents without an active transmittal.": "Status changes can be made only for the documents without an active transmittal.", "Status changes to": "Status changes to", "Status name": "Status name", "Status percentages": "Status percentages", "Statuses": "Statuses", "Stay on top of things and keep track of all your stock to be used in your projects": "Stay on top of things and keep track of all your stock to be used in your projects", "step": "step", "Step": "Step", "STEP": "STEP", "step and the assignees will be notified": "step and the assignees will be notified", "Step history": "Step history", "Steps": "Steps", "Stock": "Stock", "Stock alerts": "Stock alerts", "Stock as of a date": "Stock as of a date", "Stock in": "Stock in", "Stock Information": "Stock Information", "Stock on hand": "Stock on hand", "Stock out": "Stock out", "Stock quantities by status": "Stock quantities by status", "Stock quantities by transaction": "Stock quantities by transaction", "Stock quantities by transactions": "Stock quantities by transactions", "Stock status": "Stock status", "Stock status already exists": "Stock status already exists", "Stock status already exists. Please enter a different stock status to continue.": "Stock status already exists. Please enter a different stock status to continue.", "Stock status deleted successfully": "Stock status deleted successfully", "Stock status deletion failed. Please try again.": "Stock status deletion failed. Please try again.", "Stock statuses": "Stock statuses", "Stock tracking": "Stock tracking", "Stocks": "Stocks", "Stocks Exported": "Stocks Exported", "Streamline workflows, improve collaboration and accessibility through Sharepoint integration. Automatically sync the documents from DMS to a Sharepoint account and maintain consistent and updated documents within the integrated sites.": "Streamline workflows, improve collaboration and accessibility through Sharepoint integration. Automatically sync the documents from DMS to a Sharepoint account and maintain consistent and updated documents within the integrated sites.", "Streamline your task execution using checklists. Breakdown and report progress for the task in detail": "Streamline your task execution using checklists. Breakdown and report progress for the task in detail", "Street": "Street", "Strict filtering": "Strict filtering", "Strike": "Strike", "String": "String", "Stroke style": "Stroke style", "Stroke thickness": "Stroke thickness", "Subfolders numbering": "Subfolders numbering", "Subject": "Subject", "Submission due date": "Submission due date", "Submission entry status": "Submission entry status", "submission history": "Submission history", "Submission pending": "Submission pending", "Submissions": "Submissions", "submit": "Submit", "Submit": "Submit", "Submit a new version": "Submit a new version", "Submit all forms": "Submit all forms", "Submit for approval": "Submit for approval", "Submit review": "Submit review", "Submit the document by": "Submit the document by", "Submit, view, analyze responses and reports": "Submit, view, analyze responses and reports", "Submittals": "Submittals", "submitted": "submitted", "Submitted": "Submitted", "Submitted at": "Submitted at", "Submitted At": "Submitted At", "Submitted by": "Submitted by", "Submitted By": "Submitted By", "Submitted by me": "Submitted by me", "Submitted for": "Submitted for", "Submitted on": "Submitted on", "submitted the document ": "submitted the document ", "submitted the document  with comment": "submitted the document  with comment", "submitted the document on": "submitted the document on", "Submitter": "Submitter", "Submitters": "Submitters", "Subscription": "Subscription", "Subtasks": "Subtasks", "Subtasks created successfully": "Subtasks created successfully", "Subtitle": "Subtitle", "Success": "Success!", "Successful!": "Successful!", "successfully": "successfully", "Successfully added fields from the template.": "Successfully added fields from the template.", "Successfully copied!": "Successfully copied!", "Successfully deleted": "Successfully deleted", "Successfully saved view": "Successfully saved view", "Successfully Sent for review": "Successfully Sent for review", "Successfully synced the changes": "Successfully synced the changes", "Successfully updated the version": "Successfully updated the version", "successor": "successor", "Successor": "Successor", "Successors": "Successors", "Suffix": "Suffix", "Suggested values": "Suggested values", "sum": "sum", "Sum": "Sum", "Summarize work done or amount spent for selected time-range and activities in a bar, line charts and a table.": "Summarize work done or amount spent for selected time-range and activities in a bar, line charts and a table.", "Summary": "Summary", "Summary can be viewed in": "Summary can be viewed in", "Summary of all the materials used of this component in different assets. You can see the scope, availability and installed count for each material.": "Summary of all the materials used of this component in different assets. You can see the scope, availability and installed count for each material.", "Sun": "Sun", "Sunday": "Sunday", "Support": "Support", "Supported formats: .png, .jpeg": "Supported formats: .png, .jpeg", "Supported formats: Primavera P6 (.xer, .xml), Microsoft Projects (.mpp)": "Supported formats: Primavera P6 (.xer, .xml), Microsoft Projects (.mpp)", "Supporting documents": "Supporting documents", "Surface to Surface": "Surface to Surface", "Surplus stock": "Surplus stock", "SV": "SV", "SVG, PNG, JPG or GIF": "SVG, PNG, JPG or GIF", "Switch language": "Switch language", "Switch to an asset to create a template": "Switch to an asset to create a template", "Switch to an asset to create a transmittal": "Switch to an asset to create a transmittal", "Switch to general menu": "Switch to general menu", "Switch to table menu": "Switch to table menu", "Switching view": "Switching view", "Symbol": "Symbol", "Sync": "Sync", "Sync all documents": "Sync all documents", "Sync asset metadata": "Sync asset metadata", "Sync attachments from form fields to a selected folder/schema or update a version.": "Sync attachments from form fields to a selected folder/schema or update a version.", "Sync changes": "Sync changes", "Sync Changes": "Sync Changes", "Sync completed": "Sync completed", "Sync document": "Sync document", "Sync document from TaskMapper to your Sharepoint accounts": "Sync document from TaskMapper to your Sharepoint accounts", "Sync existing blocks": "Sync existing blocks", "Sync failed": "Sync failed", "Sync folder items to a destination in Sharepoint.": "Sync folder items to a destination in Sharepoint.", "Sync from": "Sync from", "Sync from sharepoint": "Sync from sharepoint", "Sync history": "Sync history", "Sync in progress": "Sync in progress", "Sync information from forms to one or more asset metadata fields": "Sync information from forms to one or more asset metadata fields", "Sync now": "Sync now", "Sync Now": "Sync Now", "Sync progress": "Sync progress", "Sync properties": "Sync properties", "Sync with designs": "Sync with designs", "Sync with map and automatically update progress.": "Sync with map and automatically update progress.", "synced the document on": "synced the document on", "synced the document with a message": "synced the document with a message", "synced the file from the transmittal": "synced the file from the transmittal", "Synced with design": "Synced with design", "Syncing changes": "Syncing changes", "System defined roles for easy user permissions management.": "System defined roles for easy user permissions management.", "System model": "System model", "Table": "Table", "Table chart": "Table chart", "Table displaying the submissions for a form template with selected fields as columns.": "Table displaying the submissions for a form template with selected fields as columns.", "Table settings": "Table settings", "table-settings-modal-description": "Drag and Drop the Columns you want to display in the submission Table. You can also change the order of the columns by Drag and Drop.", "Tables": "Tables", "tag": "tag", "tags": "Tags", "Tags": "Tags", "Take me home": "Take me home", "Target ends after source starts": "Target ends after source starts", "Target ends with or after source": "Target ends with or after source", "Target starts after source ends": "Target starts after source ends", "Target starts with or after source": "Target starts with or after source", "task": "Task", "Task": "Task", "Task added": "Task added", "Task archived successfully": "Task archived successfully", "Task deleted successfully": "Task deleted successfully", "Task filters": "Task filters", "Task ID": "Task ID", "Task name": "Task name", "Task priority": "Task priority", "Task removed": "Task removed", "Task status": "Task status", "Task unarchived successfully": "Task unarchived successfully", "Task will be deleted permanently, are you sure?": "Task will be deleted permanently, are you sure?", "tasks": "Tasks", "Tasks": "Tasks", "Tasks added": "Tasks added", "Tasks already exists": "Tasks already exists", "Tasks archived successfully": "Tasks archived successfully", "Tasks count": "Tasks count", "Tasks deleted successfully": "Tasks deleted successfully", "Tasks list": "Tasks list", "Tasks per day": "Tasks per day", "Tasks per month": "Tasks per month", "Tasks per quarter": "Tasks per quarter", "Tasks per week": "Tasks per week", "Tasks per year": "Tasks per year", "Tasks that are related to each other but not dependent on each other": "Tasks that are related to each other but not dependent on each other", "Tasks that can't start until this task is completed": "Tasks that can't start until this task is completed", "Tasks that must be completed before working on this task": "Tasks that must be completed before working on this task", "Tasks unarchived successfully": "Tasks unarchived successfully", "tasks? This action cannot be undone": "tasks? This action cannot be undone", "Teal": "<PERSON><PERSON>", "team": "team", "Team": "Team", "Team created": "Team created", "Team created successfully": "Team created successfully", "Team deleted": "Team deleted", "Team description": "Team description", "Team duplicated successfully": "Team duplicated successfully", "Team member": "Team member", "Team name": "Team name", "Team updated": "Team updated", "Team updated successfully": "Team updated successfully", "Teams": "Teams", "Teams with access": "Teams with access", "Technical specifications": "Technical specifications", "Tell us a little bit so we can help set up your project and suggest templates": "Tell us a little bit so we can help set up your project and suggest templates", "Temperature difference": "Temperature difference", "Temperature Histogram": "Temperature Histogram", "template": "Template", "Template": "Template", "Template created successfully": "Template created successfully", "Template Deletion failed!": "Template Deletion failed!", "Template duplicated": "Template duplicated", "Template exported": "Template exported", "Template is not published": "Template is not published", "Template loaded": "Template loaded", "Template Manager": "Template Manager", "Template name": "Template name", "Template Name": "Template Name", "Template not found": "Temp<PERSON> not found", "Template not published": "Template not published", "Template Not Yet Published To Create New Form!": "Template Not Yet Published To Create New Form!", "Template saved": "Template saved", "Template Updated Successfully!": "Template Updated Successfully!", "Template Updating failed!": "Template Updating failed!", "templates": "Templates", "Templates": "Templates", "Templates manager": "Templates manager", "Terms and conditions": "Terms and conditions", "Terms of Service": "Terms of Service", "Terra": "Terra", "Terra Activities": "Terra Activities", "Test": "Test", "Text": "Text", "Text align": "Text align", "Text Area": "Text Area", "Text bottom": "Text bottom", "Text center": "Text center", "Text color": "Text color", "Text Field": "Text Field", "Text justify": "Text justify", "Text left": "Text left", "Text middle": "Text middle", "Text opacity": "Text opacity", "Text right": "Text right", "Text style": "Text style", "Text top": "Text top", "Thanks & Regards": "Thanks & Regards", "the": "the", "The": "The", "The AC capacity of a particular project.": "The AC capacity of a particular project.", "The activity": "The activity", "The activity is ahead of schedule by": "The activity is ahead of schedule by", "The activity is complete": "The activity is complete", "The activity is lagging behind the schedule by": "The activity is lagging behind the schedule by", "The activity is on schedule": "The activity is on schedule", "The activity is scheduled to start": "The activity is scheduled to start", "The activity was completed": "The activity was completed", "The activity was completed with a delay of": "The activity was completed with a delay of", "The activity was scheduled to start": "The activity was scheduled to start", "The added users will receive an email with the transaction details.": "The added users will receive an email with the transaction details.", "The aggregated values are displayed in the footer when the assets are grouped in the list view": "The aggregated values are displayed in the footer when the assets are grouped in the list view", "The all-in-one construction management software built to help you finish quality projects": "The all-in-one construction management software built to help you finish quality projects", "The anomaly is not severe enough to warrant immediate action. For example - Dirt, Shadow, Vegetation, Suspected Soiling.": "The anomaly is not severe enough to warrant immediate action. For example - <PERSON><PERSON>, <PERSON>, Vege<PERSON>, Suspected Soiling.", "The approval block would enable the user to approve or reject any block in the workflow": "The approval block would enable the user to approve or reject any block in the workflow", "The automation will not trigger if the activity has": "The automation will not trigger if the activity has", "The calendars used by this schedule are from an imported file and cannot be changed on this platform. To make changes, use the source platform and reimport the updated schedule file.": "The calendars used by this schedule are from an imported file and cannot be changed on this platform. To make changes, use the source platform and reimport the updated schedule file.", "The combined capabilities of Taskmapper and SharePoint as a document management and file storage platform complement each other perfectly.": "The combined capabilities of Taskmapper and SharePoint as a document management and file storage platform complement each other perfectly.", "The configured reminders or any changes made in already configured reminders will apply only to the forms created hereafter": "The configured reminders or any changes made in already configured reminders will apply only to the forms created hereafter", "The custom field has been attached successfully.": "The custom field has been attached successfully.", "The custom field has been created successfully.": "The custom field has been created successfully.", "The custom field has been updated successfully.": "The custom field has been updated successfully.", "The dashboard is not published and is not available for the users.": "The dashboard is not published and is not available for the users.", "The data is either invalid/duplicate or can't be modified.": "The data is either invalid/duplicate or can't be modified.", "The data that you have copied from the previous form has been pasted successfully": "The data that you have copied from the previous form has been pasted successfully", "The dependency relations are conflicting with each other": "The dependency relations are conflicting with each other", "The document review and approval process will only continue once all documents are submitted.": "The document review and approval process will only continue once all documents are submitted.", "The document statuses will be synced only after completion of the transmittal.": "The document statuses will be synced only after completion of the transmittal.", "The document will be sent back to submitter": "The document will be sent back to submitter", "The document will be sent back to the submitter for revised version": "The document will be sent back to the submitter for revised version", "The due date for the current form will be set to": "The due date for the current form will be set to", "The due date for the current task will be set to": "The due date for the current task will be set to", "The due date will be automatically set from the date of creation": "The due date will be automatically set from the date of creation", "The entered Activity ID already exists": "The entered Activity ID already exists", "The field value will override the above selected members if a value is available for the field.": "The field value will override the above selected members if a value is available for the field.", "The fields selected should be from the same table": "The fields selected should be from the same table", "The file": "The file", "The file cannot be viewed": "The file cannot be viewed", "The file has been downloaded successfully": "The file has been downlaoded successfully.", "The file has been renamed successfully": "The file has been renamed successfully", "The file have been shared successfully": "The file have been shared successfully", "The file size must not exceed 1 MB": "The file size must not exceed 1 MB", "The file size must not exceed 5 MB": "The file size must not exceed 5 MB", "The final document status and new version of the document will be synced immediately in the register": "The final document status and new version of the document will be synced immediately in the register", "The final outcome for the document is": "The final outcome for the document is", "The folder and/or file provided in the URL do not exist": "The folder and/or file provided in the URL do not exist", "The folder and/or file provided in the URL does not exist": "The folder and/or file provided in the URL does not exist", "The folder has been downloaded successfully": "The folder has been downlaoded successfully.", "The folder has been renamed successfully": "The folder has been renamed successfully", "The folder have been shared successfully": "The folder have been shared successfully", "The folder you are trying to download has no files": "The folder you are trying to download has no files", "The following classes will get changed once saved": "The following classes will get changed once saved", "The following columns are supported. Some may be required, rest are optional": "The following columns are supported. Some may be required, rest are optional", "The following files will be uploaded to the project": "The following files will be uploaded to the project", "The following ID(s) already exist in the schedule": "The following ID(s) already exist in the schedule", "The following people have access to this project": "The following people have access to this project", "The following people have access to this project:": "The following people have access to this project:", "The following users have access": "The following users have access", "The following users have access to this project": "The following users have access to this project", "The following users have access to this template": "The following users have access to this template", "The form has been completed/closed.": "The form has been completed/closed.", "The form will be reassigned to the same members who were assignees at the time of submission of the": "The form will be reassigned to the same members who were assignees at the time of submission of the", "The form will be reopened and the assignees will be notified": "The form will be reopened and the assignees will be notified", "The form will be reopened to the selected form block and the assignees will be notified": "The form will be reopened to the selected form block and the assignees will be notified", "The form will be rolled back to": "The form will be rolled back to", "The form will be rolled back to the selected form block and the assignees will be notified": "The form will be rolled back to the selected form block and the assignees will be notified", "The imported file contains invalid start and end dates": "The imported file contains invalid start and end dates", "The initial quantity of items during the start of the financial year or accounting period": "The initial quantity of items during the start of the financial year or accounting period", "The item has been created successfully.": "The item has been created successfully.", "The item has been successfully deleted": "The item has been successfully deleted", "The item was successfully duplicated.": "The item was successfully duplicated.", "The item will be available to be added in future transactions.": "The item will be available to be added in future transactions.", "The item will no longer be available for future transactions.": "The item will no longer be available for future transactions.", "The link will be created with": "The link will be created with", "The Members field is required": "The Members field is required", "The members selected will be added as reviewers of this step.": "The members selected will be added as reviewers of this step.", "The members should add a note/comment while reviewing, approving/rejecting the request.": "The members should add a note/comment while reviewing, approving/rejecting the request.", "The modifications will affect forms that have not yet reached this stage and forms created thereafter.": "The modifications will affect forms that have not yet reached this stage and forms created thereafter.", "The number of the delivery vehicle": "The number of the delivery vehicle", "The offloading quantity entered exceeds the item quantity entered.": "The offloading quantity entered exceeds the item quantity entered.", "The offloading quantity entered is less than the item quantity entered.": "The offloading quantity entered is less than the item quantity entered.", "The owner has been successfully transferred to ": "The owner has been successfully transferred to ", "The owner has been transferred successfully": "The owner has been transferred successfully.", "The page you are looking for doesn't exist": "The page you are looking for doesn't exist", "The pre-defined conditions will not be templatized.": "The pre-defined conditions will not be templatized.", "The progress is configured to sync automatically": "The progress is configured to sync automatically", "The progress is indicated as of": "The progress is indicated as of", "The project must have at least one task": "The project must have at least one task", "The project must have at least one task.": "The project must have at least one task.", "The repair of existing facilities when the size, type or extent of such facilities is not thereby": "The repair of existing facilities when the size, type or extent of such facilities is not thereby", "The report is downloading": "The report is downloading", "The report is downloading...": "The report is downloading...", "The review process will start once the documents are submitted skipping any approvers selected in the above step.": "The review process will start once the documents are submitted skipping any approvers selected in the above step.", "The role has been successfully deleted": "The role has been successfully deleted", "The SDP Files folder where important contractual documents for the project are stored.": "The SDP Files folder where important contractual documents for the project are stored.", "The selected document has a placeholder attachment, please remove to proceed": "The selected document has a placeholder attachment, please remove to proceed", "The selected issues already have a task created/associated with them.": "The selected issues already have a task created/associated with them.", "The selected maps are archived due to inactivity. You can request to process and generate maps using the button below. It might take about 2 hours to process. Do you want to continue?": "The selected maps are archived due to inactivity. You can request to process and generate maps using the button below. It might take about 2 hours to process. Do you want to continue?", "The selected resource doesn't have access to this schedule. The schedule will be shared with them at the chosen access level once saved.": "The selected resource doesn't have access to this schedule. The schedule will be shared with them at the chosen access level once saved.", "The start date and due date for the current form will be set to": "The start date and due date for the current form will be set to", "The start date and due date for the current task will be set to": "The start date and due date for the current task will be set to", "The start date will be set to the mentioned number of days before the schedule/due-date": "The start date will be set to the mentioned number of days before the schedule/due-date", "The status will be updated to": "The status will be updated to", "The statuses in which the stock can exist. These statuses can be customized as per your business requirements. Get in touch with our team for help.": "The statuses in which the stock can exist. These statuses can be customized as per your business requirements. Get in touch with our team for help.", "The stock will be moved from the above statuses in the same order as they are displayed to the 'Issued' status on performing the transaction": "The stock will be moved from the above statuses in the same order as they are displayed to the \"Issued\" status on performing the transaction", "The submitter of form will receive submitted copies by default": "The submitter of form will receive submitted copies by default", "The sum of all weights must be equal to 100.": "The sum of all weights must be equal to 100.", "The sum of the weights cannot be less than": "The sum of the weights cannot be less than", "The sum of the weights cannot exceed": "The sum of the weights cannot exceed", "The system model containing the asset/component details of a particular project.": "The system model containing the asset/component details of a particular project.", "The table cannot be empty.": "The table cannot be empty.", "The table cannot have duplicate IDs": "The table cannot have duplicate IDs", "The table cannot have duplicate resource names.": "The table cannot have duplicate resource names.", "The table contents are hidden due to workflow configuration.": "The table contents are hidden due to workflow configuration.", "The target task can't end before the source task ends (but it may end later).": "The target task can't end before the source task ends (but it may end later).", "The target task can't end before the source task starts (but it may end later).": "The target task can't end before the source task starts (but it may end later).", "The target task can't start before the source task ends (but it may start later).": "The target task can't start before the source task ends (but it may start later).", "The target task can't start until the source task starts (but it may start later).": "The target task can't start until the source task starts (but it may start later).", "The team has been successfully deleted": "The team has been successfully deleted", "The team members will be able to access the below selected assets but they will not be added as asset members unless explicitly added.": "The team members will be able to access the below selected assets but they will not be added as asset members unless explicitly added.", "The team members will inherit permissions from the below selected roles.": "The team members will inherit permissions from the below selected roles.", "The template doesn't exist or you don't have access to it.": "The template doesn't exist or you don't have access to it.", "The template doesn’t exist or you don’t have access to it.": "The template doesn’t exist or you don’t have access to it.", "The template is unpublished. Publish the template to proceed.": "The template is unpublished. Publish the template to proceed.", "The template name cannot be the same as the singular/plural labels.": "The template name cannot be the same as the singular/plural labels.", "The transmittal will be delegated to the concerned person selected below": "The transmittal will be delegated to the concerned person selected below", "The unaccounted serial numbers are copied to your clipboard.": "The unaccounted serial numbers are copied to your clipboard.", "The uploaded file is of invalid format": "The uploaded file is of invalid format", "The user has been successfully activated": "The user has been successfully activated", "The user has been successfully deactivated": "The user has been successfully deactivated", "The user has been, successfully activated": "The user has been successfully activated", "The user has been, successfully deactivated": "The user has been successfully deactivated", "The users will receive an email with transaction details": "The users will receive an email with transaction details", "The view has changed. Do you want to save this as custom view?": "The view has changed. Do you want to save this as custom view?", "The view has changed. Do you want to save this in current view?": "The view has changed. Do you want to save this in current view?", "The view has changed. Do you want to save this view for all users?": "The view has changed. Do you want to save this view for all users?", "The warehouse has been successfully created.": "The warehouse has been successfully created.", "The weight of the folder is not equal to 100%": "The weight of the folder is not equal to 100%", "The weight of the folder is not equal to 100%, current weight is ": "The weight of the folder is not equal to 100%, current weight is ", "The widget has been successfully created and saved on the dashboard": "The widget has been successfully created and saved on the dashboard", "The workflow template has been successfully activated": "The workflow template has been successfully activated.", "The workflow template has been successfully activated.": "The workflow template has been successfully activated.", "Then": "Then", "There are no data to display here": "There are no data to display here", "Therm": "Therm", "Thermal sensor details": "Thermal sensor details", "These are anomalies which are": "These are anomalies which are", "These are anomalies whose temperature difference < 40°C and not under severity level 1. It is necessary to conduct an inspection to identify root cause and rectify the anomaly within a short period of time.": "These are anomalies whose temperature difference < 40°C and not under severity level 1. It is necessary to conduct an inspection to identify root cause and rectify the anomaly within a short period of time.", "These are anomalies whose temperature difference is": "These are anomalies whose temperature difference is", "This action cannot be undone": "This action cannot be undone", "This action cannot be undone.": "This action cannot be undone.", "This action cannot be undone. Are you sure you want to delete?": "This action cannot be undone. Are you sure you want to delete?", "This browser does not support notifications": "This browser does not support notifications", "This Device": "This Device", "This document is blocked by transmittal": "This document is blocked by transmittal", "This document is currently in another transmittal": "This document is currently in another transmittal", "This document is in an active transmittal.": "This document is in an active transmittal.", "This field is required": "This field is required", "This file is for only download": "This file is for only download", "This file is not supported for viewing on the platform. Download to view it.": "This file is not supported for viewing on the platform. Download to view it.", "This is autogenerated field": "This is autogenerated field", "This is autogenerated field.": "This is autogenerated field.", "This is the very beginning of #Checklists": "This is the very beginning of #Checklists", "This is the very beginning of #Comments": "This is the very beginning of #Comments", "This is the very beginning of #Dependencies": "This is the very beginning of #Dependencies", "This is the very beginning of #Subtasks": "This is the very beginning of #Subtasks", "This message was deleted": "This message was deleted", "This milestone has been completed": "This milestone has been completed", "This milestone has no predecessors. You can manually mark it as complete.": "This milestone has no predecessors. You can manually mark it as complete.", "This milestone is manually marked as complete.": "This milestone is manually marked as complete.", "This month": "This month", "This operation will put the selected document(s) on hold in the current transmittal until they are processed in the new transmittal. Are you sure you want to continue?": "This operation will put the selected document(s) on hold in the current transmittal until they are processed in the new transmittal. Are you sure you want to continue?", "This quarter": "This quarter", "This reply was deleted": "This reply was deleted", "This schedule cannot be edited.": "This schedule cannot be edited.", "This schedule has been published.": "This schedule has been published.", "This schedule has been unpublished.": "This schedule has been unpublished.", "This setting will not affect the submitted data.": "This setting will not affect the submitted data.", "This task is waiting on another task to be completed. Are you sure you want to change the status of this task?": "This task is waiting on another task to be completed. Are you sure you want to change the status of this task?", "This version is displayed as black": "This version is displayed as black", "This view has unsaved changes": "This view has unsaved changes", "This week": "This week", "this widget": "this widget", "This will clear the custom email, are you sure?": "This will clear the custom email, are you sure?", "This will only execute for the forms that are created for a feature.": "This will only execute for the forms that are created for a feature.", "This will only execute for the forms that are created for a file.": "This will only execute for the forms that are created for a file.", "This will only register items to asset": "This will only register items to asset", "This year": "This year", "Thu": "<PERSON>hu", "Thursday": "Thursday", "Time": "Time", "Time based progress view for selected activities": "Time based progress view for selected activities", "Time period": "Time period", "Time range": "Time range", "Time stamp": "Time stamp", "Timeline": "Timeline", "Timeline view": "Timeline view", "Timerange": "Timerange", "times": "times", "Timestamp": "Timestamp", "Timezone": "Timezone", "Timezone updated successfully": "Timezone updated successfully", "Title": "Title", "to": "to", "To": "To", "to close": "to close", "to delete template": "to delete template", "to enable the workflows": "to enable the workflows", "To ensure uninterrupted access, pick a plan now.": "To ensure uninterrupted access, pick a plan now.", "To me": "To me", "to navigate": "to navigate", "to select": "to select", "today": "today", "Today": "Today", "Today line": "Today line", "Toggle branch": "Toggle branch", "Toggle Fly": "Toggle Fly", "Toggle header column": "Toggle header column", "Toggle header row": "Toggle header row", "Toggle privacy setting for any drawing": "Toggle privacy setting for any drawing", "Toggle Snap": "Toggle Snap", "Toggling modes require reload, continue?": "Toggling modes require reload, continue?", "Tomorrow": "Tomorrow", "Tomorrow afternoon - ": "Tomorrow afternoon - ", "Tomorrow morning - ": "Tomorrow morning - ", "Too many login attempts. Please try again in 60 minutes.": "Too many login attempts. Please try again in 60 minutes.", "Tools": "Tools", "Top": "Top", "Top to bottom": "Top to bottom", "Total": "Total", "Total (scope)": "Total (scope)", "Total activities": "Total activities", "Total Capacity": "Total Capacity", "Total Cost": "Total Cost", "Total DC capacity": "Total DC capacity", "Total DC Capacity": "Total DC Capacity", "Total duration": "Total duration", "Total float": "Total float", "Total images": "Total images", "Total number of times the": "Total number of times the", "Total percentage completion of the schedule along with lead/lag indicator.": "Total percentage completion of the schedule along with lead/lag indicator.", "Total Qty": "Total Qty", "Total quantity": "Total quantity", "Total remaining duration": "Total remaining duration", "Total slack": "Total slack", "Total Slack": "Total Slack", "Total Stock": "Total Stock", "Total weight of subtasks cannot exceed 100%": "Total weight of subtasks cannot exceed 100%", "Total weight of subtasks is not 100%": "Total weight of subtasks is not 100%", "Total weights of documents do not sum to 100%": "Total weights of documents do not sum to 100%", "Track and review all system, user activities with detailed logs for improved compliance": "Track and review all system, user activities with detailed logs for improved compliance", "Track block-level material requirements for the selected warehouses": "Track block-level material requirements for the selected warehouses", "Track by": "Track by", "Track cost": "Track cost", "Track day, week or month wise procured quantities for the selected transaction type": "Track day, week or month wise procured quantities for the selected transaction type", "Track defects using tasks": "Track defects using tasks", "Track information across various items (or material) for the defined columns": "Track information across various items (or material) for the defined columns", "Track items within transactions with a pivot table": "Track items within transactions with a pivot table", "Track resources": "Track resources", "Track serial number for all transactions": "Track serial number for all transactions", "Track serial numbers in bulk for all transactions": "Track serial numbers in bulk for all transactions", "Track stock information in a pivot table for the items that are tracked using serial numbers": "Track stock information in a pivot table for the items that are tracked using serial numbers", "Track stock quantities": "Track stock quantities", "Track the time spent, cost spent, resources used in an activity to monitor the progress.": "Track the time spent, cost spent, resources used in an activity to monitor the progress.", "Track work done, amount spent for the activity, or record for a particular resource.": "Track work done, amount spent for the activity, or record for a particular resource.", "Tracker": "Tracker", "Tracker completion": "Tracker completion", "Tracker name": "Tracker name", "Tracker progress": "Tracker progress", "Trackers": "Trackers", "Tracking": "Tracking", "Transacted quantities": "Transacted quantities", "Transaction": "Transaction", "Transaction could not be published. Please try again.": "Transaction could not be published. Please try again.", "Transaction date": "Transaction date", "Transaction draft creation failed. Please try again.": "Transaction draft creation failed. Please try again.", "Transaction export failed. Please try again.": "Transaction export failed. Please try again.", "Transaction item is required": "Transaction item is required", "Transaction item is required. Please add a item to continue.": "Transaction item is required. Please add a item to continue.", "Transaction items": "Transaction items", "Transaction number": "Transaction number", "Transaction number already exists": "Transaction number already exists", "Transaction number already exists. Please enter a different transaction number to continue.": "Transaction number already exists. Please enter a different transaction number to continue.", "Transaction number generation failed. Please try again.": "Transaction number generation failed. Please try again.", "Transaction published date": "Transaction published date", "Transaction published successfully": "Transaction published successfully", "Transaction type": "Transaction type", "Transaction types": "Transaction types", "Transactions": "Transactions", "Transactions export failed. Please try again.": "Transactions export failed. Please try again.", "Transactions Exported": "Transactions Exported", "Transfer": "Transfer", "Transfer ownership": "Transfer ownership", "Transfer Ownership": "Transfer Ownership", "transferred ownership to": "transferred ownership to", "Transmit": "Transmit", "Transmittal": "Transmittal", "Transmittal archived Successfully": "Transmittal archived Successfully", "Transmittal cancelled Successfully": "Transmittal cancelled Successfully", "Transmittal Created Successfully": "Transmittal Created Successfully", "Transmittal Id": "Transmittal Id", "Transmittal is completed": "Transmittal is completed", "Transmittal is currently under review": "Transmittal is currently under review", "Transmittal is currently waiting for the documents": "Transmittal is currently waiting for the documents", "Transmittal is waiting for the documents": "Transmittal is waiting for the documents", "Transmittal is waiting for your review": "Transmittal is waiting for your review", "Transmittal list": "Transmittal list", "Transmittal saved as draft": "Transmittal saved as draft", "Transmittal saving as draft failed": "Transmittal saving as draft failed", "Transmittal Settings": "Transmittal Settings", "Transmittal unarchived Successfully": "Transmittal unarchived Successfully", "Transmittal updated Successfully": "Transmittal updated Successfully", "Transmittals": "Transmittals", "transmitted": "transmitted", "Transmitted successfully": "Transmitted successfully", "transmitted the file in the transmittal": "transmitted the file in the transmittal", "Transpose row and columns": "Transpose row and columns", "Transpose rows and columns": "Transpose rows and columns", "Trash": "Trash", "Triangulated": "Triangulated", "Trigger": "<PERSON><PERSON>", "Trigger Editor": "<PERSON>gger Editor", "Truck Number": "Truck Number", "True": "True", "Trying to generate the document  again": "Trying to generate the document  again", "Tue": "<PERSON><PERSON>", "Tuesday": "Tuesday", "Two weeks": "Two weeks", "Type": "Type", "Type a description...": "Type a description...", "Type Email Domain": "Type Email Domain", "Type something to comment": "Type something to comment", "Type the name of the resource": "Type the name of the resource", "Type to filter": "Type to filter", "Type your reply": "Type your reply", "typing": "typing", "un-cancelled the document  with a message": "un-cancelled the document  with a message", "Unable to archive": "Unable to archive", "Unable to create a team. Please double-check your input and try again.": "Unable to create a team. Please double-check your input and try again.", "Unable to create the item. Please try again": "Unable to create the item. Please try again", "Unable to create the role. Please double-check your input and try again": "Unable to create the role. Please double-check your input and try again", "Unable to create the transaction. Please try again": "Unable to create the transaction. Please try again", "Unable to create the warehouse. Please try again": "Unable to create the warehouse. Please try again", "Unable to create workflow template. Please try again.": "Unable to create workflow template. Please try again.", "Unable to delete template. Please try again.": "Unable to delete template. Please try again.", "Unable to delete the item. Please try again": "Unable to delete the item. Please try again", "Unable to delete the role. Please try again": "Unable to delete the role. Please try again", "Unable to delete the team. Please try again": "Unable to delete the team. Please try again", "Unable to delete this task.": "Unable to delete this task.", "Unable to duplicate template. Please try again.": "Unable to duplicate template. Please try again.", "Unable to fetch automations": "Unable to fetch automations", "Unable to fetch references": "Unable to fetch references", "Unable to find the location to restore. Make sure the path exists.": "Unable to find the location to restore. Make sure the path exists.", "Unable to get link. Please try after some time": "Unable to get link. Please try after some time", "Unable to invite users. Please double-check your input and try again": "Unable to invite users. Please double-check your input and try again", "Unable to load fields from the template. Please try again.": "Unable to load fields from the template. Please try again.", "Unable to save template. Please try again.": "Unable to save template. Please try again.", "Unable to save the dashboard. Please try again later": "Unable to save the dashboard. Please try again later", "Unable to save the schedule": "Unable to save the schedule", "Unable to unarchive": "Unable to unarchive", "Unable to update the item. Please try again": "Unable to update the item. Please try again", "Unable to update the role. Please double-check your input and try again": "Unable to update the role. Please double-check your input and try again", "Unable to update the user. Please try again": "Unable to update the user. Please try again", "Unable to update the warehouse. Please try again": "Unable to update the warehouse. Please try again", "Unarchive": "Unarchive", "Unarchive Task": "Unarchive Task", "Unarchive Transmittal": "Unarchive Transmittal", "unarchived": "unarchived", "unarchived a file": "unarchived a file", "unarchived a folder": "unarchived a folder", "unarchived the file": "unarchived the file", "unarchived the folder": "unarchived the folder", "Unassign": "Unassign", "Unassigned": "Unassigned", "unassigned and will be lost": "unassigned and will be lost", "Unassigned pages will be lost after you press": "Unassigned pages will be lost after you press", "unassociated": "unassociated", "Uncancel": "Uncancel", "Uncancel document": "Uncancel document", "Uncancel documents": "Uncancel documents", "Uncancel Request": "Uncancel Request", "Undefined": "Undefined", "Under review": "Under review", "Underline": "Underline", "Undo": "Undo", "Unique document number": "Unique document number", "Unit": "Unit", "Unit of measure": "Unit of measure", "Unit of measurement": "Unit of measurement", "units": "units", "Units": "Units", "Units Completion": "Units Completion", "Units Installed (units)": "Units Installed (units)", "Units of Measure": "Units of Measure", "Unknown": "Unknown", "unlinked": "unlinked", "unlinked forms": "unlinked forms", "Unlock": "Unlock", "Unlock Document": "Unlock Document", "Unlock now": "Unlock now", "Unlock Request for Approved Document": "Unlock Request for Approved Document", "Unlock Request for Cancelled Document": "Unlock Request for Cancelled Document", "unlocked the ": "unlocked the ", "Unpublish": "Unpublish", "Unpublish form": "Unpublish form", "Unpublish form to add blocks": "Unpublish form to add blocks", "Unpublish to modify the workflow": "Unpublish to modify the workflow", "Unpublished": "Unpublished", "Unpublished schedule": "Unpublished schedule", "Unpublished successfully": "Unpublished successfully", "unpublished the document": "unpublished the document", "unpublished the form": "unpublished the form", "Unpublishing this form will make all open/draft submissions not accessible until the form is published": "Unpublishing this form will make all open/draft submissions not accessible until the form is published", "Unread": "Unread", "Unread mentions": "Unread mentions", "Unread submissions": "Unread submissions", "Unregister item": "Unregister item", "Unsaved changes": "Unsaved changes", "Unselect to set up non working hours": "Unselect to set up non working hours", "Unstacked": "Unstacked", "Unsupported file": "Unsupported file", "Untitled": "Untitled", "Untitled Section": "Untitled Section", "UOM": "UOM", "UOM Added successfully": "UOM Added successfully", "Up to level": "Up to level", "Upcoming": "Upcoming", "update": "Update", "Update": "Update", "Update +New Form button label": "Update +New Form button label", "Update all schedules, activities, configure settings and manage versions": "Update all schedules, activities, configure settings and manage versions", "Update asset details": "Update asset details", "Update associated feature properties using form fields.": "Update associated feature properties using form fields.", "Update Attribute": "Update Attribute", "Update available, click on reload button to update.": "Update available, click on reload button to update.", "Update blocks": "Update blocks", "Update calibration": "Update calibration", "Update Checklist": "Update Checklist", "Update criteria": "Update criteria", "Update custom fields": "Update custom fields", "Update defect status, priority, assignees, serial number for all defects": "Update defect status, priority, assignees, serial number for all defects", "Update defect status, priority, assignees, serial number for assigned defects": "Update defect status, priority, assignees, serial number for assigned defects", "Update document": "Update document", "Update document meta data by mapping the form fields": "Update document meta data by mapping the form fields", "Update documents in register": "Update documents in register", "Update existing files": "Update existing files", "Update failed": "Update failed", "Update feature properties by mapping the properties of fields": "Update feature properties by mapping the properties of fields", "Update Field": "Update Field", "Update form": "Update form", "Update Form": "Update Form", "Update form, share with other users, schedule form": "Update form, share with other users, schedule form", "Update forms": "Update forms", "Update items": "Update items", "Update organization details": "Update organization details", "Update pivot table settings": "Update pivot table settings", "Update progress": "Update progress", "Update Progress": "Update Progress", "Update progress for features": "Update progress for features", "Update progress, status and other custom fields.": "Update progress, status and other custom fields.", "Update resources": "Update resources", "Update review": "Update review", "Update role": "Update role", "Update Schedule": "Update Schedule", "Update schedule tracker": "Update schedule tracker", "Update schedule tracker for construction progress monitoring": "Update schedule tracker for construction progress monitoring", "Update Tag": "Update Tag", "Update task": "Update task", "Update tasks": "Update tasks", "Update team": "Update team", "Update Template": "Update Template", "Update today's progress": "Update today's progress", "Update version": "Update version", "Update versions": "Update versions", "Update you billing details and address": "Update you billing details and address", "update-current-view": "The view has changed. Do you want to save this in current view?", "Update/Re-classify defect type": "Update/Re-classify defect type", "updated": "updated", "Updated": "Updated", "updated access of": "updated access of", "updated description": "updated description", "Updated report successfully": "Updated report successfully", "Updated successfully": "Updated successfully", "updated the block": "updated the block", "Updated the category": "Updated the category", "Updated the custom fields": " Updated the custom fields", "updated the document ": "updated the document ", "updated the field": "updated the field", "Updated the number": "Updated the number", "updated the section": "updated the section", "Updated the status": "Updated the status", "Updated the tags": "Updated the tags", "updated the transmittal": "updated the transmittal", "Updated!": "Updated!", "Updating block": "Updating block", "Updating version": "Updating version", "Updating Workflow": "Updating Workflow", "Upgrade Now": "Upgrade Now", "Upgrade to": "Upgrade to", "upload": "Upload", "Upload": "Upload", "Upload attachments to a new/existing sub folder matching the schema.": "Upload attachments to a new/existing sub folder matching the schema.", "Upload CSV": "Upload CSV", "Upload document": "Upload document", "Upload Documents": "Upload Documents", "Upload drawings": "Upload drawings", "Upload file": "Upload file", "Upload File": "Upload File", "Upload files": "Upload files", "Upload Files Block": "Upload Files Block", "Upload folder": "Upload folder", "Upload folders": "Upload folders", "Upload more": "Upload more", "Upload new version": "Upload new version", "Upload New Version": "Upload New Version", "Upload OR Drag & Drop": "Upload OR Drag & Drop", "Upload Plan": "Upload Plan", "Upload Signature": "Upload Signature", "Upload Source": "Upload Source", "Upload spreadsheet": "Upload spreadsheet", "Upload to a folder": "Upload to a folder", "uploaded": "uploaded", "Uploaded": "Uploaded", "Uploaded By": "Uploaded By", "Uploaded by me": "Uploaded by me", "uploaded the file": "uploaded the file", "Uploader": "Uploader", "Uploading files": "Uploading files", "Uploading new document will replace the existing document": "Uploading new document will replace the existing document", "Url": "Url", "URL": "URL", "URL copied successfully": "URL copied successfully", "Url copied!": "Url copied!", "Use for": "Use for", "Use form data to update associated feature details": "Use form data to update associated feature details", "Use our wide variety of customizable fields, ensuring efficient and accurate data collection.": "Use our wide variety of customizable fields, ensuring efficient and accurate data collection.", "Used for": "Used for", "Used in": "Used in", "Used/Installed": "Used/Installed", "user": "user", "User": "User", "User activated": "User activated", "User Basic Details": "User Basic Details", "User cancelled successfully": "User cancelled successfully", "User deactivated": "User deactivated", "User Info": "User Info", "User invited successfully": "User invited successfully", "User removed": "User removed", "User removed successfully from the asset": "User removed successfully from the asset", "User type": "User type", "User updated": "User updated", "Users": "Users", "Users & Invites": "Users & Invites", "Users can put comments on tasks created and add reminders to send emails/push notifications based on configured rules": "Users can put comments on tasks created and add reminders to send emails/push notifications based on configured rules", "Users will be notified": "Users will be notified", "Users will be notified.": "Users will be notified.", "Valid stock": "Valid stock", "Validate stock": "Validate stock", "Value": "Value", "Value label": "Value label", "Values": "Values", "Vectors": "Vectors", "Vehicle Number": "Vehicle Number", "Vendor": "<PERSON><PERSON><PERSON>", "Vendor already exists": "Vendor already exists", "Vendor already exists. Please enter a different vendor to continue.": "Vendor already exists. Please enter a different vendor to continue.", "Vendor created successfully": "<PERSON><PERSON><PERSON> created successfully", "Vendor deleted successfully": "<PERSON><PERSON><PERSON> deleted successfully", "Vendor deletion failed. Please try again.": "Vendor deletion failed. Please try again.", "Vendor name": "Vendor name", "Vendor Name": "Vendor Name", "Vendor Name(Optional)": "Vendor Name(Optional)", "Vendor updated successfully": "Vendor updated successfully", "Vendors": "Vend<PERSON>", "Verification code sent, check your email": "Verification code sent, check your email", "Verification email has been sent": "Verification email has been sent", "Verified": "Verified", "Verify Domain": "Verify Domain", "Verify the submittal": "Verify the submittal", "Verify your email": "Verify your email", "Version": "Version", "Version date": "Version date", "Version Deleted": "Version Deleted", "Version Description": "Version Description", "Version name": "Version name", "Version No": "Version No", "Version renamed successfully": "Version renamed successfully", "Version Schema": "Version Schema", "Version successfully deleted": "Version successfully deleted", "Version Uploaded": "Version Uploaded", "Versions": "Versions", "Vertical": "Vertical", "Vertical Bar Chart": "Vertical Bar Chart", "Vertical bar chart representing the count of forms by their assignees separately and matching the filtering criteria.": "Vertical bar chart representing the count of forms by their assignees separately and matching the filtering criteria.", "Vertical chart": "Vertical chart", "Videos": "Videos", "view": "view", "View": "View", "View Activities": "View Activities", "View activity log": "View activity log", "View all dashboards": "View all dashboards", "View all defects in accessible maps": "View all defects in accessible maps", "View all form templates i.e workflow, form layout and settings": "View all form templates i.e workflow, form layout and settings", "View all form templates i.e workflow, form, view settings but can not make any changes": "View all form templates i.e workflow, form, view settings but can not make any changes", "View all form templates i.e workflow, form, view settings but can not make any changes.": "View all form templates i.e workflow, form, view settings but can not make any changes.", "View all forms and the responses": "View all forms and the responses", "View all forms/responses and historical submissions": "View all forms/responses and historical submissions", "View all forms/responses and historical submissions.": "View all forms/responses and historical submissions.", "View all items, their details and stock information": "View all items, their details and stock information", "View all maps/layers": "View all maps/layers", "View all maps/layers, Share any map/layer, Update schedule tracker, Clear duplicate barcodes, Modify feature geometry, import/export feature json, properties, type and delete features.": "View all maps/layers, Share any map/layer, Update schedule tracker, Clear duplicate barcodes, Modify feature geometry, import/export feature json, properties, type and delete features.", "View all private/non-private drawings": "View all private/non-private drawings", "View all published transactions within the selected time range": "View all published transactions within the selected time range", "View all schedules": "View all schedules", "View all transactions": "View all transactions", "View all transmittals": "View all transmittals", "View and manage reports": "View and manage reports", "View and modify bill of material": "View and modify bill of material", "View and share maps.": "View and share maps.", "View and use transmittal workflows": "View and use transmittal workflows", "View assigned defects": "View assigned defects", "View assigned defects in accessible maps": "View assigned defects in accessible maps", "View barcode scan dashboards and download scan reports": "View barcode scan dashboards and download scan reports", "View charts - construction progress monitoring reports": "View charts - construction progress monitoring reports", "View created workflows": "View created workflows", "View defects": "View defects", "View details": "View details", "View Details": "View Details", "View Document": "View Document", "View entire schedule, manage resources, assignments, progress configurations, progress updates and integrations": "View entire schedule, manage resources, assignments, progress configurations, progress updates and integrations", "View existing construction progress workflows": "View existing construction progress workflows", "View feedback": "View feedback", "View form": "View form", "View integration": "View integration", "View items- stock and quantities in warehouses and records of all transactions.": "View items- stock and quantities in warehouses and records of all transactions.", "View location": "View location", "View more": "View more", "View on map": "View on map", "View own transactions and create drafts only.": "View, own transactions and create drafts only.", "View own transactions and create/publish transactions": "View own transactions and create/publish transactions", "View own transactions and create/publish transactions.": "View own transactions and create/publish transactions.", "View pivot data": "View pivot data", "View pivot data, View Scan, download reports, Create and modify views, Update progress for features, View created workflows": "View pivot data, View Scan, download reports, Create and modify views, Update progress for features, View created workflows", "View pivot data,View Scan, download reports,Create and modify views,Update progress for features,View created workflows": "View pivot data,View Scan, download reports,Create and modify views,Update progress for features,View created workflows", "View published charts, Download project reports, Create/modify pivot, Add/Update feature extraProperties and change feature type, Modify feature type symbology": "View published charts, Download project reports, Create/modify pivot, Add/Update feature extraProperties and change feature type, Modify feature type symbology", "View published charts. Download layer based reports": "View published charts. Download layer based reports", "View Receipt": "View Receipt", "View responses and historical submissions for all forms": "View responses and historical submissions for all forms", "View Scan dashboards and download Scan reports": "View Scan dashboards and download Scan reports", "View schedule, activities and all information related to the schedule": "View schedule, activities and all information related to the schedule", "View settings": "View settings", "View shared maps/resources only.": "View shared maps/resources only.", "View sheet": "View sheet", "View Shipment": "View Shipment", "View template": "View template", "View the stock quantities breakdown by their transactions in the selected time period": "View the stock quantities breakdown by their transactions in the selected time period", "View the stock quantities breakdown by their transactions in the selected time period.": "View the stock quantities breakdown by their transactions in the selected time period.", "View the stock quantities of various items as on a particular date": "View the stock quantities of various items as on a particular date", "View the stock quantities of various items as on a particular date.": "View the stock quantities of various items as on a particular date.", "View Transmittal": "View Transmittal", "View warehouses, stock and transactions within": "View warehouses, stock and transactions within", "View workflow": "View workflow", "View workflows": "View workflows", "View-details": "View-details", "View, change status, attach and comment on assigned tasks only": "View, change status, attach and comment on assigned tasks only", "View, fill and submit assigned forms only": "View, fill and submit assigned forms only", "View, modify, and share maps. Update features and types.": "View, modify, and share maps. Update features and types.", "View, modify, and share maps. Update features, types, and workflows.": "View, modify, and share maps. Update features, types, and workflows.", "View/analyze vector data i.e pivot table": "View/analyze vector data i.e pivot table", "viewed the document ": "viewed the document ", "viewed the file": "viewed the file", "viewed the form on": "viewed the form on", "viewed the task on": "viewed the task on", "Viewer": "Viewer", "Views": "Views", "Violet": "Violet", "Visible": "Visible", "Visible Columns": "Visible Columns", "Visible On": "Visible On", "Visualize the data in Gantt and Table formats.": "Visualize the data in Gantt and Table formats.", "Volume Calculate": "Volume Calculate", "Volume calculation": "Volume calculation", "Volume History": "Volume History", "Wait": "Wait", "Waiting": "Waiting", "Waiting for approval": "Waiting for approval", "Waiting for approvers to take action": "Waiting for approvers to take action", "Waiting for approvers to take action.": "Waiting for approvers to take action.", "Waiting for documents": "Waiting for documents", "Waiting for other members to approve": "Waiting for other members to approve", "Waiting for other members to review": "Waiting for other members to review", "Waiting for review": "Waiting for review", "Waiting for review and approval": "Waiting for review and approval", "Waiting for review from reviewers/approvers.": "Waiting for review from reviewers/approvers.", "Waiting for submission": "Waiting for submission", "Waiting for the document from submitters": "Waiting for the document from submitters", "Waiting for the review from another transmittal": "Waiting for the review from another transmittal", "Waiting for upload": "Waiting for upload", "Waiting on": "Waiting on", "Waiting On": "Waiting On", "Warehouse": "Warehouse", "Warehouse Allocation": "Warehouse Allocation", "Warehouse Code": "Warehouse Code", "Warehouse code already exists": "Warehouse code already exists", "Warehouse code already exists. Please enter a different code to continue.": "Warehouse code already exists. Please enter a different code to continue.", "Warehouse created": "Warehouse created", "Warehouse Created successfully": "Warehouse Created successfully", "Warehouse creation failed. Please try again.": "Warehouse creation failed. Please try again.", "Warehouse deletion failed. Please try again.": "Warehouse deletion failed. Please try again.", "Warehouse item list": "Warehouse item list", "Warehouse name": "Warehouse name", "Warehouse name already exists": "Warehouse name already exists", "Warehouse name already exists. Please enter a different name to continue.": "Warehouse name already exists. Please enter a different name to continue.", "Warehouse stock": "Warehouse stock", "Warehouse type": "Warehouse type", "Warehouse type already exists": "Warehouse type already exists", "Warehouse type already exists. Please enter a different warehouse type to continue.": "Warehouse type already exists. Please enter a different warehouse type to continue.", "Warehouse type created successfully": "Warehouse type created successfully", "Warehouse type deleted successfully": "Warehouse type deleted successfully", "Warehouse type updated successfully": "Warehouse type updated successfully", "Warehouse types": "Warehouse types", "Warehouse updated": "Warehouse updated", "Warehouse Updated successfully": "Warehouse Updated successfully", "Warehouses": "Warehouses", "Warning": "Warning", "Warranty date": "Warranty date", "Watchers": "Watchers", "WBS": "WBS", "We couldn't save the widget. Please double-check your input and try again": "We couldn't save the widget. Please double-check your input and try again", "We detected a location change. Do you want to change the timezone to this location": "We detected a location change. Do you want to change the timezone to this location", "We found an organisation with the same domain name. Would you like to join this organisation?": "We found an organisation with the same domain name. Would you like to join this organisation?", "We sent a verification link to": "We sent a verification link to", "Website": "Website", "Wed": "Wed", "Wednesday": "Wednesday", "Week": "Week", "Week to date": "Week to date", "week(s)": "week(s)", "Weekly": "Weekly", "Weekly Progress": "Weekly Progress", "weeks": "weeks", "Weeks": "Weeks", "weeks before": "weeks before", "Weight": "Weight", "Weightages do not sum to": "Weightages do not sum to", "Weightages sum to": "Weightages sum to", "Weights": "Weights", "Welcome to cnc": "Welcome to Commissioning & Certification!", "Welcome to files": "Welcome to Files", "Welcome to forms": "Welcome to Forms", "Welcome to Inventory": "Welcome to Inventory", "Welcome to inventory-items": "Welcome to Inventory", "Welcome to plans": "Welcome to Plans", "Welcome to project-management": "Welcome to Project management", "Welcome to SenseHawk": "Welcome to SenseHawk", "Welcome to TaskMapper": "Welcome to TaskMapper", "Welcome to tasks": "Welcome to Tasks", "Welcome to terra": "Welcome to Terra", "What do you want to copy": "What do you want to copy", "What's New": "What's New", "Whats's New": "Whats's New", "When": "When", "Whether to check stock existence before making the transaction": "Whether to check stock existence before making the transaction", "Whether to consider the stock as available or used within BOM": "Whether to consider the stock as available or used within BOM", "Whether to consider the stock in this status as stock on hand i.e within the warehouse": "Whether to consider the stock in this status as stock on hand i.e within the warehouse", "Widget Creation Failed": "Widget Creation Failed", "Widget not supported": "Widget not supported", "Widget Saved": "Widget Saved", "will be converted to type WBS and the data in the below table will be its children.": "will be converted to type WBS and the data in the below table will be its children.", "will be created": "will be created", "will be deleted": "will be deleted", "will be deleted from documents. You can restore it from Trash.": "will be deleted from documents. You can restore it from Trash.", "With": "With", "with a comment": "with a comment", "with a note": "with a note", "with instructions to reset your password": "with instructions to reset your password", "work": "work", "Work": "Work", "Work complete": "Work complete", "Work Complete": "Work Complete", "Work done": "Work done", "Work done is equal or greater than required": "Work done is equal or greater than required", "Work done is less than required": "Work done is less than required", "Work performed": "Work performed", "Work rate": "Work rate", "Work Rate": "Work Rate", "Work rate (actual)": "Work rate (actual)", "Work Rate (Actual)": "Work Rate (Actual)", "Work rate (baseline)": "Work rate (baseline)", "Work Rate (Baseline)": "Work Rate (Baseline)", "Work rate (planned)": "Work rate (planned)", "Work Rate (Planned)": "Work Rate (Planned)", "Work rate (remaining)": "Work rate (remaining)", "Work Rate (Remaining)": "Work Rate (Remaining)", "Work Rate (units/day)": "Work Rate (units/day)", "Work Required": "Work Required", "Work Required (per day)": "Work Required (per day)", "Work Required (Per Day)": "Work Required (Per Day)", "Work started early and is equal or greater than required": "Work started early and is equal or greater than required", "Work started early and is less than required": "Work started early and is less than required", "Work with your team by inviting them to your workspace.": "Work with your team by inviting them to your workspace.", "Workflow": "Workflow", "Workflow builder": "Workflow builder", "Workflow name": "Workflow name", "Workflow template": "Workflow template", "Workflow template created": "Workflow template created", "Workflow Templates": "Workflow Templates", "Workflow/Status settings": "Workflow/Status settings", "Workflows": "Workflows", "Working days": "Working days", "Working hours": "Working hours", "Workload": "Workload", "Wrong password. Try again or click Forgot password to reset it.": "Wrong password. Try again or click Forgot password to reset it.", "Year": "Year", "Year Full": "Year Full", "Year Short": "Year Short", "Year to date": "Year to date", "years": "years", "Years": "Years", "Yellow": "Yellow", "Yes": "Yes", "Yes / No": "Yes / No", "Yes, Change classes": "Yes, Change classes", "Yes, change my timezone": "Yes, change my timezone", "Yes, close this task": "Yes, close this task", "Yes, you can cancel your subscription at any time. All you need to do is go into your Account settings and change the Subscription Plan.": "Yes, you can cancel your subscription at any time. All you need to do is go into your Account settings and change the Subscription Plan.", "Yes/No": "Yes/No", "Yesterday": "Yesterday", "Yield estimator": "Yield estimator", "Yield Estimator": "Yield Estimator", "Yield Loss Estimate": "Yield Loss Estimate", "You": "You", "You approved": "You approved", "You are about to cancel the whole transmittal. This action is irreversible. If you want to continue, type confirm in the box.": "You are about to cancel the whole transmittal. This action is irreversible. If you want to continue, type confirm in the box.", "You are about to clear the progress history for all the features within the selected projects. This action is irreversible.": "You are about to clear the progress history for all the features within the selected projects. This action is irreversible.", "You are about to clear the progress history for all the features within the selected projects. This action is irreversible. If you want to continue, type confirm in the box.": "You are about to clear the progress history for all the features within the selected projects. This action is irreversible. If you want to continue, type confirm in the box.", "You are about to clear the progress history for the selected features. This action is irreversible. If you want to continue, type confirm in the box.": "You are about to clear the progress history for the selected features. This action is irreversible. If you want to continue, type confirm in the box.", "You are about to create a version numbering based on issue purpose. Are you sure you want to continue?": "You are about to create a version numbering based on issue purpose. Are you sure you want to continue?", "You are about to delete the integration. Are you sure?": "You are about to delete the integration. Are you sure?", "You are about to remove the integration. Existing documents in SharePoint will remain unaffected, though future updates will not be synced. Are you sure?": "You are about to remove the integration. Existing documents in SharePoint will remain unaffected, though future updates will not be synced. Are you sure?", "You are about to sync all documents. Existing documents in SharePoint will remain unaffected, and remaining all documents will be synced. Are you sure?": "You are about to sync all documents. Existing documents in SharePoint will remain unaffected, and remaining all documents will be synced. Are you sure?", "You are about to turn off the integration. Are you sure?": "You are about to turn off the integration. Are you sure?", "You are deleting a document status that is associated with some Documents. What should we do?": "You are deleting a document status that is associated with some Documents. What should we do?", "You are looking to remove access for the following users. You can remove activity tracking of any/all users. The trackings associated with a resource will be removed across all activities.": "You are looking to remove access for the following users. You can remove activity tracking of any/all users. The trackings associated with a resource will be removed across all activities.", "You can add the document yourself and mark it as submitted.": "You can add the document yourself and mark it as submitted.", "You can assign weightages to folders, files and see overall completion status on parent folder.": "You can assign weightages to folders, files and see overall completion status on parent folder.", "You can copy/paste or import additional metadata using a csv file. Download": "You can copy/paste or import additional metadata using a csv file. Download", "You can copy/paste or import additional metadata using an excel file. Download": "You can copy/paste or import additional metadata using an excel file. Download", "You can customize the report and also add summary to the exported pdf. Summary will be shown before task details in the report": "You can customize the report and also add summary to the exported pdf. Summary will be shown before task details in the report", "You can now copy the code by clicking on the": "You can now copy the code by clicking on the", "You can only select up to 100 forms at once": "You can only select up to 100 forms at once", "You can only select up to 100 tasks at once": "You can only select up to 100 tasks at once", "You can see your C&C simulation here. You can see default section like plant information, asset details etc": "You can see your C&C simulation here. You can see default section like plant information, asset details etc", "You can select users or enter their email address. They will receive transaction details through email.": "You can select users or enter their email address. They will receive transaction details through email.", "You can still review and change the status": "You can still review and change the status", "You can still review the document": "You can still review the document", "You can still submit the document": "You can still submit the document", "You can track different type of metrics": "You can track different type of metrics", "You can track different types of cost associated and work done by resources": "You can track different types of cost associated and work done by resources", "You cannnot delete selected files/folders": "You cannot delete selected files/folders", "You cannot make any changes": "You cannot make any changes", "You cannot mention the user": "You cannot mention the user", "You cannot move selected files/folders": "You cannot move selected files/folders", "You delegated the review to": "You delegated the review to", "You delegated the submission to": "You delegated the submission to", "You denied": "You denied", "You do not have permissions to update progress.": "You do not have permissions to update progress.", "You do not have the permission for this action": "You do not have the permission for this action", "You don't have enough permissions to create a form.": "You don't have enough permissions to create a form.", "You don’t have enough permissions to create a form.": "You don’t have enough permissions to create a form.", "You don't have enough permissions.": "You don't have enough permissions.", "You don't have permission to view the page": "You don't have permission to view the page", "You don't have permissions to perform this action.": "You don't have permissions to perform this action.", "You have access to only": "You have access to only", "You have been invited to join another organization": "You have been invited to join another organization", "You have unsaved changes": "You have unsaved changes", "You have unsaved changes. Do you want to navigate away?": "You have unsaved changes. Do you want to navigate away?", "You marked the document as ": "You marked the document as ", "You need to enter card details to proceed further and access the application. Please enter your card details below": "You need to enter card details to proceed further and access the application. Please enter your card details below", "You requested other members to review the document. Check the feedback section below for more details.": "You requested other members to review the document. Check the feedback section below for more details.", "You reviewed": "You reviewed", "You will be able to view these forms in the archived forms section.": "You will be able to view these forms in the archived forms section.", "You will be able to view this form in the archived forms section.": "You will be able to view this form in the archived forms section.", "You will continue to have access to the platform until then": "You will continue to have access to the platform until then", "You will no longer be able to see the schedule as you are not an admin.": "You will no longer be able to see the schedule as you are not an admin.", "You will receive an email to download once the export is completed.": "You will receive an email to download once the export is completed.", "you wont be charged again unless you restart your account. if you cancel with time left in your billing period, you can use sense-hawk until the account cancels automatically at the end of the billing period.": "You won't be charged again unless you restart your account. If you cancel with time left in your billing period, you can use SenseHawk until the account cancels automatically at the end of the billing period.", "Your @mention will add people to this discussion and send an email": "Your @mention will add people to this discussion and send an email", "Your #dashboard for scan will appear here": "Your #dashboard for scan will appear here", "Your approval is pending": "Your approval is pending", "Your changes are not transmitted. Please proceed to transmit them now.": "Your changes are not transmitted. Please proceed to transmit them now.", "Your changes have been saved": "Your changes have been saved", "Your changes have been saved and the selected version made active": "Your changes have been saved and the selected version made active", "Your changes have been saved and your dashboard is removed from the list": "Your changes have been saved and your dashboard is removed from the list", "Your changes have been saved and your report is removed from the list": "Your changes have been saved and your report is removed from the list", "Your changes have been saved and your schedule is added": "Your changes have been saved and your schedule is added", "Your changes have been saved and your subtasks are added to the list": "Your changes have been saved and your subtasks are added to the list", "Your changes have been saved and your task is archived from the list": "Your changes have been saved and your task is archived from the list", "Your changes have been saved and your task is removed from the list": "Your changes have been saved and your task is removed from the list", "Your changes have been saved and your task is unarchived from the list": "Your changes have been saved and your task is unarchived from the list", "Your changes have been saved and your tasks are archived from the list": "Your changes have been saved and your tasks are archived from the list", "Your changes have been saved and your tasks are removed from the list": "Your changes have been saved and your tasks are removed from the list", "Your changes have been saved and your tasks are unarchived from the list": "Your changes have been saved and your tasks are unarchived from the list", "Your changes to the dashboard have been successfully saved": "Your changes to the dashboard have been successfully saved", "Your changes to the role have been successfully saved": "Your changes to the role have been successfully saved", "Your changes to the team have been successfully saved": "Your changes to the team have been successfully saved", "Your changes to the user have been successfully saved": "Your changes to the user have been successfully saved", "Your changes to the user have been, successfully saved": "Your changes to the user have been successfully saved", "Your changes will be lost, are you sure?": "Your changes will be lost, are you sure?", "Your current action cannot be completed at this moment.": "Your current action cannot be completed at this moment.", "Your free trial ends on": "Your free trial ends on", "Your organization": "Your organization", "Your review is pending": "Your review is pending", "Your search results will appear here": "Your search results will appear here", "Your subscription will be cancelled at the end of the current billing cycle": "Your subscription will be cancelled at the end of the current billing cycle", "Your template has been duplicated successfully.": "Your template has been duplicated successfully.", "Your template has been saved successfully": "Your template has been saved successfully", "Your view has been modified": "Your view has been modified", "Zipping files to download": "Zipping files to download", "Zoom in": "Zoom in", "Zoom out": "Zoom out", "Δ Temperature": "Δ Temperature", "ΔT": "ΔT", "- - - - - - - - - - ADD NEW KEYS BELOW THIS LINE - - - - - - - - - -": "", "Associate activities": "Associate activities", "Add Activity": "Add Activity", "Sub activity": "Sub activity", "Sub activities": "Sub activities", "Sub layers": "Sub layers", "Select layer": "Select layer", "This activity was initially created in": "This activity was initially created in", "Allow using this activity in all assets": "Allow using this activity in all assets", "Modifying the activity will also update the activities in other assets": "Modifying the activity will also update the activities in other assets", "Add sub activities to be tracked, assign weights and configure feature types to reach at each percentage steps": "Add sub activities to be tracked, assign weights and configure feature types to reach at each percentage steps", "Updating": "Updating", "No Activities Yet": "No Activities Yet", "no-data-no-activities": "Add a new activity template to automate your daily tasks.", "Deleting the activity will remove all associated sub activities and all data collected for the features/sub activities. This action is irreversible. Are you sure you want to delete the activity?": "Deleting the activity will remove all associated sub activities and all data collected for the features/sub activities. This action is irreversible. Are you sure you want to delete the activity?", "Only internal users can delete map": "Only internal users can delete map", "Select Sub activities": "Select Sub activities", "Sub layer": "Sub layer", "Exporting report": "Exporting report", "Report exported": "Report exported"}