name: AUTOMATIC - Mergeback

on:
  pull_request:
    branches:
      - release/qa
      - release/pre-production
    types:
      - closed

jobs:
  autopr:
    runs-on: ubuntu-latest

    steps:
      - run: echo "⚡ This job was triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 Running job on a ${{ runner.os }} server."

      - name: Check out repository code
        uses: actions/checkout@v3

      - run: echo "🐑 The ${{ github.repository }} repository has been cloned to the runner."
      - run: echo "🚀 The workflow is now ready to start."

      - name: Merge into release/production (hotfix)
        if: ${{ (github.event.pull_request.merged == true) && (contains(github.event.pull_request.labels.*.name, 'hotfix')) }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: "release/production"
          PULL_REQUEST_TITLE: "chore: merge release/pre-production into release/production"
          PULL_REQUEST_BODY: This PR was automatically created because another PR with the label 'hotfix' was merged into branch 'release/pre-production'. DO NOT SQUASH and merge this PR.

      - name: Merge into release/production (cherry)
        if: ${{ (github.event.pull_request.merged == true) && (contains(github.event.pull_request.labels.*.name, 'cherry')) }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: "release/production"
          PULL_REQUEST_TITLE: "chore: merge release/pre-production into release/production"
          PULL_REQUEST_BODY: This PR was automatically created because another PR with the label 'cherry' was merged into branch 'release/pre-production'. DO NOT SQUASH and merge this PR.

      - name: Merge into main (hotfix)
        if: ${{ (github.event.pull_request.merged == true) && (contains(github.event.pull_request.labels.*.name, 'hotfix')) }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: "main"
          PULL_REQUEST_TITLE: "chore: merge release/pre-production into main"
          PULL_REQUEST_BODY: This PR was automatically created because another PR with the label 'hotfix' was merged into branch 'release/pre-production'. DO NOT SQUASH and merge this PR.

      - name: Merge into main (cherry)
        if: ${{ (github.event.pull_request.merged == true) && (contains(github.event.pull_request.labels.*.name, 'cherry')) }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: "main"
          PULL_REQUEST_TITLE: "chore: merge release/pre-production into main"
          PULL_REQUEST_BODY: This PR was automatically created because another PR with the label 'cherry' was merged into branch 'release/pre-production'. DO NOT SQUASH and merge this PR.

      - name: Merge into main (post-cutoff)
        if: ${{ (github.event.pull_request.merged == true) && (contains(github.event.pull_request.labels.*.name, 'post-cutoff')) }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/qa"
          TO_BRANCH: "main"
          PULL_REQUEST_TITLE: "chore: merge release/qa into main"
          PULL_REQUEST_BODY: This PR was automatically created because another PR with the label 'post-cutoff' was merged into branch 'release/qa'. DO NOT SQUASH and merge this PR.
      - run: echo "⏹ End of job. Status - ${{ job.status }}."
