name: AUTOMATIC - Update Widget Branches

on:
  push:
    branches:
      - release/development
      - release/qa
      - release/production
      - release/ril-production

jobs:
  # Merge release/development into widget branches
  merge-development:
    if: github.ref == 'refs/heads/release/development'
    strategy:
      matrix:
        widget: [dashboard, mapsnapshot]
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: release/development
      target_branch: release/${{ matrix.widget }}/development
      commit_message: ":runner: release/development → release/${{ matrix.widget }}/development"

  # Merge release/qa into widget branches
  merge-qa:
    if: github.ref == 'refs/heads/release/qa'
    strategy:
      matrix:
        widget: [dashboard, mapsnapshot]
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: release/qa
      target_branch: release/${{ matrix.widget }}/qa
      commit_message: ":runner: release/qa → release/${{ matrix.widget }}/qa"

  # Merge release/production into widget branches
  merge-production:
    if: github.ref == 'refs/heads/release/production'
    strategy:
      matrix:
        widget: [dashboard, mapsnapshot]
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: release/production
      target_branch: release/${{ matrix.widget }}/production
      commit_message: ":runner: release/production → release/${{ matrix.widget }}/production"

  # Merge release/ril-production into widget branches
  merge-ril-production:
    if: github.ref == 'refs/heads/release/ril-production'
    strategy:
      matrix:
        widget: [dashboard, terra]  # NOTE: old branch; excludes mapsnapshot
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: release/ril-production
      target_branch: widget/${{ matrix.widget }}-ril
      commit_message: ":runner: release/ril-production → widget/${{ matrix.widget }}-ril"
