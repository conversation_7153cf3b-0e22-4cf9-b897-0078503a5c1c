name: ">> Merge"

on:
  workflow_dispatch:
    inputs:
      source_branch:
        type: choice
        description: Source branch
        required: true
        default: main
        options:
          - main
          - release/development
          - release/qa
          - release/pre-production
          - release/production
          - release/ril-production
      target_branch:
        type: choice
        description: Target branch
        required: true
        default: release/development
        options:
          - release/development
          - release/qa
          - release/pre-production
          - release/production
          - release/ril-production

jobs:
  merge:
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: ${{ github.event.inputs.source_branch }}
      target_branch: ${{ github.event.inputs.target_branch }}
      commit_message: ":runner: ${{ github.event.inputs.source_branch }} → ${{ github.event.inputs.target_branch }}"
