name: SCHEDULED - Dev Merge

on:
  schedule:
    - cron: "30 5-13/1 * * 1-5"
    # Every hour from 11 am through 7 pm IST on every day-of-week from Monday through Friday

jobs:
  merge-to-development:
    uses: sensehawk/workflows/.github/workflows/merge.yml@main
    secrets: inherit
    with:
      source_branch: 'main'
      target_branch: 'release/development'

  notify:
    needs: [merge-to-development]
    runs-on: ubuntu-latest
    steps:
      - run: echo "⏹ End of job. Status - ${{ job.status }}."
