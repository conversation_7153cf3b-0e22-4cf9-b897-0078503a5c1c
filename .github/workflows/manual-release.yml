name: ">> Release (PR)"

on:
  workflow_dispatch:
    inputs:
      sprint:
        description: Sprint number
      folder:
        description: Folder number
      target:
        type: choice
        description: Target branch
        options:
          - release/pre-production
          - release/production
          - release/ril-production

jobs:
  autopr:
    if: ${{ (github.actor == 'sh-ravan') || (github.actor == 'kdyadav') || (github.actor == 'Anil-Pujeri') || (github.actor == 'saideeptalari') }}
    runs-on: ubuntu-latest

    steps:
      - run: echo "⚡ This job was manually triggered by ${{ github.actor }}."
      - run: echo "🐧 Running job on a ${{ runner.os }} server."

      - name: Check out repository code
        uses: actions/checkout@v3

      - run: echo "🐑 The ${{ github.repository }} repository has been cloned to the runner."
      - run: echo "🚀 The workflow is now ready to start."

      - name: Create PR to release/pre-production
        if: ${{ github.event.inputs.target == 'release/pre-production' }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/qa"
          TO_BRANCH: ${{ github.event.inputs.target }}
          PULL_REQUEST_TITLE: "chore: merge release/qa into release/pre-production"
          PULL_REQUEST_BODY: "This PR will merge `release/qa` into `release/pre-production`. DO NOT SQUASH and merge this PR."

      - name: Create PR to release/production
        if: ${{ github.event.inputs.target == 'release/production' && github.event.inputs.sprint == '' }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: ${{ github.event.inputs.target }}
          PULL_REQUEST_TITLE: "chore: merge release/pre-production into release/production"
          PULL_REQUEST_BODY: "This PR will merge `release/pre-production` into `release/production`. DO NOT SQUASH and merge this PR."

      - name: Create release PR to release/production
        if: ${{ github.event.inputs.target == 'release/production' && github.event.inputs.sprint != '' }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/pre-production"
          TO_BRANCH: ${{ github.event.inputs.target }}
          PULL_REQUEST_TITLE: "chore(release): v${{ github.event.inputs.folder }}.${{ github.event.inputs.sprint }}.0 [Sprint ${{ github.event.inputs.sprint }} Deployment]"
          PULL_REQUEST_BODY: "This PR will merge `release/pre-production` into `release/production`. DO NOT SQUASH and merge this PR."

      - name: Create PR to release/ril-production
        if: ${{ github.event.inputs.target == 'release/ril-production' }}
        uses: tretuna/sync-branches@1.4.0
        with:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          FROM_BRANCH: "release/production"
          TO_BRANCH: ${{ github.event.inputs.target }}
          PULL_REQUEST_TITLE: "chore(release): v${{ github.event.inputs.folder }}.${{ github.event.inputs.sprint }}.0 [Sprint ${{ github.event.inputs.sprint }} RIL Deployment]"
          PULL_REQUEST_BODY: "This PR will merge `release/production` into `release/ril-production`. DO NOT SQUASH and merge this PR."

      - run: echo "⏹ End of job. Status - ${{ job.status }}."
