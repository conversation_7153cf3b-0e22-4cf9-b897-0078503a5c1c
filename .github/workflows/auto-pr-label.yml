name: AUTOMATIC - Label PRs

on:
  pull_request:
    branches:
      - release/qa
      - release/pre-production
    types: [opened, reopened, synchronize, edited]

jobs:
  labeller:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
      - run: echo "⚡ This job was triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 Running job on a ${{ runner.os }} server."

      - name: Check if PR is targeting release/qa
        id: check_qa
        run: |
          if [[ $GITHUB_BASE_REF == 'release/qa' ]]; then
            echo "is_qa_branch=true" >> $GITHUB_OUTPUT
          fi

      - name: Add label and comment
        if: steps.check_qa.outputs.is_qa_branch == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ['post-cutoff']
            });

            const comments = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });

            const hasComment = comments.data.some(comment => 
            comment.body.includes("Destination branch `release/qa` detected.")
            );

            if (!hasComment) {
              const commentText = "Destination branch `release/qa` detected. The **`post-cutoff`** label has been added automatically. When this PR is merged with this label present, another PR to merge `release/qa` into `main` will be opened automatically.";
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentText
              });
            }

      - name: Check if PR is of hotfix type
        id: check
        run: |
          if [[ $GITHUB_HEAD_REF == hotfix* ]] && [[ $GITHUB_BASE_REF == 'release/pre-production' ]]; then
            echo "hotfix=true" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_HEAD_REF == cherry* ]] && [[ $GITHUB_BASE_REF == 'release/pre-production' ]]; then
            echo "cherry=true" >> $GITHUB_OUTPUT
          fi

      - name: Add the hotfix label
        if: steps.check.outputs.hotfix == 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['hotfix']
            })

      - name: Add the cherry label
        if: steps.check.outputs.cherry == 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['cherry']
            })

      - run: echo "⏹ End of job. Status - ${{ job.status }}."
